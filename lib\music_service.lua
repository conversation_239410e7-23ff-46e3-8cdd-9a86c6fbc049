-- 音乐服务模块 - 处理音乐相关的业务逻辑
MusicService = {}

-- 默认平台
MusicService.DEFAULT_PLATFORM = "NETEASE"

-- 调试开关
local DEBUG = false

-- 调试日志函数
local function debugPrint(...)
    if DEBUG then
        print("^3[音乐服务调试]^7", ...)
    end
end

-- 切换调试模式
function MusicService.toggleDebug()
    DEBUG = not DEBUG
    print("^3[音乐服务] ^7调试模式: " .. (DEBUG and "已启用" or "已禁用"))
    return DEBUG
end

-- 搜索音乐
function MusicService.searchMusic(platform, keyword, cb, limit)
    -- 默认使用网易云音乐，但优先使用传入的平台
    platform = platform or MusicService.DEFAULT_PLATFORM
    -- 记录实际使用的平台
    -- print("^3[音乐服务] 使用平台: " .. platform)
    debugPrint("搜索请求 - 关键词: " .. keyword .. ", 平台: " .. platform .. ", 最大结果数: " .. (limit or 20))
    -- 默认最大结果数量为20
    limit = limit or 20
    
    -- 生成缓存键
    local cache_params = {
        action = "search",
        platform = platform:upper(),
        keyword = keyword,
        limit = limit
    }
    local cache_key = Cache.generateKey(cache_params)
    debugPrint("生成缓存键: " .. cache_key)
    
    -- 首先尝试从数据库缓存获取
    if Database then
        Database.getSearchResults(platform:upper(), keyword, function(dbResults)
            if dbResults then
                print("^2[音乐服务] 从数据库缓存获取搜索结果: " .. keyword)
                if cb then
                    cb(dbResults)
                end
                return
            else
                -- 数据库中没有结果，尝试从API获取
                MusicService.searchFromAPI(platform, keyword, cb, limit)
            end
        end, limit)
    else
        -- 如果数据库模块不可用，则尝试从内存缓存获取
    local cached_result = Cache.get(cache_key)
    if cached_result then
            print("^2[音乐服务] 从内存缓存获取搜索结果: " .. keyword)
        if cb then
            cb(cached_result)
        end
        return
        else
            -- 内存缓存中没有结果，尝试从API获取
            MusicService.searchFromAPI(platform, keyword, cb, limit)
        end
    end
    end
    
-- 从API搜索音乐
function MusicService.searchFromAPI(platform, keyword, cb, limit)
    -- 从API获取
    API.searchMusic(platform, keyword, function(response, statusCode)
        -- print("^3[音乐服务] 收到API响应，状态码: " .. tostring(statusCode))
        
        if response and statusCode == 200 then
            -- 检查响应类型
            if type(response) == "table" then
                -- print("^2[音乐服务] 搜索成功，找到 " .. #response .. " 条结果")
                
                -- 为每个歌曲添加平台信息和确保ID存在
                for i, song in ipairs(response) do
                    -- 添加平台信息
                    song.platform = platform:upper()
                    
                    -- 添加搜索关键词
                    song.search_keyword = keyword
                    
                    -- 确保每首歌有唯一id
                    if not song.id or song.id == "" then
                        -- 对于所有平台使用统一的ID生成策略（避免中文字符）
                        local idComponents = {
                            song.platform,
                            song.mid or "",
                            string.gsub(song.name or "unknown", "[^%w%-_]", ""), -- 移除特殊字符
                            string.gsub(song.singer or "unknown", "[^%w%-_]", "") -- 移除特殊字符
                        }
                        song.id = table.concat(idComponents, "_")
                        print("^3[音乐服务] 歌曲 #" .. i .. " (" .. song.name .. ") 缺少ID，已生成: " .. song.id)
                    end
                    
                    -- 确保图片URL字段一致
                    if song.pic and not song.picUrl then
                        song.picUrl = song.pic
                    elseif song.picUrl and not song.pic then
                        song.pic = song.picUrl
                    end
                    
                    -- 如果都没有，确保至少有一个空值而不是nil
                    if not song.pic then song.pic = "" end
                    if not song.picUrl then song.picUrl = "" end
                end
                
                -- 缓存结果到内存
                local cache_params = {
                    action = "search",
                    platform = platform:upper(),
                    keyword = keyword,
                    limit = limit
                }
                local cache_key = Cache.generateKey(cache_params)
                Cache.set(cache_key, response)
                
                -- 保存歌曲信息到数据库
                if Database then
                    Database.saveSongResults(response, keyword, platform:upper())
                    -- print("^2[音乐服务] 搜索结果已保存到数据库: " .. #response .. " 首歌曲")
                end
                
                -- 返回搜索结果给客户端
                if cb then
                    cb(response)
                end
            else
                -- print("^1[音乐服务] 响应不是表格类型: " .. type(response))
                if cb then
                    cb(nil)
                end
            end
        else
            -- print("^1[音乐服务] 搜索失败: " .. tostring(statusCode))
            if cb then
                cb(nil)
            end
        end
    end, limit)
end

-- 获取支持的平台列表
function MusicService.getSupportedPlatforms()
    local platforms = {}
    
    -- 从API.PLATFORMS中获取平台信息
    for code, name in pairs(API.PLATFORMS) do
        table.insert(platforms, {
            code = code,
            name = code .. "音乐" -- 为每个平台添加"音乐"后缀
        })
    end
    
    -- 特殊平台名称处理（只保留启用的平台）
    for i, platform in ipairs(platforms) do
        if platform.code == "QQ" then
            platform.name = "QQ音乐"
        elseif platform.code == "NETEASE" then
            platform.name = "网易云音乐"
        elseif platform.code == "XMLY" then
            platform.name = "喜马拉雅"
        end
    end
    
    return platforms
end

-- 获取缓存统计信息
function MusicService.getCacheStats()
    -- 内存缓存统计
    local memoryStats = Cache.getStats()
    
    -- 如果数据库模块可用，未来可以添加数据库缓存统计
    local stats = {
        memory = memoryStats,
        database = {
            available = (Database ~= nil)
        }
    }
    
    return stats
end

-- 清除缓存
function MusicService.clearCache()
    -- 清除内存缓存
    local memoryResult = Cache.clear()
    
    -- 如果数据库模块可用，未来可以添加清除数据库缓存的功能
    
    return memoryResult
end

-- 获取歌曲详情
function MusicService.getSongDetail(platform, songId, cb)
    -- 默认使用QQ音乐，但优先使用传入的平台
    platform = platform or MusicService.DEFAULT_PLATFORM
    -- 记录实际使用的平台
    print("^3[音乐服务] 获取歌曲详情，平台: " .. platform .. ", 歌曲ID: " .. songId)
    
    -- 生成缓存键
    local cache_params = {
        action = "songDetail",
        platform = platform:upper(),
        songId = songId
    }
    local cache_key = Cache.generateKey(cache_params)
    
    -- 首先尝试从内存缓存获取
    local cached_result = Cache.get(cache_key)
    if cached_result then
        print("^2[音乐服务] 从内存缓存获取歌曲详情: " .. songId)
        if cb then
            cb(cached_result)
        end
        return
    end
    
    -- 从API获取
    API.getSongDetail(platform, songId, function(response, statusCode)
        print("^3[音乐服务] 收到歌曲详情API响应，状态码: " .. tostring(statusCode))
        
        if response and statusCode == 200 then
            -- 检查响应类型
            if type(response) == "table" then
                print("^2[音乐服务] 获取歌曲详情成功: " .. (response.name or "未知歌曲"))
                
                -- 添加平台信息
                response.platform = platform:upper()
                
                -- 确保图片URL字段一致
                if response.pic and not response.picUrl then
                    response.picUrl = response.pic
                elseif response.picUrl and not response.pic then
                    response.pic = response.picUrl
                end
                
                -- 确保图片URL使用HTTPS
                if response.pic and response.pic:find("^http:") then
                    response.pic = response.pic:gsub("^http:", "https:")
                    response.picUrl = response.pic
                end
                
                -- 缓存结果到内存
                Cache.set(cache_key, response)
                
                if cb then
                    cb(response)
                end
            else
                print("^1[音乐服务] 歌曲详情响应不是表格类型: " .. type(response))
                if cb then
                    cb(nil)
                end
            end
        else
            print("^1[音乐服务] 获取歌曲详情失败: " .. tostring(statusCode))
            if cb then
                cb(nil)
            end
        end
    end)
end 