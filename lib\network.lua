-- 网络通信模块 - 处理客户端和服务器之间的通信
Network = {}

-- 初始化
function Network.init()
    -- 确保MusicService模块已加载
    if not MusicService then
        print("^1[网络] MusicService模块未加载")
        return false
    end
    
    print("^2[网络] 初始化完成")
    return true
end

-- 注册网络事件
function Network.registerEvents()
    -- 搜索音乐
    RegisterNetEvent('music:searchMusic')
    AddEventHandler('music:searchMusic', function(platform, keyword, limit)
        local source = source
        local playerName = GetPlayerName(source)
        
        print("^3[音乐播放器] ^7玩家 " .. playerName .. " 搜索音乐: " .. keyword .. " (平台: " .. platform .. ", 最大结果数: " .. (limit or 20) .. ")")
        
        -- 默认最大结果数量为20
        limit = limit or 20
        
        -- 确保平台参数有效，如果无效则使用默认平台
        if not platform or platform == "" then
            platform = MusicService.DEFAULT_PLATFORM
            print("^3[音乐播放器] ^7使用默认平台: " .. platform)
        end
        
        MusicService.searchMusic(platform, keyword, function(result)
            if result then
                if type(result) == "table" then
                    TriggerClientEvent('music:searchResult', source, result)
                    print("^2[音乐播放器] ^7搜索结果已发送: " .. #result .. " 条")
                else
                    print("^1[音乐播放器] ^7搜索结果格式错误")
                    TriggerClientEvent('music:searchResult', source, nil)
                end
            else
                print("^1[音乐播放器] ^7搜索失败: 无结果")
                TriggerClientEvent('music:searchResult', source, nil)
            end
        end, limit)
    end)
    
    -- 切换平台事件
    RegisterNetEvent('music:switchPlatform')
    AddEventHandler('music:switchPlatform', function(platform)
        local source = source
        local playerName = GetPlayerName(source)
        
        if platform and platform ~= "" then
            print("^3[音乐播放器] ^7玩家 " .. playerName .. " 切换到平台: " .. platform)
        end
    end)
    
    -- 获取支持的平台列表
    RegisterNetEvent('music:getSupportedPlatforms')
    AddEventHandler('music:getSupportedPlatforms', function()
        local source = source
        local platforms = MusicService.getSupportedPlatforms()
        
        TriggerClientEvent('music:supportedPlatforms', source, platforms)
    end)
    
    -- 获取缓存统计信息
    RegisterNetEvent('music:getCacheStats')
    AddEventHandler('music:getCacheStats', function()
        local source = source
        local stats = MusicService.getCacheStats()
        
        TriggerClientEvent('music:cacheStats', source, stats)
    end)
    
    -- 清除缓存
    RegisterNetEvent('music:clearCache')
    AddEventHandler('music:clearCache', function()
        local source = source
        local success = MusicService.clearCache()
        
        TriggerClientEvent('music:cacheCleared', source, success)
    end)
    
    print("^2[音乐播放器] ^7网络事件已注册")
end

-- 初始化网络模块
if not Network.init() then
    print("^1[音乐播放器] ^7初始化失败: 无法初始化网络模块")
    return
end

print("^2[音乐播放器] ^7网络模块已加载") 

return Network 