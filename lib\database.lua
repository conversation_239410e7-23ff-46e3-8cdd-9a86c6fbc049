-- 数据库模块 - 处理歌曲缓存和玩家歌单
Database = {}

-- 日志控制选项
local LOG_DEBUG = false     -- 详细调试日志
local LOG_INFO = false      -- 重要信息日志
local LOG_ERROR = false     -- 错误日志也禁用

-- 定义日志函数
local function logDebug(...)
    -- 完全禁用调试日志
    return
end

local function logInfo(...)
    -- 完全禁用信息日志
    return
end

local function logError(...)
    -- 完全禁用错误日志
    return
end

-- 确保数据库已连接
function Database.init()
    if not exports.oxmysql then
        logError("oxmysql未正确加载")
        return false
    end
    
    -- 创建必要的数据库表
    Database.createTables()
    
    logInfo("初始化完成")
    return true
end

-- 创建数据库表
function Database.createTables()
    -- 创建歌曲缓存表
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS music_songs (
            id VARCHAR(128) PRIMARY KEY,
            mid VARCHAR(128) DEFAULT NULL,
            platform VARCHAR(20) NOT NULL,
            name VARCHAR(128) NOT NULL,
            singer VARCHAR(128) NOT NULL,
            album VARCHAR(128) DEFAULT NULL,
            duration INT DEFAULT 0,
            cover_url TEXT DEFAULT NULL, 
            url TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_platform (platform),
            INDEX idx_name_singer (name, singer),
            INDEX idx_mid (mid)
        )
    ]], {}, function(success)
        if success then
            print("^2[数据库] 歌曲缓存表创建成功")
        else
            print("^1[数据库] 歌曲缓存表创建失败")
        end
    end)
    

    
    -- 创建搜索排序表
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS music_song_search_order (
            song_id VARCHAR(128) NOT NULL,
            search_keyword VARCHAR(128) NOT NULL,
            platform VARCHAR(20) NOT NULL,
            search_order INT NOT NULL DEFAULT 0,
            PRIMARY KEY (song_id, search_keyword, platform)
        )
    ]], {}, function(success)
        if success then
            print("^2[数据库] 搜索排序表创建成功")
        else
            print("^1[数据库] 搜索排序表创建失败")
        end
    end)
    
    -- 创建玩家表
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS music_players (
            identifier VARCHAR(60) PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ]], {}, function(success)
        if success then
            print("^2[数据库] 玩家表创建成功")
        else
            print("^1[数据库] 玩家表创建失败")
        end
    end)
    
    -- 创建歌单表
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS music_playlists (
            id INT AUTO_INCREMENT PRIMARY KEY,
            player_identifier VARCHAR(60) NOT NULL,
            name VARCHAR(50) NOT NULL,
            cover_url TEXT DEFAULT NULL,
            description VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_player_id (player_identifier)
        )
    ]], {}, function(success)
        if success then
            print("^2[数据库] 歌单表创建成功")
        else
            print("^1[数据库] 歌单表创建失败")
        end
    end)
    
    -- 创建歌单歌曲关联表
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS music_playlist_songs (
            playlist_id INT NOT NULL,
            song_id VARCHAR(128) NOT NULL,
            search_keyword VARCHAR(128) DEFAULT NULL,
            platform VARCHAR(20) DEFAULT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (playlist_id, song_id),
            INDEX idx_playlist_id (playlist_id),
            INDEX idx_song_id (song_id),
            INDEX idx_search_platform (search_keyword, platform)
        )
    ]], {}, function(success)
        if success then
            print("^2[数据库] 歌单歌曲关联表创建成功")
        else
            print("^1[数据库] 歌单歌曲关联表创建失败")
        end
    end)
    
    -- 创建收藏歌曲表
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS music_favorite_songs (
            player_identifier VARCHAR(60) NOT NULL,
            song_id VARCHAR(128) NOT NULL,
            search_keyword VARCHAR(128) DEFAULT NULL,
            platform VARCHAR(20) DEFAULT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (player_identifier, song_id),
            INDEX idx_player_id (player_identifier),
            INDEX idx_song_id (song_id),
            INDEX idx_search_platform (search_keyword, platform)
        )
    ]], {}, function(success)
        if success then
            print("^2[数据库] 收藏歌曲表创建成功")
        else
            print("^1[数据库] 收藏歌曲表创建失败")
        end
    end)
end

-- 保存搜索结果到数据库
function Database.saveSongResults(songs, keyword, platform)
    if not songs or #songs == 0 then
        return false
    end
    
    -- 确保关键词和平台参数有效
    keyword = keyword or ""
    platform = platform or "UNKNOWN"
    
    -- print("^2[数据库] 正在保存 " .. #songs .. " 首歌曲到数据库，关键词: " .. keyword .. ", 平台: " .. platform)
    
    -- 检查是否是排行榜关键词
    -- if keyword == "热歌榜" or keyword == "新歌榜" or keyword == "说唱榜" then
    --     print("^2[数据库] 检测到排行榜数据: " .. keyword)
    -- end
    
    -- 先删除相同关键词和平台的旧排序数据
    if keyword ~= "" then
        exports.oxmysql:execute('DELETE FROM music_song_search_order WHERE search_keyword = ? AND platform = ?', {
            keyword,
            platform
        })
    end
    
    -- 计数器跟踪保存成功的歌曲
    local saved = 0
    
    for index, song in ipairs(songs) do
        -- 确保歌曲有id
        if not song.id or song.id == "" then
            -- 如果没有id，生成一个唯一id
            song.id = song.platform .. "_" .. (song.mid or "") .. "_" .. song.name .. "_" .. song.singer
            -- print("^3[数据库] 歌曲缺少ID，已生成: " .. song.id)
        end
        
        -- 记录歌曲信息用于调试
        -- if index <= 3 then -- 只记录前3首，避免日志过长
        --    print("^2[数据库] 歌曲 #" .. index .. ":")
        --    print("  - ID: " .. song.id)
        --    print("  - 标题: " .. (song.name or "未知歌曲"))
        --    print("  - 歌手: " .. (song.singer or "未知歌手"))
        --    print("  - URL: " .. (song.url or "无"))
        -- end
        
        -- 检查歌曲是否已存在
        exports.oxmysql:execute('SELECT id FROM music_songs WHERE id = ?', {
            song.id
        }, function(result)
            if result and #result == 0 then
                -- 歌曲不存在，添加到数据库
                exports.oxmysql:insert('INSERT INTO music_songs (id, mid, platform, name, singer, album, duration, cover_url, url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', {
                    song.id,
                    song.mid or nil,
                    song.platform or "UNKNOWN",
                    song.name or "未知歌曲",
                    song.singer or "未知歌手",
                    song.album or "未知专辑",
                    song.duration or 0,
                    song.pic or song.picUrl or song.cover_url or "",
                    song.url or ""
                }, function(insertId)
                    if insertId then
                        saved = saved + 1
                        -- local debugMsg = "^2[数据库] 歌曲已添加到缓存: " .. song.name .. " (" .. saved .. "/" .. #songs .. ")"
                        -- if song.mid then
                        --     debugMsg = debugMsg .. " [MID: " .. song.mid .. "]"
                        -- end
                        -- print(debugMsg)
                        
                        -- 添加排序信息（包含搜索关键词和平台）
                        if keyword ~= "" then
                            exports.oxmysql:execute('INSERT INTO music_song_search_order (song_id, search_keyword, platform, search_order) VALUES (?, ?, ?, ?)', {
                                song.id,
                                keyword,
                                platform,
                                index
                            })
                        end
                    else
                        -- print("^1[数据库] 添加歌曲到缓存失败: " .. song.name)
                    end
                end)
            else
                -- 歌曲已存在，更新最后访问时间、可能缺失的图片URL和mid字段
                exports.oxmysql:execute('UPDATE music_songs SET mid = COALESCE(?, mid), last_accessed = CURRENT_TIMESTAMP, cover_url = COALESCE(?, cover_url) WHERE id = ?', {
                    song.mid or nil,
                    song.pic or song.picUrl or song.cover_url or nil,
                    song.id
                }, function(rowsChanged)
                    -- 安全处理rowsChanged，确保它是数字类型
                    local numRowsChanged = 0
                    if type(rowsChanged) == "number" then
                        numRowsChanged = rowsChanged
                    elseif type(rowsChanged) == "table" then
                        -- 如果是表格，尝试提取可能的数字字段
                        if rowsChanged.affectedRows then
                            numRowsChanged = rowsChanged.affectedRows
                        end
                    end
                    
                    if numRowsChanged > 0 then
                        saved = saved + 1
                        -- print("^2[数据库] 歌曲缓存已更新: " .. song.name .. " (" .. saved .. "/" .. #songs .. ")")
                    end
                end)
                
                -- 更新排序信息（包含搜索关键词和平台）
                if keyword ~= "" then
                    -- 确保index参数是数字或可以转换为数字
                    local order_value = 0
                    if type(index) == "number" then
                        order_value = index
                    elseif type(index) == "string" then
                        order_value = tonumber(index) or 0
                    elseif type(index) == "table" then
                        -- 如果是表，使用默认值0
                        order_value = 0
                        -- print("^1[数据库] 警告: index是表格类型，使用默认值0")
                    else
                        -- 其他类型也使用默认值
                        order_value = 0
                    end
                    
                    exports.oxmysql:execute('INSERT INTO music_song_search_order (song_id, search_keyword, platform, search_order) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE search_order = ?', {
                        song.id,
                        keyword,
                        platform,
                        order_value, -- 使用处理后的值
                        order_value  -- 使用处理后的值
                    })
                end
            end
        end)
    end
    
    return true
end

-- 从数据库获取搜索结果
function Database.getSearchResults(platform, keyword, cb, limit)
    limit = limit or 20
    
    -- 优先使用排序表进行查询，确保完全按照原始搜索顺序
    exports.oxmysql:execute([[
        SELECT s.*, IFNULL(s.cover_url, '') as cover_url, o.search_keyword 
        FROM music_songs s
        JOIN music_song_search_order o ON s.id = o.song_id
        WHERE s.platform = ? AND o.platform = ? AND o.search_keyword = ?
        ORDER BY o.search_order ASC
        LIMIT ?
    ]], {
        platform,
        platform,
        keyword,
        limit
    }, function(results)
        if results and #results > 0 then
            -- print("^2[数据库] 从精确缓存获取搜索结果: " .. #results .. "条，关键词: " .. keyword)
            
            -- 确保所有歌曲都有必要的属性
            for _, song in ipairs(results) do
                -- 更新访问时间
                exports.oxmysql:execute('UPDATE music_songs SET last_accessed = CURRENT_TIMESTAMP WHERE id = ?', {
                    song.id
                })
                
                -- 确保图片URL属性存在
                song.pic = song.cover_url
                song.picUrl = song.cover_url
                
                -- 添加其他可能缺失的API返回字段
                if not song.duration then song.duration = 0 end
                if not song.album then song.album = "未知专辑" end
                -- 确保平台信息一致，使用数据库中保存的平台
                song.source = song.platform:lower()
                -- 添加搜索关键词
                song.search_keyword = keyword
            end
            
            if cb then
                cb(results)
            end
        else
            -- 如果使用精确搜索关键词没有找到结果，不再尝试模糊匹配
            -- print("^3[数据库] 精确关键词未找到匹配结果，不进行模糊匹配")
            -- print("^3[数据库] 缓存中未找到匹配结果")
            if cb then
                cb(nil)
            end
        end
    end)
end

-- 注册玩家
function Database.registerPlayer(identifier, name)
    if not identifier or identifier == "" then
        -- print("^1[数据库] 注册玩家失败: 标识符为空")
        return false
    end
    
    exports.oxmysql:execute('INSERT INTO music_players (identifier, name) VALUES (?, ?) ON DUPLICATE KEY UPDATE name = ?, last_login = CURRENT_TIMESTAMP', {
        identifier,
        name,
        name
    }, function(success)
        if success then
            -- print("^2[数据库] 玩家注册/更新成功: " .. name)
        else
            print("^1[数据库] 玩家注册/更新失败: " .. name)
        end
    end)
    
    return true
end

-- 创建歌单
function Database.createPlaylist(identifier, name, description, cb)
    if not identifier or identifier == "" then
        print("^1[数据库] 创建歌单失败: 玩家标识符为空")
        if cb then cb(false) end
        return
    end
    
    exports.oxmysql:insert('INSERT INTO music_playlists (player_identifier, name, description) VALUES (?, ?, ?)', {
        identifier,
        name,
        description or ""
    }, function(playlistId)
        -- 安全处理playlistId，确保它是数字类型
        local numPlaylistId = 0
        if type(playlistId) == "number" then
            numPlaylistId = playlistId
        elseif type(playlistId) == "table" then
            -- 如果是表格，尝试提取可能的数字字段
            if playlistId.insertId then
                numPlaylistId = playlistId.insertId
            end
        end
        
        if playlistId and numPlaylistId > 0 then
            print("^2[数据库] 歌单创建成功: " .. name)
            if cb then cb(numPlaylistId) end
        else
            print("^1[数据库] 歌单创建失败: " .. name)
            if cb then cb(false) end
        end
    end)
end

-- 获取玩家歌单列表
function Database.getPlayerPlaylists(identifier, cb)
    if not identifier or identifier == "" then
        print("^1[数据库] 获取歌单失败: 玩家标识符为空")
        if cb then cb({}) end
        return
    end
    
    exports.oxmysql:execute('SELECT * FROM music_playlists WHERE player_identifier = ? ORDER BY updated_at DESC', {
        identifier
    }, function(results)
        if results and #results > 0 then
            --print("^2[数据库] 获取玩家歌单成功: " .. #results .. "个")
            if cb then cb(results) end
        else
            -- 禁用数据库日志
-- print("^3[数据库] 玩家没有歌单")
            if cb then cb({}) end
        end
    end)
end

-- 添加歌曲到歌单
function Database.addSongToPlaylist(playlistId, songId, searchKeyword, platform, cb)
    -- 确保参数有效
    searchKeyword = searchKeyword or nil
    platform = platform or nil
    
    exports.oxmysql:execute('INSERT INTO music_playlist_songs (playlist_id, song_id, search_keyword, platform) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE added_at = CURRENT_TIMESTAMP, search_keyword = ?, platform = ?', {
        playlistId,
        songId,
        searchKeyword,
        platform,
        searchKeyword,
        platform
    }, function(success)
        if success then
            -- 更新歌单的最后修改时间
            exports.oxmysql:execute('UPDATE music_playlists SET updated_at = CURRENT_TIMESTAMP WHERE id = ?', {
                playlistId
            })
            
            print("^2[数据库] 歌曲已添加到歌单，搜索词: " .. (searchKeyword or "无") .. ", 平台: " .. (platform or "无"))
            if cb then cb(true) end
        else
            print("^1[数据库] 添加歌曲到歌单失败")
            if cb then cb(false) end
        end
    end)
end

-- 从歌单中移除歌曲
function Database.removeSongFromPlaylist(playlistId, songId, cb)
    exports.oxmysql:execute('DELETE FROM music_playlist_songs WHERE playlist_id = ? AND song_id = ?', {
        playlistId,
        songId
    }, function(rowsChanged)
        -- 安全处理rowsChanged，确保它是数字类型
        local numRowsChanged = 0
        if type(rowsChanged) == "number" then
            numRowsChanged = rowsChanged
        elseif type(rowsChanged) == "table" then
            -- 如果是表格，尝试提取可能的数字字段
            if rowsChanged.affectedRows then
                numRowsChanged = rowsChanged.affectedRows
            end
        end
        
        if numRowsChanged > 0 then
            -- 更新歌单的最后修改时间
            exports.oxmysql:execute('UPDATE music_playlists SET updated_at = CURRENT_TIMESTAMP WHERE id = ?', {
                playlistId
            })
            
            print("^2[数据库] 歌曲已从歌单移除")
            if cb then cb(true) end
        else
            print("^1[数据库] 从歌单移除歌曲失败")
            if cb then cb(false) end
        end
    end)
end

-- 获取歌单中的歌曲
function Database.getPlaylistSongs(playlistId, cb)
    exports.oxmysql:execute([[
        SELECT s.* 
        FROM music_songs s
        JOIN music_playlist_songs ps ON s.id = ps.song_id
        WHERE ps.playlist_id = ?
        ORDER BY ps.added_at DESC
    ]], {
        playlistId
    },     function(results)
        if results and #results > 0 then
            print("^2[数据库] 获取歌单歌曲成功: " .. #results .. "首")
            
            -- 确保所有歌曲都有必要的属性
            for _, song in ipairs(results) do
                -- 确保图片URL属性存在
                song.pic = song.cover_url
                song.picUrl = song.cover_url
                
                -- 添加其他可能缺失的API返回字段
                if not song.duration then song.duration = 0 end
                if not song.album then song.album = "未知专辑" end
                -- 确保平台信息一致，使用数据库中保存的平台
                song.source = song.platform:lower()
            end
            
            if cb then cb(results) end
        else
            print("^3[数据库] 歌单没有歌曲")
            if cb then cb({}) end
        end
    end)
end

-- 收藏歌曲
function Database.addFavoriteSong(identifier, songId, searchKeyword, platform, cb)
    -- 确保参数有效
    if searchKeyword == nil or searchKeyword == "" then
        searchKeyword = "每日推荐"  -- 默认使用"每日推荐"作为搜索关键词
    end
    
    if platform == nil or platform == "" then
        platform = "NETEASE"  -- 默认使用"NETEASE"作为平台
    end
    
    -- 处理旧版本调用（无搜索关键词和平台参数）
    if type(searchKeyword) == "function" then
        cb = searchKeyword
        searchKeyword = "每日推荐"
        platform = "NETEASE"
    end
    
    print("^2[数据库] 添加收藏歌曲: ID=" .. songId .. ", 搜索词=" .. searchKeyword .. ", 平台=" .. platform)
    
    exports.oxmysql:execute('INSERT INTO music_favorite_songs (player_identifier, song_id, search_keyword, platform) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE added_at = CURRENT_TIMESTAMP, search_keyword = ?, platform = ?', {
        identifier,
        songId,
        searchKeyword,
        platform,
        searchKeyword,
        platform
    }, function(success)
        if success then
            print("^2[数据库] 歌曲已收藏，搜索词: " .. searchKeyword .. ", 平台: " .. platform)
            if cb then cb(true) end
        else
            print("^1[数据库] 收藏歌曲失败")
            if cb then cb(false) end
        end
    end)
end

-- 取消收藏歌曲
function Database.removeFavoriteSong(identifier, songId, cb)
    exports.oxmysql:execute('DELETE FROM music_favorite_songs WHERE player_identifier = ? AND song_id = ?', {
        identifier,
        songId
    }, function(rowsChanged)
        if rowsChanged > 0 then
            print("^2[数据库] 已取消收藏歌曲")
            if cb then cb(true) end
        else
            print("^1[数据库] 取消收藏歌曲失败")
            if cb then cb(false) end
        end
    end)
end

-- 获取收藏歌曲列表
function Database.getFavoriteSongs(identifier, cb)
    -- 先尝试从music_songs表获取
    exports.oxmysql:execute([[
        SELECT s.* 
        FROM music_songs s
        JOIN music_favorite_songs fs ON s.id = fs.song_id
        WHERE fs.player_identifier = ?
        ORDER BY fs.added_at DESC
    ]], {
        identifier
    }, function(results)
        -- 如果从music_songs表中找不到足够的歌曲，尝试从music_favorite_songs表直接获取
        if not results or #results == 0 then
            -- 直接从收藏表获取歌曲ID和搜索信息
            exports.oxmysql:execute([[
                SELECT fs.song_id as id, fs.search_keyword, fs.platform, fs.added_at
                FROM music_favorite_songs fs
                WHERE fs.player_identifier = ?
                ORDER BY fs.added_at DESC
            ]], {
                identifier
            }, function(favResults)
                if favResults and #favResults > 0 then
                    print("^2[数据库] 从收藏表获取歌曲成功: " .. #favResults .. "首")
                    
                    -- 尝试从每日推荐表获取更多信息
                    local songIds = {}
                    for _, song in ipairs(favResults) do
                        table.insert(songIds, song.id)
                    end
                    
                    -- 将songIds转换为逗号分隔的字符串
                    local songIdsStr = "'" .. table.concat(songIds, "','") .. "'"
                    
                    -- 从daily_recommend_songs表获取更多信息
                    exports.oxmysql:execute(string.format([[
                        SELECT * FROM daily_recommend_songs
                        WHERE song_id IN (%s)
                    ]], songIdsStr), {}, function(dailyResults)
                        local songMap = {}
                        if dailyResults and #dailyResults > 0 then
                            for _, song in ipairs(dailyResults) do
                                songMap[song.song_id] = song
                            end
                        end
                        
                        -- 合并信息
                        for _, song in ipairs(favResults) do
                            local dailySong = songMap[song.id]
                            if dailySong then
                                song.name = dailySong.name
                                song.singer = dailySong.singer
                                song.album = dailySong.album
                                song.duration = dailySong.duration
                                song.cover_url = dailySong.cover_url
                                song.url = dailySong.url
                            end
                            
                            -- 添加必要的字段
                            song.pic = song.cover_url or ""
                            song.picUrl = song.cover_url or ""
                            song.source = song.platform:lower()
                            song.name = song.name or "未知歌曲"
                            song.singer = song.singer or "未知歌手"
                            song.album = song.album or "未知专辑"
                            song.duration = song.duration or 0
                        end
                        
                        if cb then cb(favResults) end
                    end)
                else
                    -- 禁用数据库日志
-- print("^3[数据库] 没有收藏歌曲")
                    if cb then cb({}) end
                end
            end)
        else
            print("^2[数据库] 获取收藏歌曲成功: " .. #results .. "首")
            
            -- 确保所有歌曲都有必要的属性
            for _, song in ipairs(results) do
                -- 确保图片URL属性存在
                song.pic = song.cover_url
                song.picUrl = song.cover_url
                
                -- 添加其他可能缺失的API返回字段
                if not song.duration then song.duration = 0 end
                if not song.album then song.album = "未知专辑" end
                -- 确保平台信息一致，使用数据库中保存的平台
                song.source = song.platform:lower()
            end
            
            if cb then cb(results) end
        end
    end)
end

-- 检查歌曲是否已收藏
function Database.checkSongFavorite(identifier, songId, cb)
    exports.oxmysql:execute('SELECT * FROM music_favorite_songs WHERE player_identifier = ? AND song_id = ?', {
        identifier,
        songId
    }, function(result)
        local isFavorite = result and #result > 0
        if cb then cb(isFavorite) end
    end)
end

-- 直接保存单首歌曲到数据库
function Database.saveSong(songId, songData)
    if not songId or not songData then
        return false
    end
    
    -- 检查歌曲是否已存在
    exports.oxmysql:execute('SELECT id FROM music_songs WHERE id = ?', {
        songId
    }, function(result)
        if result and #result == 0 then
            -- 歌曲不存在，添加到数据库
            exports.oxmysql:insert('INSERT INTO music_songs (id, mid, platform, name, singer, album, duration, cover_url, url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', {
                songId,
                songData.mid or nil,
                songData.platform or "UNKNOWN",
                songData.name or "未知歌曲",
                songData.singer or "未知歌手",
                songData.album or "未知专辑",
                songData.duration or 0,
                songData.pic or songData.picUrl or songData.cover_url or "",
                songData.url or ""
            }, function(insertId)
                if insertId then
                    print("^2[数据库] 歌曲已直接添加到缓存: " .. songData.name)
                else
                    print("^1[数据库] 歌曲直接添加到缓存失败: " .. songData.name)
                end
            end)
        else
            -- 歌曲已存在，更新最后访问时间和mid字段（如果提供）
            if songData.mid and songData.mid ~= "" then
                exports.oxmysql:execute('UPDATE music_songs SET mid = ?, last_accessed = CURRENT_TIMESTAMP WHERE id = ?', {
                    songData.mid, songId
                })
                print("^2[数据库] 更新歌曲mid字段: " .. songId .. " -> " .. songData.mid)
            else
                exports.oxmysql:execute('UPDATE music_songs SET last_accessed = CURRENT_TIMESTAMP WHERE id = ?', {
                    songId
                })
            end
        end
    end)
    
    return true
end

-- 启动数据库模块
if not Database.init() then
    print("^1[数据库] 初始化失败")
    return
end

print("^2[数据库] 模块已加载")

return Database