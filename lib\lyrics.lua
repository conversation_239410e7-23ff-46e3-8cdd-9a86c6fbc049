-- 歌词处理模块
Lyrics = {}

-- 歌词缓存
local lyricsCache = {}
local cacheExpireTime = 3600000 -- 1小时缓存时间（毫秒）

-- 调试开关
local DEBUG = false

-- 调试日志函数
local function debugPrint(...)
    -- 完全禁用调试输出
    -- 即使DEBUG为true也不输出
    return
end

-- 设置调试模式
function Lyrics.setDebug(enabled)
    DEBUG = enabled
    -- 禁用调试输出
    -- debugPrint("调试模式已" .. (enabled and "启用" or "禁用"))
end

-- 解析LRC歌词格式
function Lyrics.parseLRC(lrcContent)
    if not lrcContent or lrcContent == "" then
        debugPrint("LRC内容为空")
        return {}
    end
    
    local lyricsArray = {}
    local metadata = {}
    
    -- 处理歌词中的\n字符，将其转换为实际的换行符
    lrcContent = lrcContent:gsub("\\n", "\n")
    
    debugPrint("开始解析LRC，内容长度: " .. string.len(lrcContent))
    
    -- 逐行解析
    for line in string.gmatch(lrcContent, "[^\r\n]+") do
        line = line:trim() -- 去除首尾空白
        
        if line ~= "" then
            -- 检查是否是元数据行（如：[ar:歌手], [ti:标题]）
            local metaKey, metaValue = string.match(line, "%[([a-z]+):(.*)%]")
            if metaKey and metaValue then
                metadata[metaKey] = metaValue
                debugPrint("解析元数据: " .. metaKey .. " = " .. metaValue)
            else
                -- 解析时间标签和歌词内容
                local time, content = string.match(line, "%[(%d+:%d+%.%d+)%](.*)")
                
                if not time then
                    -- 尝试其他时间格式 [mm:ss]
                    time, content = string.match(line, "%[(%d+:%d+)%](.*)")
                    if time then
                        time = time .. ".00" -- 补充毫秒部分
                    end
                end
                
                if not time then
                    -- 尝试 [hh:mm:ss] 格式
                    time, content = string.match(line, "%[(%d+:%d+:%d+)%](.*)")
                end
                
                if time and content then
                    local timeInMs = Lyrics.parseTimeToMs(time)
                    if timeInMs then
                        table.insert(lyricsArray, {
                            time = timeInMs,
                            content = content:trim()
                        })
                        debugPrint("解析歌词行: " .. time .. " -> " .. content:trim())
                    end
                else
                    debugPrint("无法识别的行格式: " .. line)
                end
            end
        end
    end
    
    -- 按时间顺序排序
    table.sort(lyricsArray, function(a, b)
        return a.time < b.time
    end)
    
    debugPrint("解析完成，共 " .. #lyricsArray .. " 行歌词")
    
    return {
        lyrics = lyricsArray,
        metadata = metadata
    }
end

-- 将时间字符串转换为毫秒
function Lyrics.parseTimeToMs(timeStr)
    if not timeStr then return nil end
    
    -- 格式：mm:ss.xx
    local min, sec = string.match(timeStr, "(%d+):(%d+%.%d+)")
    if min and sec then
        return (tonumber(min) * 60 + tonumber(sec)) * 1000
    end
    
    -- 格式：mm:ss
    local min, sec = string.match(timeStr, "(%d+):(%d+)")
    if min and sec then
        return (tonumber(min) * 60 + tonumber(sec)) * 1000
    end
    
    -- 格式：hh:mm:ss
    local hour, min, sec = string.match(timeStr, "(%d+):(%d+):(%d+)")
    if hour and min and sec then
        return (tonumber(hour) * 3600 + tonumber(min) * 60 + tonumber(sec)) * 1000
    end
    
    debugPrint("无法解析时间格式: " .. timeStr)
    return nil
end

-- 根据当前播放时间获取应该显示的歌词
function Lyrics.getCurrentLyric(lyricsArray, currentTimeMs)
    if not lyricsArray or #lyricsArray == 0 then
        return nil, 0
    end
    
    local currentIndex = 1
    
    -- 查找当前应该显示的歌词
    for i = 1, #lyricsArray do
        if i == #lyricsArray then
            -- 最后一行歌词
            currentIndex = i
            break
        elseif currentTimeMs >= lyricsArray[i].time and currentTimeMs < lyricsArray[i + 1].time then
            -- 在两个时间点之间
            currentIndex = i
            break
        elseif currentTimeMs < lyricsArray[i].time then
            -- 还没到第一行歌词的时间
            currentIndex = math.max(1, i - 1)
            break
        end
    end
    
    return lyricsArray[currentIndex], currentIndex
end

-- 获取下一句歌词
function Lyrics.getNextLyric(lyricsArray, currentIndex)
    if not lyricsArray or #lyricsArray == 0 or currentIndex >= #lyricsArray then
        return nil
    end
    
    return lyricsArray[currentIndex + 1]
end

-- 格式化歌词显示
function Lyrics.formatLyric(lyric, showTime)
    if not lyric then return "" end
    
    local content = lyric.content or ""
    
    if showTime and lyric.time then
        local minutes = math.floor(lyric.time / 60000)
        local seconds = math.floor((lyric.time % 60000) / 1000)
        local timeStr = string.format("[%02d:%02d]", minutes, seconds)
        return timeStr .. " " .. content
    end
    
    return content
end

-- 缓存歌词
function Lyrics.cacheLyrics(songId, lyricsData)
    if not songId or not lyricsData then return end
    
    lyricsCache[songId] = {
        data = lyricsData,
        timestamp = GetGameTimer()
    }
    
    debugPrint("缓存歌词: " .. songId)
end

-- 从缓存获取歌词
function Lyrics.getCachedLyrics(songId)
    if not songId then return nil end
    
    local cached = lyricsCache[songId]
    if not cached then
        debugPrint("缓存中未找到歌词: " .. songId)
        return nil
    end
    
    -- 检查是否过期
    local currentTime = GetGameTimer()
    if currentTime - cached.timestamp > cacheExpireTime then
        lyricsCache[songId] = nil
        debugPrint("歌词缓存已过期: " .. songId)
        return nil
    end
    
    debugPrint("从缓存获取歌词: " .. songId)
    return cached.data
end

-- 清理过期缓存
function Lyrics.cleanExpiredCache()
    local currentTime = GetGameTimer()
    local cleaned = 0
    
    for songId, cached in pairs(lyricsCache) do
        if currentTime - cached.timestamp > cacheExpireTime then
            lyricsCache[songId] = nil
            cleaned = cleaned + 1
        end
    end
    
    if cleaned > 0 then
        debugPrint("清理了 " .. cleaned .. " 个过期歌词缓存")
    end
end

-- 获取缓存统计信息
function Lyrics.getCacheStats()
    local count = 0
    local totalSize = 0
    
    for songId, cached in pairs(lyricsCache) do
        count = count + 1
        if cached.data and cached.data.lyrics then
            totalSize = totalSize + #cached.data.lyrics
        end
    end
    
    return {
        count = count,
        totalSize = totalSize,
        expireTime = cacheExpireTime
    }
end

-- 清空所有缓存
function Lyrics.clearCache()
    local count = 0
    for _ in pairs(lyricsCache) do
        count = count + 1
    end
    
    lyricsCache = {}
    debugPrint("清空了 " .. count .. " 个歌词缓存")
    return count
end

-- 字符串trim函数
if not string.trim then
    string.trim = function(s)
        return s:match("^%s*(.-)%s*$")
    end
end

-- 定期清理过期缓存
if IsDuplicityVersion() then -- 只在服务端运行
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(300000) -- 每5分钟清理一次
            Lyrics.cleanExpiredCache()
        end
    end)
end

return Lyrics