-- 缓存模块 - 缓存API请求结果以提高性能
Cache = {}

-- 缓存配置
local CACHE_CONFIG = {
    max_size = 100, -- 最大缓存条目数
    expiry_time = 3600 -- 缓存过期时间(秒)
}

-- 缓存存储
local cache_storage = {}
local cache_keys = {} -- 用于跟踪缓存顺序

-- 生成缓存键
function Cache.generateKey(params)
    local parts = {}
    
    -- 按字母顺序排序键以确保一致性
    local sorted_keys = {}
    for k in pairs(params) do
        table.insert(sorted_keys, k)
    end
    table.sort(sorted_keys)
    
    -- 构建缓存键
    for _, k in ipairs(sorted_keys) do
        table.insert(parts, k .. "=" .. tostring(params[k]))
    end
    
    return table.concat(parts, "&")
end

-- 检查键是否存在且未过期
function Cache.exists(key)
    local item = cache_storage[key]
    
    if not item then
        return false
    end
    
    -- 检查是否过期
    if os.time() - item.timestamp > (item.expiry or CACHE_CONFIG.expiry_time) then
        -- 缓存已过期，删除并返回false
        Cache.remove(key)
        return false
    end
    
    return true
end

-- 获取缓存项
function Cache.get(key)
    local item = cache_storage[key]
    
    if not item then
        return nil
    end
    
    -- 检查是否过期
    if os.time() - item.timestamp > (item.expiry or CACHE_CONFIG.expiry_time) then
        -- 缓存已过期，删除并返回nil
        Cache.remove(key)
        return nil
    end
    
    -- 更新访问时间
    item.last_accessed = os.time()
    
    return item.data
end

-- 设置缓存项
function Cache.set(key, data, expiry_time)
    -- 检查缓存是否已满
    if #cache_keys >= CACHE_CONFIG.max_size then
        -- 移除最旧的缓存项
        Cache.remove(cache_keys[1])
    end
    
    -- 添加新缓存项
    cache_storage[key] = {
        data = data,
        timestamp = os.time(),
        last_accessed = os.time(),
        expiry = expiry_time -- 可选的特定项目过期时间
    }
    
    -- 添加到键列表
    table.insert(cache_keys, key)
    
    return true
end

-- 移除缓存项
function Cache.remove(key)
    if cache_storage[key] then
        cache_storage[key] = nil
        
        -- 从键列表中移除
        for i, k in ipairs(cache_keys) do
            if k == key then
                table.remove(cache_keys, i)
                break
            end
        end
        
        return true
    end
    
    return false
end

-- 清除所有缓存
function Cache.clear()
    cache_storage = {}
    cache_keys = {}
    return true
end

-- 获取缓存统计信息
function Cache.getStats()
    return {
        total_items = #cache_keys,
        max_size = CACHE_CONFIG.max_size,
        expiry_time = CACHE_CONFIG.expiry_time
    }
end 