// 缓存资源名称，避免重复获取
let cachedResourceName = null;

// 安全获取FiveM资源名称的函数
function GetParentResourceName() {
    // 如果已经缓存了资源名称，直接返回
    if (cachedResourceName) {
        return cachedResourceName;
    }

    try {
        // 优先使用FiveM提供的函数
        if (typeof window.GetParentResourceName === 'function') {
            const resourceName = window.GetParentResourceName();
            if (resourceName && resourceName.trim() !== '') {
                cachedResourceName = resourceName;
                return resourceName;
            }
        }

        // 如果FiveM函数不可用，尝试从URL中获取资源名称
        if (window.location && window.location.href) {
            const urlMatch = window.location.href.match(/cfx-nui-([^\/]+)/);
            if (urlMatch && urlMatch[1]) {
                cachedResourceName = urlMatch[1];
                return urlMatch[1];
            }
        }

        // 最后的回退选项
        cachedResourceName = 'music_tudou';
        return 'music_tudou';
    } catch (e) {
        console.warn('获取资源名称失败，使用默认值:', e);
        cachedResourceName = 'music_tudou';
        return 'music_tudou'; // 如果不在FiveM环境中，返回默认值
    }
}

// 调试开关
const DEBUG = false;

// 调试日志函数
function debug(...args) {
    // 禁用所有调试输出
    return;
}

class MusicPlayerState {
    constructor() {
        this.currentSong = null;
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;
        this.volume = 0.7;
        this.isMuted = false;
        this.isShuffling = false;
        this.repeatMode = 'none'; // none, one, all
        this.currentPlaylist = [];
        this.currentIndex = -1;
        this.favorites = new Set();
        this.playlists = [];
        this.playHistory = [];
        this.songInfoList = []; // 存储歌曲详细信息
        this.currentTheme = 'dark';
        this.currentView = 'discover';
        this.visible = false; // 添加UI可见性状态
        
        this.loadFromStorage();
    }
    
    saveToStorage() {
        const data = {
            favorites: Array.from(this.favorites),
            playlists: this.playlists,
            playHistory: this.playHistory.slice(-50), // 只保存最近50首
            songInfoList: this.songInfoList ? this.songInfoList.slice(-50) : [], // 保存歌曲详细信息
            currentTheme: this.currentTheme,
            volume: this.volume,
            isShuffling: this.isShuffling,
            repeatMode: this.repeatMode,
            isMuted: this.isMuted // 保存静音状态
        };
        // 注意：由于不能使用localStorage，这里使用内存存储
        window.musicPlayerData = data;
        
        // 添加日志输出保存的数据内容
        debug("保存音乐播放器数据:", {
            favorites: data.favorites.length,
            playlists: data.playlists.length,
            playHistory: data.playHistory.length,
            songInfoList: data.songInfoList.length,
            currentTheme: data.currentTheme,
            volume: data.volume,
            isShuffling: data.isShuffling,
            repeatMode: data.repeatMode
        });
    }
    
    loadFromStorage() {
        if (window.musicPlayerData) {
            const data = window.musicPlayerData;
            this.favorites = new Set(data.favorites || []);
            this.playlists = data.playlists || [];
            this.playHistory = data.playHistory || [];
            this.songInfoList = data.songInfoList || []; // 加载歌曲详细信息
            this.currentTheme = data.currentTheme || 'dark';
            this.volume = data.volume || 0.7;
            this.isShuffling = data.isShuffling !== undefined ? data.isShuffling : false;
            this.repeatMode = data.repeatMode || 'none';
            this.isMuted = data.isMuted !== undefined ? data.isMuted : false; // 加载静音状态
            
            debug("加载播放器数据:", {
                favorites: this.favorites.size,
                playlists: this.playlists.length,
                playHistory: this.playHistory.length,
                songInfoList: this.songInfoList.length,
                isShuffling: this.isShuffling,
                repeatMode: this.repeatMode,
                isMuted: this.isMuted,
                volume: this.volume
            });
        }
    }
}

// ==================== 音乐播放器类 ====================
class MusicPlayer {
    constructor() {
        // 创建状态管理实例
        this.state = new MusicPlayerState();
        
        // 加载播放状态
        this.state.loadFromStorage();
        
        // 创建音频元素
        this.audio = new Audio();
        
        // 设置音频事件监听
        this.setupAudioEvents();
        
        // 初始化数据
        this.searchResults = [];
        this.dailyRecommendSongs = []; // 每日推荐歌曲
        this.rankingSongs = {
            hot: [],     // 热歌榜
            new: [],     // 新歌榜
            original: [] // 说唱榜
        };
        this.mockSongs = [];
        this.favoriteSongs = []; // 收藏的歌曲
        this.mockPlaylists = [];
        this.currentPlatform = "NETEASE"; // 默认平台
        this.supportedPlatforms = []; // 支持的平台
        this.animationFrameId = null; // 保存动画帧ID
        
        // 初始化标记
        this._currentSearchKeyword = "";
        this._playlistBeforeSearch = null;
        this._songIdBeforeSearch = null;
        this._isComponentsInitialized = false;
        this._dailyRecommendTimeout = null;
        
        this.setupUIEvents();
        this.initializeUI();
        this.generateMockData();
        this.startBannerCarousel();
        this.setupMessageListener(); // 添加消息监听器
    }
    
    setupAudioEvents() {
        // 创建音频元素
        this.audio = new Audio();
        
        // 音频事件处理
        this.audio.addEventListener('timeupdate', () => {
            // 确保音频元素有效
            if (this.audio && !isNaN(this.audio.currentTime) && this.audio.currentTime > 0) {
                // 更新当前时间
                this.state.currentTime = this.audio.currentTime;
                // 更新进度条显示
            this.updateProgressDisplay();
            }
        });
        
        this.audio.addEventListener('ended', () => {
            this.handleSongEnd();
        });
        
        this.audio.addEventListener('canplay', () => {
            // 开始播放后立即更新一次进度条
            if (this.state.isPlaying) {
                this.updateProgressDisplay();
            }
        });
        
        this.audio.addEventListener('error', (e) => {
            // 用户要求不再显示错误状态，直接跳过所有错误处理
            debug("音频加载出现问题，但按照要求不显示错误状态");
            
            // 通知服务器播放失败，让它尝试使用服务端直接播放，但不影响UI显示
            
            // 通知服务器播放失败，让它尝试使用服务端直接播放
            if (this.state.currentSong && this.state.currentSong.url) {
                fetch(`https://${GetParentResourceName()}/playServerSide`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        songId: this.state.currentSong.id,
                        url: this.state.currentSong.url,
                        title: this.state.currentSong.title,
                        artist: this.state.currentSong.artist
                    })
                }).then(response => response.json())
                .then(data => {
                    // 设置一个标记，表示已经尝试了服务端播放
                    this.state.serverPlaybackAttempted = true;
                    
                    // 添加一次检查，确保UI状态被正确更新
                    setTimeout(() => {
                        // 如果是暂停状态，不显示错误
                        if (this.state.isPaused) {
                            return;
                        }
                        
                        // 如果歌曲仍然在播放，恢复正常显示
                        if (this.state.isPlaying && this.state.currentSong) {
                            // 清除错误显示定时器
                            if (this._errorDisplayTimer) {
                                clearTimeout(this._errorDisplayTimer);
                                this._errorDisplayTimer = null;
                            }
                            
                            // 恢复正常显示
                            const updatedSong = {...this.state.currentSong};
                            delete updatedSong.isError;
                            
                            // 恢复原始标题
                            if (updatedSong.title && (
                                updatedSong.title.startsWith("加载失败:") || 
                                updatedSong.title.startsWith("播放失败:") ||
                                updatedSong.title.startsWith("直接播放失败:"))) {
                                updatedSong.title = updatedSong.title.replace(/^(加载失败:|播放失败:|直接播放失败:)\s*/, "");
                            }
                            
                            // 更新当前歌曲信息
                            this.state.currentSong = updatedSong;
                            
                            // 更新播放器显示
                            this.updatePlayerInfo(updatedSong);
                        }
                    }, 5000);
                })
                .catch(error => {
                    // 错误处理，但不显示调试信息
                });
            }
        });
        
        // 添加到setupAudioEvents方法中的事件监听器
        this.audio.addEventListener('pause', () => {
            // 设置暂停状态
            this.state.isPaused = true;
            
            // 如果当前有错误状态，暂时清除它
            if (this.state.currentSong && this.state.currentSong.isError) {
                const updatedSong = {...this.state.currentSong};
                delete updatedSong.isError;
                
                // 恢复原始标题
                if (updatedSong.title && (
                    updatedSong.title.startsWith("加载失败:") || 
                    updatedSong.title.startsWith("播放失败:") ||
                    updatedSong.title.startsWith("直接播放失败:"))) {
                    updatedSong.title = updatedSong.title.replace(/^(加载失败:|播放失败:|直接播放失败:)\s*/, "");
                }
                
                // 更新当前歌曲信息
                this.state.currentSong = updatedSong;
                
                // 更新播放器显示
                this.updatePlayerInfo(updatedSong);
            }
        });
        
        // 添加到setupAudioEvents方法中的事件监听器
        this.audio.addEventListener('play', () => {
            // 清除暂停状态
            this.state.isPaused = false;
            
            // 如果当前有错误状态，清除它
            if (this.state.currentSong && this.state.currentSong.isError) {
                const updatedSong = {...this.state.currentSong};
                delete updatedSong.isError;
                
                // 恢复原始标题
                if (updatedSong.title && (
                    updatedSong.title.startsWith("加载失败:") || 
                    updatedSong.title.startsWith("播放失败:") ||
                    updatedSong.title.startsWith("直接播放失败:"))) {
                    updatedSong.title = updatedSong.title.replace(/^(加载失败:|播放失败:|直接播放失败:)\s*/, "");
                }
                
                // 更新当前歌曲信息
                this.state.currentSong = updatedSong;
                
                // 更新播放器显示
                this.updatePlayerInfo(updatedSong);
            }
        });
    }
    
    setupUIEvents() {
        // 设置播放列表关闭按钮事件
        const closePlaylistBtn = document.querySelector('.btn-close-playlist');
        if (closePlaylistBtn) {
            // 移除可能存在的旧事件处理器
            closePlaylistBtn.replaceWith(closePlaylistBtn.cloneNode(true));
            // 重新获取元素
            const newCloseBtn = document.querySelector('.btn-close-playlist');
            // 添加新的事件处理器
            newCloseBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                debug('关闭播放列表按钮被点击');
                this.closePlaylistPanel();
            });
        }
        
        // 添加清空播放列表按钮事件
        const clearPlaylistBtn = document.querySelector('.btn-clear-playlist');
        if (clearPlaylistBtn) {
            clearPlaylistBtn.addEventListener('click', () => {
                debug('清空播放列表按钮被点击');
                this.clearPlaylist();
            });
        }
        
        // 主题切换
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const theme = btn.getAttribute('data-theme');
                this.switchTheme(theme);
            });
        });
        
        // 视图切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', () => {
                const view = item.getAttribute('data-view');
                if (view) {
                    this.switchView(view);
                }
            });
        });
        
        // 播放控制
        const playBtn = document.getElementById('playBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const shuffleBtn = document.getElementById('shuffleBtn');
        const repeatBtn = document.getElementById('repeatBtn');
        const favoriteBtn = document.getElementById('favoriteBtn');
        const playlistBtn = document.getElementById('playlistBtn');
        
        if (playBtn) {
            playBtn.addEventListener('click', () => this.togglePlay());
        }
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.playPrevious());
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.playNext());
        }
        
        if (shuffleBtn) {
            shuffleBtn.addEventListener('click', () => this.toggleShuffle());
        }
        
        if (repeatBtn) {
            repeatBtn.addEventListener('click', () => this.cycleRepeatMode());
        }
        
        if (favoriteBtn) {
            favoriteBtn.addEventListener('click', () => this.toggleFavorite());
        }
        
        if (playlistBtn) {
            playlistBtn.addEventListener('click', () => this.togglePlaylistPanel());
        }
        
        // 关闭按钮
        document.querySelectorAll('.close-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.closeUI();
            });
        });
        
        // 创建歌单按钮 - 添加这部分
        // 侧边栏的创建歌单按钮
        const sidebarCreateBtn = document.querySelector('.btn-create-playlist');
        if (sidebarCreateBtn) {
            sidebarCreateBtn.addEventListener('click', () => {
                debug('侧边栏创建歌单按钮点击');
                this.showCreatePlaylistModal();
            });
        }
        
        // 页面中的创建新歌单按钮
        const pageCreateBtn = document.querySelector('.btn-create-new-playlist');
        if (pageCreateBtn) {
            pageCreateBtn.addEventListener('click', () => {
                debug('页面创建歌单按钮点击');
                this.showCreatePlaylistModal();
            });
        }
        
        // 设置进度条和音量控制
        this.setupProgressBar();
        this.setupVolumeControl();
        
        // 设置搜索功能
        this.setupSearch();
        
        // 设置模态框
        this.setupModal();
        
        // 设置右键菜单
        this.setupContextMenu();
        
        // 设置轮播图控制
        this.setupBannerControls();
        
        // 设置排行榜标签
        this.setupRankingTabs();
    }
    
    initializeUI() {
        // 应用当前主题
        document.body.dataset.theme = this.state.currentTheme;
        
        // 设置音量和静音状态
        this.audio.volume = this.state.volume;
        this.audio.muted = this.state.isMuted;
        this.updateVolumeDisplay();
        
        // 如果是静音状态，确保服务器端也应用静音
        if (this.state.isMuted) {
            fetch(`https://${GetParentResourceName()}/setVolume`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    volume: 0
                })
            }).catch(() => {
                // 错误处理，但不输出调试信息
            });
        }
        
        // 加载收藏数量
        this.updateFavoritesCount();
        
        // 设置当前日期
        document.getElementById('currentDate').textContent = 
            new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
        // 确保UI默认隐藏
        document.getElementById('app').style.display = 'none';
    }
    
    // ==================== 主题切换 ====================
    switchTheme(theme) {
        this.state.currentTheme = theme;
        document.body.dataset.theme = theme;
        this.state.saveToStorage();
        
        // 更新主题按钮状态
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.theme === theme);
        });
    }
    
    // ==================== 视图切换 ====================
    switchView(viewName) {
        this.state.currentView = viewName;
        
        // 设置全局视图标识，用于其他函数判断当前视图
        window.currentView = viewName;
        debug("切换视图到:", viewName);
        
        // 隐藏所有视图
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });
        
        // 显示目标视图
        const targetView = document.getElementById(viewName + 'View');
        if (targetView) {
            targetView.classList.add('active');
        }
        
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.toggle('active', item.dataset.view === viewName);
        });
        
        // 加载视图数据
        this.loadViewData(viewName);
    }
    
    loadViewData(viewName) {
        switch (viewName) {
            case 'discover':
                this.loadDiscoverData();
                break;
            case 'recommend':
                this.loadRecommendData();
                break;
            case 'ranking':
                this.loadRankingData();
                break;
            case 'search':
                this.loadSearchData();
                break;
            case 'favorites':
                this.loadFavoritesData();
                break;
            case 'playlists':
                this.loadPlaylistsData();
                break;
            case 'history':
                this.loadHistoryData();
                break;
        }
    }
    
    // ==================== 数据加载 ====================
    generateMockData() {
        // 生成模拟歌曲数据 - 扩展到12首，确保有足够的随机歌曲可供选择
        
    }
    
    loadDiscoverData() {
        debug("执行loadDiscoverData方法");
        
        // 先检查是否已有 mockSongs，如果没有就先生成
        if (!this.mockSongs || this.mockSongs.length === 0) {
            this.generateMockData();
        }
        
        // 尝试从以下数据源获取歌曲：
        // 1. 数据库随机歌曲
        // 2. 当前播放列表
        // 3. 搜索结果
        // 4. 收藏歌曲
        // 5. 历史记录
        let songPromises = [];
        
        // 1. 从数据库获取随机歌曲
        const dbSongsPromise = this.getRandomDatabaseSongs()
            .then(songs => {
                debug("从数据库获取到歌曲:", songs.length, "首");
                return songs;
            })
            .catch(error => {
                console.error("获取数据库随机歌曲失败:", error);
                return [];
            });
        songPromises.push(dbSongsPromise);
        
        // 2. 获取当前播放列表中的歌曲（如果有）
        if (this.state.currentPlaylist && this.state.currentPlaylist.songs && this.state.currentPlaylist.songs.length > 0) {
                            debug("从当前播放列表获取歌曲");
            songPromises.push(Promise.resolve(this.state.currentPlaylist.songs.slice(0, 5)));
        }
        
        // 3. 获取搜索结果（如果有）
        if (this.searchResults && this.searchResults.length > 0) {
                            debug("从搜索结果获取歌曲");
            songPromises.push(Promise.resolve(this.searchResults.slice(0, 5)));
        }
        
        // 4. 获取收藏歌曲（如果有）
        if (this.favoriteSongs && this.favoriteSongs.length > 0) {
                            debug("从收藏列表获取歌曲");
            songPromises.push(Promise.resolve(this.favoriteSongs.slice(0, 5)));
        }
        
        // 5. 获取历史记录歌曲
        const historySongsPromise = Promise.resolve().then(() => {
            if (this.state.playHistory && this.state.playHistory.length > 0) {
                debug("正在处理播放历史:", this.state.playHistory.length, "首");
                // 获取历史记录中的歌曲（如果存在）
                const historySongs = this.state.playHistory
                    .map(id => {
                        // 尝试从搜索结果中查找
                        if (this.searchResults) {
                            const song = this.searchResults.find(s => s.id === id);
                            if (song) return song;
                        }
                        // 尝试从模拟数据中查找
                        return this.mockSongs.find(s => s.id === id);
                    })
                    .filter(Boolean); // 过滤掉未找到的项
                
                debug("从播放历史找到歌曲:", historySongs.length, "首");
                return historySongs;
            }
            return [];
        });
        songPromises.push(historySongsPromise);
        
        // 获取所有歌曲数据并进行合并、去重和随机展示
        Promise.all(songPromises)
            .then(songArrays => {
                // 合并所有来源的歌曲
                let allSongs = [];
                songArrays.forEach(songs => {
                    if (songs && songs.length > 0) {
                        allSongs = allSongs.concat(songs);
                    }
                });
                
                // 去重（基于ID）
                const uniqueSongs = [];
                const songIds = new Set();
                
                for (const song of allSongs) {
                    if (!songIds.has(song.id)) {
                        songIds.add(song.id);
                        uniqueSongs.push(song);
                    }
                }
                
                debug("去重后的缓存歌曲:", uniqueSongs.length, "首");
                
                // 确保一定会有模拟数据用于展示
                if (uniqueSongs.length < 10) {
                    debug("缓存歌曲不足10首，仅使用现有歌曲");
                    // 不再使用模拟数据补充
                }
                
                // 打乱顺序随机展示
                const shuffledSongs = this.shuffleArray([...uniqueSongs]);
                
                // 取前10首歌曲展示，或者全部展示（如果不足10首）
                const hotSongs = shuffledSongs.slice(0, Math.min(10, shuffledSongs.length));
                debug("随机展示的热门歌曲:", hotSongs.length, "首");
                // 打印详细信息，便于检查
                hotSongs.forEach((song, index) => {
                    debug(`歌曲${index+1}: ID=${song.id}, 标题=${song.title}, 歌手=${song.artist}`);
                });
                
                // 保存热门歌曲数据到全局变量，确保点击播放时可以找到
                // 首先将新的热门歌曲与现有歌曲合并，保留已存在的歌曲信息，避免丢失
                // 确保window.hotSongs是一个数组
                if (!window.hotSongs || !Array.isArray(window.hotSongs)) {
                    debug("初始化window.hotSongs为空数组");
                    window.hotSongs = [];
                }
                
                // 将新的热门歌曲添加到现有列表中，避免重复
                hotSongs.forEach(newSong => {
                    if (!newSong || !newSong.id) return; // 跳过无效歌曲
                    
                    // 检查歌曲是否已存在
                    const existingIndex = window.hotSongs.findIndex(s => s && s.id === newSong.id);
                    if (existingIndex >= 0) {
                        // 如果已存在，保留原有信息，但可以更新一些可能变化的字段
                        window.hotSongs[existingIndex] = {
                            ...window.hotSongs[existingIndex],
                            title: newSong.title || window.hotSongs[existingIndex].title,
                            artist: newSong.artist || window.hotSongs[existingIndex].artist,
                            cover: newSong.cover || window.hotSongs[existingIndex].cover
                        };
                    } else {
                        // 如果不存在，添加新歌曲
                        window.hotSongs.push(newSong);
                    }
                });
                
                debug("已更新热门歌曲数据到全局变量，共", window.hotSongs.length, "首");
                
                // 加载热门歌曲
                const hotSongsContainer = document.getElementById('hotSongs');
                if (!hotSongsContainer) {
                    console.error("找不到热门歌曲容器元素#hotSongs");
                    return;
                }
                
                hotSongsContainer.innerHTML = this.renderSongGrid(hotSongs);
                debug("已渲染热门歌曲到DOM");
                
                // 加载推荐歌单
                const playlistsContainer = document.getElementById('recommendPlaylists');
                if (playlistsContainer) {
                    // 从数据库获取真实歌单
                    this.getRandomDatabasePlaylists()
                        .then(playlists => {
                            if (playlists && playlists.length > 0) {
                                playlistsContainer.innerHTML = this.renderPlaylistGrid(playlists);
                            } else {
                                // 如果没有真实歌单数据，使用模拟数据
                                if (!this.mockPlaylists) this.generateMockData();
                                playlistsContainer.innerHTML = this.renderPlaylistGrid(this.mockPlaylists);
                            }
                            debug("已渲染推荐歌单到DOM");
                        })
                        .catch(error => {
                            console.error("获取歌单失败:", error);
                            if (!this.mockPlaylists) this.generateMockData();
                            playlistsContainer.innerHTML = this.renderPlaylistGrid(this.mockPlaylists);
                            debug("已渲染模拟推荐歌单到DOM");
                        });
                }
            })
            .catch(error => {
                console.error("处理歌曲数据失败:", error);
                // 失败时使用备用方法
                this.loadDiscoverDataFallback();
            });
    }
    
    // 备用方法，使用旧的逻辑加载发现页面
    loadDiscoverDataFallback() {
        debug("使用备用方法加载发现页面数据");
        // 确保先生成模拟数据，避免mockSongs未定义
        if (!this.mockSongs || this.mockSongs.length === 0) {
            debug("模拟数据不存在，先生成模拟数据");
            this.generateMockData();
        }
        
        // 从搜索结果和历史记录中获取歌曲作为缓存
        let cachedSongs = [];
        
        // 添加搜索结果
        if (this.searchResults && this.searchResults.length > 0) {
            debug("添加搜索结果到缓存:", this.searchResults.length, "首");
            cachedSongs = cachedSongs.concat(this.searchResults);
        }
        
        // 添加播放历史对应的歌曲
        if (this.state.playHistory && this.state.playHistory.length > 0) {
            debug("正在处理播放历史:", this.state.playHistory.length, "首");
            // 获取历史记录中的歌曲（如果存在）
            const historySongs = this.state.playHistory
                .map(id => {
                    // 尝试从搜索结果中查找
                    if (this.searchResults) {
                        const song = this.searchResults.find(s => s.id === id);
                        if (song) return song;
                    }
                    // 尝试从模拟数据中查找
                    return this.mockSongs.find(s => s.id === id);
                })
                .filter(Boolean); // 过滤掉未找到的项
            
            debug("从播放历史找到歌曲:", historySongs.length, "首");
            // 添加到缓存
            cachedSongs = cachedSongs.concat(historySongs);
        }
        
        // 添加收藏的歌曲
        if (this.favoriteSongs && this.favoriteSongs.length > 0) {
            debug("添加收藏歌曲到缓存:", this.favoriteSongs.length, "首");
            cachedSongs = cachedSongs.concat(this.favoriteSongs);
        }
        
        // 去重（基于ID）
        const uniqueSongs = [];
        const songIds = new Set();
        
        for (const song of cachedSongs) {
            if (!songIds.has(song.id)) {
                songIds.add(song.id);
                uniqueSongs.push(song);
            }
        }
        
        debug("去重后的缓存歌曲:", uniqueSongs.length, "首");
        
        // 确保有歌曲用于展示
        if (uniqueSongs.length < 10) {
            debug("缓存歌曲不足10首，仅使用现有歌曲");
            // 不再使用模拟数据补充
        }
        
        // 打乱顺序随机展示
        const shuffledSongs = this.shuffleArray([...uniqueSongs]);
        
        // 取前10首歌曲展示，或者全部展示（如果不足10首）
        const hotSongs = shuffledSongs.slice(0, Math.min(10, shuffledSongs.length));
        debug("随机展示的热门歌曲:", hotSongs.length, "首");
        // 打印详细信息，便于检查
        hotSongs.forEach((song, index) => {
            debug(`歌曲${index+1}: ID=${song.id}, 标题=${song.title}, 歌手=${song.artist}`);
        });
        
                 // 保存热门歌曲数据到全局变量，确保点击播放时可以找到
         // 首先将新的热门歌曲与现有歌曲合并，保留已存在的歌曲信息，避免丢失
         // 确保window.hotSongs是一个数组
         if (!window.hotSongs || !Array.isArray(window.hotSongs)) {
             debug("初始化window.hotSongs为空数组");
             window.hotSongs = [];
         }
         
         // 将新的热门歌曲添加到现有列表中，避免重复
         hotSongs.forEach(newSong => {
             if (!newSong || !newSong.id) return; // 跳过无效歌曲
             
             // 检查歌曲是否已存在
             const existingIndex = window.hotSongs.findIndex(s => s && s.id === newSong.id);
             if (existingIndex >= 0) {
                 // 如果已存在，保留原有信息，但可以更新一些可能变化的字段
                 window.hotSongs[existingIndex] = {
                     ...window.hotSongs[existingIndex],
                     title: newSong.title || window.hotSongs[existingIndex].title,
                     artist: newSong.artist || window.hotSongs[existingIndex].artist,
                     cover: newSong.cover || window.hotSongs[existingIndex].cover
                 };
             } else {
                 // 如果不存在，添加新歌曲
                 window.hotSongs.push(newSong);
             }
         });
        
        debug("已更新热门歌曲数据到全局变量，共", window.hotSongs.length, "首");
        
        // 加载热门歌曲
        const hotSongsContainer = document.getElementById('hotSongs');
        if (!hotSongsContainer) {
            console.error("找不到热门歌曲容器元素#hotSongs");
            return;
        }
        
        hotSongsContainer.innerHTML = this.renderSongGrid(hotSongs);
        debug("已渲染热门歌曲到DOM");
        
        // 加载推荐歌单
        const playlistsContainer = document.getElementById('recommendPlaylists');
        if (playlistsContainer) {
            playlistsContainer.innerHTML = this.renderPlaylistGrid(this.mockPlaylists);
            debug("已渲染推荐歌单到DOM");
        }
    }
    
    // 从数据库获取随机歌曲
    getRandomDatabaseSongs() {
        return new Promise((resolve, reject) => {
            debug("正在从数据库获取随机歌曲...");
            
            // 添加一次性消息处理函数
            const messageHandler = (event) => {
                // 检查是否是我们要的消息类型
                if (event.data && event.data.type === 'getRandomDatabaseSongs:response') {
                    debug("接收到数据库歌曲响应:", event.data);
                    
                    // 移除事件监听器，避免多次处理
                    window.removeEventListener('message', messageHandler);
                    
                    const data = event.data;
                    // 检查数据格式并处理
                    let songs = [];
                    
                    // 尝试从多种可能的数据结构中获取歌曲数组
                    if (data && typeof data === 'object') {
                        if (data.songs && Array.isArray(data.songs)) {
                            // 如果返回 {songs: [...]} 格式
                            songs = data.songs;
                        } else if (Array.isArray(data)) {
                            // 如果直接返回歌曲数组
                            songs = data;
                        } else if (data.data && Array.isArray(data.data)) {
                            // 如果返回 {data: [...]} 格式
                            songs = data.data;
                        } else if (data.result && Array.isArray(data.result)) {
                            // 如果返回 {result: [...]} 格式
                            songs = data.result;
                        } else if (data.status === 'success' && data.songs && Array.isArray(data.songs)) {
                            // 如果返回 {status: 'success', songs: [...]} 格式
                            songs = data.songs;
                        } else if (Object.prototype.toString.call(data) === '[object Object]') {
                            // 处理单个对象作为一首歌曲的情况
                            debug("检测到单个对象作为歌曲数据");
                            // 检查对象是否包含歌曲必要的属性
                            if (data.id || data.songId || data.song_id || 
                                (data.title || data.name || data.songName || data.song_name)) {
                                songs = [data]; // 将对象作为数组中的一个元素
                            } else {
                                // 尝试从对象的任何数组属性中获取
                                for (const key in data) {
                                    if (Array.isArray(data[key]) && data[key].length > 0) {
                                        songs = data[key];
                                        debug(`从数据的${key}属性中找到歌曲数组`);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    
                    if (songs.length === 0) {
                        debug("无法从响应中获取歌曲数组");
                        reject("未获取到歌曲数据");
                        return;
                    }
                    
                    debug("找到歌曲数组，条目数:", songs.length);
                    
                    // 格式化歌曲数据，确保符合前端展示要求
                    const formattedSongs = songs.map(song => {
                        // 确保song是对象
                        if (typeof song !== 'object' || song === null) {
                            console.warn("歌曲数据无效:", song);
                            return null;
                        }
                        
                        // 尝试从多种可能的属性名中获取数据
                        const id = song.id || song.songId || song.song_id || String(Date.now() + Math.floor(Math.random() * 1000));
                        const title = song.title || song.name || song.songName || song.song_name || '未知歌曲';
                        const artist = song.artist || song.singer || song.artistName || song.author || '未知歌手';
                        
                        // 处理封面图片
                        let cover = null;
                        if (song.cover) cover = song.cover;
                        else if (song.pic) cover = song.pic;
                        else if (song.image) cover = song.image;
                        else if (song.albumPic) cover = song.albumPic;
                        else if (song.picUrl) cover = song.picUrl;
                        else if (song.cover_url) cover = song.cover_url;
                        else cover = `https://via.placeholder.com/200x200/${this.getRandomColor()}/fff?text=${encodeURIComponent((title || '').charAt(0))}`;
                        
                        // 处理无效的封面URL
                        if (!cover || cover === 'null' || cover === 'undefined' || cover === '') {
                            cover = `https://via.placeholder.com/200x200/${this.getRandomColor()}/fff?text=${encodeURIComponent((title || '').charAt(0))}`;
                        }
                        
                        // 确保封面URL使用HTTPS
                        if (typeof cover === 'string' && cover.startsWith('http:')) {
                            cover = cover.replace(/^http:/, 'https:');
                        }
                        
                        // 处理歌曲URL
                        const url = song.url || song.songUrl || song.mp3 || song.mp3Url || '';
                        
                        // 处理歌曲时长
                        let duration = '3:30'; // 默认时长
                        if (song.duration) {
                            if (typeof song.duration === 'number') {
                                // 如果是秒数，转换为分:秒格式
                                const minutes = Math.floor(song.duration / 60);
                                const seconds = Math.floor(song.duration % 60);
                                duration = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                            } else if (typeof song.duration === 'string') {
                                duration = song.duration;
                            }
                        }
                        
                        return {
                            id,
                            title,
                            artist,
                            cover,
                            duration,
                            url,
                            // 保留原始数据，以便后续可能的处理
                            originalData: song
                        };
                    }).filter(Boolean); // 过滤掉null值
                    
                    if (formattedSongs.length === 0) {
                        console.warn("所有歌曲数据格式化后无效");
                        reject("歌曲数据格式无效");
                        return;
                    }
                    
                    debug("成功格式化歌曲数据:", formattedSongs.length, "首");
                    formattedSongs.forEach((song, index) => {
                        debug(`数据库歌曲${index+1}: ID=${song.id}, 标题=${song.title}, 歌手=${song.artist}`);
                    });
                    
                    resolve(formattedSongs);
                }
            };
            
            // 注册消息事件监听器
            window.addEventListener('message', messageHandler);
            
            // 发送请求获取数据库中的随机歌曲
            const resourceName = GetParentResourceName();
            debug("使用资源名称:", resourceName);

            fetch(`https://${resourceName}/triggerServerEvent`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    eventName: 'getRandomDatabaseSongs',
                    limit: 10 // 请求10首随机歌曲
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误，状态码: ${response.status}`);
                }
                // 请求成功，等待事件响应
                debug("已发送获取随机歌曲请求，等待响应...");
            })
            .catch(error => {
                console.error("发送随机数据库歌曲请求出错:", error);
                console.error("请求URL:", `https://${resourceName}/triggerServerEvent`);
                console.error("资源名称:", resourceName);
                reject(error);
            });
            
            // 设置超时，避免无限等待
            setTimeout(() => {
                reject("获取随机歌曲超时");
            }, 10000);
        });
    }
    
    // 从数据库获取随机歌单
    getRandomDatabasePlaylists() {
        return new Promise((resolve, reject) => {
            debug("正在从数据库获取随机歌单...");
            
            // 发送请求获取数据库中的随机歌单
            // 尝试使用triggerServerEvent接口，这在FiveM环境中更常见
            fetch(`https://${GetParentResourceName()}/triggerServerEvent`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    eventName: 'getRandomDatabasePlaylists',
                    limit: 4 // 请求4个随机歌单
                })
            })
            .then(response => {
                // 检查响应是否成功
                if (!response.ok) {
                    throw new Error(`HTTP错误，状态码: ${response.status}`);
                }
                
                // 尝试解析响应为JSON
                try {
                    return response.json();
                } catch (error) {
                    console.error("解析JSON失败:", error);
                    // 尝试获取原始文本
                    return response.text().then(text => {
                        debug("服务器返回的原始文本:", text);
                        throw new Error("无法解析响应为JSON");
                    });
                }
            })
            .then(data => {
                debug("数据库歌单接口返回数据:", data);
                
                // 检查数据格式并处理
                let playlists = [];
                
                // 尝试从多种可能的数据结构中获取歌单数组
                if (data && typeof data === 'object') {
                    if (Array.isArray(data)) {
                        // 如果直接返回歌单数组
                        playlists = data;
                    } else if (data.playlists && Array.isArray(data.playlists)) {
                        // 如果返回 {playlists: [...]} 格式
                        playlists = data.playlists;
                    } else if (data.data && Array.isArray(data.data)) {
                        // 如果返回 {data: [...]} 格式
                        playlists = data.data;
                    } else if (data.result && Array.isArray(data.result)) {
                        // 如果返回 {result: [...]} 格式
                        playlists = data.result;
                    } else if (data.status === 'success' && data.playlists && Array.isArray(data.playlists)) {
                        // 如果返回 {status: 'success', playlists: [...]} 格式
                        playlists = data.playlists;
                    } else {
                        // 尝试从对象的任何数组属性中获取
                        for (const key in data) {
                            if (Array.isArray(data[key]) && data[key].length > 0) {
                                playlists = data[key];
                                debug(`从数据的${key}属性中找到歌单数组`);
                                break;
                            }
                        }
                    }
                }
                
                if (playlists.length === 0) {
                    debug("无法从响应中获取歌单数组");
                    resolve([]); // 返回空数组，会使用模拟数据
                    return;
                }
                
                debug("找到歌单数组，条目数:", playlists.length);
                
                // 格式化歌单数据，确保符合前端展示要求
                const formattedPlaylists = playlists.map(playlist => {
                    // 确保playlist是对象
                    if (typeof playlist !== 'object' || playlist === null) {
                        console.warn("歌单数据无效:", playlist);
                        return null;
                    }
                    
                    // 尝试从多种可能的属性名中获取数据
                    const id = playlist.id || playlist.playlistId || playlist.playlist_id || String(Date.now() + Math.floor(Math.random() * 1000));
                    const name = playlist.name || playlist.title || playlist.playlistName || playlist.playlist_name || '未命名歌单';
                    
                    // 获取歌曲数量
                    let count = 0;
                    if (playlist.count !== undefined) count = playlist.count;
                    else if (playlist.songCount !== undefined) count = playlist.songCount;
                    else if (playlist.song_count !== undefined) count = playlist.song_count;
                    else if (playlist.trackCount !== undefined) count = playlist.trackCount;
                    else if (playlist.songs && Array.isArray(playlist.songs)) count = playlist.songs.length;
                    
                    // 处理封面图片
                    let cover = null;
                    if (playlist.cover) cover = playlist.cover;
                    else if (playlist.pic) cover = playlist.pic;
                    else if (playlist.image) cover = playlist.image;
                    else if (playlist.coverImgUrl) cover = playlist.coverImgUrl;
                    else if (playlist.picUrl) cover = playlist.picUrl;
                    else cover = `https://via.placeholder.com/180x180/${this.getRandomColor()}/fff?text=${encodeURIComponent((name || '').charAt(0))}`;
                    
                    // 处理无效的封面URL
                    if (!cover || cover === 'null' || cover === 'undefined' || cover === '') {
                        cover = `https://via.placeholder.com/180x180/${this.getRandomColor()}/fff?text=${encodeURIComponent((name || '').charAt(0))}`;
                    }
                    
                    // 确保封面URL使用HTTPS
                    if (typeof cover === 'string' && cover.startsWith('http:')) {
                        cover = cover.replace(/^http:/, 'https:');
                    }
                    
                    return {
                        id,
                        name,
                        count,
                        cover,
                        // 保留原始数据，以便后续可能的处理
                        originalData: playlist
                    };
                }).filter(Boolean); // 过滤掉null值
                
                if (formattedPlaylists.length === 0) {
                    console.warn("所有歌单数据格式化后无效");
                    resolve([]); // 返回空数组，会使用模拟数据
                    return;
                }
                
                debug("成功格式化歌单数据:", formattedPlaylists.length, "个");
                formattedPlaylists.forEach((playlist, index) => {
                    debug(`数据库歌单${index+1}: ID=${playlist.id}, 名称=${playlist.name}, 歌曲数=${playlist.count}`);
                });
                
                resolve(formattedPlaylists);
            })
            .catch(error => {
                console.error("获取随机数据库歌单出错:", error);
                resolve([]); // 返回空数组，会使用模拟数据
            });
        });
    }
    
    // 生成随机颜色代码，用于占位图
    getRandomColor() {
        const colors = [
            'ff6b6b', '4ecdc4', '45b7d1', 'f39c12', 
            '9b59b6', 'e74c3c', '27ae60', '8e44ad',
            'd35400', '2c3e50', '16a085', 'c0392b'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    // 打乱数组顺序的辅助函数
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }
    
    loadRecommendData() {
        // 更新当前日期
        document.getElementById('currentDate').textContent = 
            new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
        // 显示加载状态
        const container = document.getElementById('dailyRecommendList');
        container.innerHTML = this.renderEmptyState('spinner', '加载中...', '正在加载每日推荐歌曲...');
        
        // 从服务器获取每日推荐歌曲
        fetch(`https://${GetParentResourceName()}/triggerServerEvent`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                eventName: 'getDailyRecommend'
            })
        })
        .then(response => response.json())
        .then(data => {
            // 清除超时定时器
            if (this._dailyRecommendTimeout) {
                clearTimeout(this._dailyRecommendTimeout);
                this._dailyRecommendTimeout = null;
            }
            
            if (data && data.status === 'success' && data.songs && data.songs.length > 0) {
                debug('从服务器获取到每日推荐歌曲:', data.songs.length, '首');
                
                // 预处理每日推荐歌曲数据，确保字段名称正确
                const processedSongs = data.songs.map(song => {
                    // 确保ID字段是字符串类型
                    if (typeof song.id === 'number') {
                        song.id = String(song.id);
                    }
                    
                    return {
                        ...song,
                        // 确保标题和歌手字段正确
                        title: song.title || song.name || "未知歌曲",
                        artist: song.artist || song.singer || "未知歌手",
                        // 确保有搜索关键词和平台信息
                        search_keyword: song.search_keyword || "每日推荐",
                        platform: song.platform || "NETEASE"
                    };
                });
                
                // 保存处理后的数据
                window.dailyRecommendSongs = processedSongs;
                this.dailyRecommendSongs = processedSongs;
                
                // 打印前3首歌曲的ID，帮助调试
                processedSongs.slice(0, 3).forEach((song, i) => {
                    debug(`预处理后的每日推荐歌曲 #${i+1} ID:`, song.id, "类型:", typeof song.id, "标题:", song.title);
                });
                
                // 更新UI
                container.innerHTML = this.renderSongList(data.songs);
            } else {
                debug('没有每日推荐歌曲或获取失败');
                // 使用模拟数据作为备选
                window.dailyRecommendSongs = [...this.mockSongs];
                container.innerHTML = this.renderSongList(this.mockSongs);
            }
        })
        .catch(error => {
            console.error('请求每日推荐失败:', error);
            container.innerHTML = this.renderEmptyState('exclamation-triangle', '获取失败', '无法连接到音乐服务');
            
            // 使用模拟数据作为备选
            setTimeout(() => {
                // 直接使用模拟数据，不做预处理
                window.dailyRecommendSongs = [...this.mockSongs];
                container.innerHTML = this.renderSongList(this.mockSongs);
            }, 1000);
        });
        
        // 设置超时，如果10秒内没有收到响应，则使用模拟数据
        this._dailyRecommendTimeout = setTimeout(() => {
            if (container.querySelector('.empty-state')) {
                debug('每日推荐请求超时，使用模拟数据');
                
                // 直接使用模拟数据，不做预处理
                window.dailyRecommendSongs = [...this.mockSongs];
                container.innerHTML = this.renderSongList(this.mockSongs);
                
                // 立即更新所有可能包含这些歌曲的播放列表
                this.updateCurrentPlaylist();
            }
        }, 10000);
    }
    
    loadRankingData() {
        // 默认加载热歌榜
        const container = document.getElementById('rankingList');
        document.querySelectorAll('.ranking-tab').forEach(tab => {
            if (tab.dataset.ranking === 'hot') {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
        
        // 确保热门歌曲数据被加载
        if (!window.hotSongs || window.hotSongs.length === 0) {
            debug("初始化热门歌曲数据");
            // 如果rankingSongs中已有hot数据，直接使用
            if (this.rankingSongs && this.rankingSongs.hot && this.rankingSongs.hot.length > 0) {
                window.hotSongs = this.rankingSongs.hot;
                debug("从缓存加载热门歌曲数据，共", window.hotSongs.length, "首");
            }
        }
        
        this.loadRankingByType('hot');
    }
    
    loadSearchData() {
        // 搜索视图不需要预加载数据
        // 数据会在用户搜索时通过handleSearchResult方法加载
        const container = document.getElementById('searchResults');
        if (!container.innerHTML.trim()) {
            container.innerHTML = this.renderEmptyState('search', '开始搜索', '输入关键词搜索你喜欢的音乐');
        }
    }
    
    loadFavoritesData() {
        const container = document.getElementById('favoritesList');
        container.innerHTML = this.renderEmptyState('spinner', '加载中...', '正在加载收藏的歌曲...');
        
        // 从服务器获取收藏歌曲列表
        const resourceName = GetParentResourceName();
        debug("获取收藏歌曲 - 使用资源名称:", resourceName);

        fetch(`https://${resourceName}/getFavoriteSongs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.songs && data.songs.length > 0) {
                debug('从服务器获取到收藏歌曲:', data.songs.length, '首');
                
                // 更新本地收藏状态，确保favorites是一个Set对象
                this.state.favorites = new Set(data.songs.map(song => song.id));
                
                // 处理歌曲时长，确保每首歌曲都有正确的时长格式
                const processedSongs = data.songs.map(song => {
                    // 确保时长是秒数
                    if (song.duration && typeof song.duration === 'string' && song.duration.includes(':')) {
                        song.maxDuration = this.parseTimeString(song.duration);
                        song.durationInSeconds = song.maxDuration;
                        debug("将收藏歌曲时间字符串转换为秒数:", song.duration, "->", song.maxDuration);
                    } else if (typeof song.duration === 'number') {
                        song.maxDuration = song.duration;
                        song.durationInSeconds = song.duration;
                    } else {
                        // 默认设置3分钟
                        song.maxDuration = 180;
                        song.durationInSeconds = 180;
                    }
                    return song;
                });
                
                // 保存收藏歌曲数据，以便在播放时使用
                this.favoriteSongs = processedSongs;
                this.state.saveToStorage();
                
                // 更新UI
                container.innerHTML = this.renderSongList(processedSongs);
                this.updateFavoritesCount();
            } else {
                debug('没有收藏的歌曲或获取失败');
                this.favoriteSongs = [];
                container.innerHTML = this.renderEmptyState('star', '暂无收藏', '快去收藏你喜欢的音乐吧');
            }
        })
        .catch(error => {
            console.error('获取收藏歌曲失败:', error);
            console.error("请求URL:", `https://${resourceName}/getFavoriteSongs`);
            console.error("资源名称:", resourceName);
            container.innerHTML = this.renderEmptyState('exclamation-triangle', '加载失败', '无法从服务器获取收藏歌曲');

            // 回退到本地数据
            this.loadLocalFavorites();
        });
    }
    
    // 从本地加载收藏数据的备用方法
    loadLocalFavorites() {
        const container = document.getElementById('favoritesList');
        
        // 确保favorites是一个Set对象
        if (!(this.state.favorites instanceof Set)) {
            this.state.favorites = new Set(Array.isArray(this.state.favorites) ? this.state.favorites : []);
        }
        
        let favoriteSongs = this.mockSongs.filter(song => 
            this.state.favorites.has(song.id)
        );
        
        // 处理歌曲时长，确保每首歌曲都有正确的时长格式
        favoriteSongs = favoriteSongs.map(song => {
            // 确保时长是秒数
            if (song.duration && typeof song.duration === 'string' && song.duration.includes(':')) {
                song.maxDuration = this.parseTimeString(song.duration);
                song.durationInSeconds = song.maxDuration;
                debug("将本地收藏歌曲时间字符串转换为秒数:", song.duration, "->", song.maxDuration);
            } else if (typeof song.duration === 'number') {
                song.maxDuration = song.duration;
                song.durationInSeconds = song.duration;
            } else {
                // 默认设置3分钟
                song.maxDuration = 180;
                song.durationInSeconds = 180;
            }
            return song;
        });
        
        // 保存收藏歌曲数据，以便在播放时使用
        this.favoriteSongs = favoriteSongs;
        
        if (favoriteSongs.length === 0) {
            container.innerHTML = this.renderEmptyState('star', '暂无收藏', '快去收藏你喜欢的音乐吧');
        } else {
            container.innerHTML = this.renderSongList(favoriteSongs);
        }
    }
    
    loadPlaylistsData() {
        const container = document.getElementById('userPlaylistsGrid');
        
        if (!container) return;
        
        if (this.state.playlists.length === 0) {
            container.innerHTML = this.renderEmptyState(
                'fas fa-music',
                '暂无歌单',
                '点击"创建新歌单"按钮创建您的第一个歌单'
            );
            return;
        }
        
        container.innerHTML = this.state.playlists.map(playlist => `
            <div class="playlist-card" data-playlist-id="${playlist.id}">
                <div class="playlist-cover">
                    <img src="${playlist.cover}" alt="${this.escapeHtml(playlist.name)}">
                    <div class="playlist-actions">
                        <button class="btn-play-playlist" data-playlist-id="${playlist.id}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="playlist-info">
                    <h4>${this.escapeHtml(playlist.name)}</h4>
                    <p>${playlist.songCount !== undefined ? playlist.songCount : playlist.songs.length} 首歌曲</p>
                </div>
            </div>
        `).join('');
        
        // 添加点击事件
        document.querySelectorAll('.btn-play-playlist').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const playlistId = btn.dataset.playlistId;
                
                // 加载歌单歌曲
                this.loadPlaylistSongs(playlistId);
            });
        });
        
        document.querySelectorAll('.playlist-card').forEach(card => {
            card.addEventListener('click', () => {
                const playlistId = card.dataset.playlistId;
                
                // 加载歌单歌曲
                this.loadPlaylistSongs(playlistId);
            });
        });
    }
    
    loadHistoryData() {
        const container = document.getElementById('historyList');
        if (this.state.playHistory.length === 0) {
            container.innerHTML = this.renderEmptyState('history', '暂无播放记录', '开始播放音乐后这里会显示历史记录');
        } else {
            // 优先使用存储的详细信息列表
            let historySongs = [];
            
            if (this.state.songInfoList && this.state.songInfoList.length > 0) {
                debug("使用保存的歌曲详细信息数据渲染历史记录:", this.state.songInfoList.length, "首");
                historySongs = this.state.songInfoList.map(song => ({...song}));
            } else {
                // 后备方案：从多个数据源查找歌曲信息
                debug("未找到保存的歌曲详情，从数据源查找:", this.state.playHistory.length, "首");
                historySongs = this.state.playHistory.map(id => {
                    // 尝试多种格式的ID匹配
                    const numericId = typeof id === 'string' ? parseInt(id) : id;
                    const stringId = String(id);
                    
                    // 1. 首先尝试从mockSongs中查找
                    let song = this.mockSongs.find(s => 
                        s.id === id || s.id === numericId || s.id === stringId
                    );
                    
                    // 2. 尝试从热门歌曲中查找
                    if (!song && window.hotSongs && window.hotSongs.length > 0) {
                        song = window.hotSongs.find(s => 
                            s.id === id || s.id === numericId || s.id === stringId
                        );
                    }
                    
                    // 3. 尝试从搜索结果中查找
                    if (!song && this.searchResults && this.searchResults.length > 0) {
                        song = this.searchResults.find(s => 
                            s.id === id || s.id === numericId || s.id === stringId
                        );
                    }
                    
                    // 4. 尝试从收藏歌曲中查找
                    if (!song && this.favoriteSongs && this.favoriteSongs.length > 0) {
                        song = this.favoriteSongs.find(s => 
                            s.id === id || s.id === numericId || s.id === stringId
                        );
                    }
                    
                    // 5. 如果都找不到，创建一个简单的歌曲对象
                    if (!song) {
                        debug("在历史记录中找不到歌曲详情，创建占位: ID=", id);
                        song = {
                            id: id,
                            title: `歌曲 ${typeof id === 'string' ? id.substring(0, 8) : id}`,
                            artist: '未知歌手',
                            duration: '3:00',
                            cover: `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${id}`
                        };
                    }
                    
                    return song;
                }).filter(Boolean);
            }
            
            // 处理歌曲时长，确保每首歌曲都有正确的时长格式
            historySongs = historySongs.map(song => {
                // 确保时长是秒数
                if (song.duration && typeof song.duration === 'string' && song.duration.includes(':')) {
                    song.maxDuration = this.parseTimeString(song.duration);
                    song.durationInSeconds = song.maxDuration;
                    debug("将历史记录歌曲时间字符串转换为秒数:", song.duration, "->", song.maxDuration);
                } else if (typeof song.duration === 'number') {
                    song.maxDuration = song.duration;
                    song.durationInSeconds = song.duration;
                } else {
                    // 默认设置3分钟
                    song.maxDuration = 180;
                    song.durationInSeconds = 180;
                }
                return song;
            });
            
            debug("历史记录歌曲数:", historySongs.length, "首");
            historySongs.forEach((song, index) => {
                debug(`历史记录歌曲[${index+1}]: ${song.title} - ${song.artist}`);
            });
            
            // 保存到全局变量，以便播放时使用
            window.historySongs = historySongs;
            
            container.innerHTML = this.renderSongList(historySongs);
        }
    }
    
    // ==================== 渲染函数 ====================
    renderSongGrid(songs) {
        debug("renderSongGrid被调用，渲染歌曲数:", songs.length);
        return songs.map(song => {
            // 确保歌曲标题和艺术家名称不含有可能导致HTML解析错误的字符
            const safeTitle = this.escapeHtml(song.title || '未知歌曲');
            const safeArtist = this.escapeHtml(song.artist || '未知歌手');
            
            // 处理封面图片
            let cover = song.cover || song.pic || song.picUrl || 'https://via.placeholder.com/60';
            // 处理无效的图片URL
            if (!cover || cover === 'null' || cover === 'undefined' || cover === '') {
                cover = `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${encodeURIComponent((song.title || '').charAt(0))}`;
            }
            // 确保HTTPS
            const safeCover = typeof cover === 'string' && cover.startsWith('http:') ? cover.replace(/^http:/, 'https:') : cover;
            
            // 确保ID是安全的
            const safeId = typeof song.id === 'string' ? `'${song.id.replace(/'/g, "\\'")}'` : song.id;
            
            return `
            <div class="song-card" data-song-id="${song.id}">
                <div class="song-cover">
                    <img src="${safeCover}" alt="${safeTitle}" onerror="this.src='https://via.placeholder.com/60'">
                    <div class="play-overlay">
                        <button class="play-btn" onclick="player.playSong(${safeId})">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="song-info">
                    <div class="song-title">${safeTitle}</div>
                    <div class="song-artist">${safeArtist}</div>
                </div>
            </div>
            `;
        }).join('');
    }
    
    renderPlaylistGrid(playlists) {
        return playlists.map(playlist => `
            <div class="playlist-card" data-playlist-id="${playlist.id}">
                <div class="playlist-cover">
                    <i class="fas fa-music"></i>
                </div>
                <div class="playlist-name">${playlist.name}</div>
                <div class="playlist-count">${playlist.count} 首歌</div>
            </div>
        `).join('');
    }
    
    renderSongList(songs) {
        debug("渲染歌曲列表，总数:", songs.length);
        // 不限制歌曲数量，显示所有获取到的歌曲
        return `
            ${songs.map((song, index) => {
                // 处理不同的字段名
                const title = song.title || song.name || '未知歌曲';
                const artist = song.artist || song.singer || '未知歌手';
                const cover = song.cover || song.pic || song.picUrl || 'https://via.placeholder.com/60';
                const duration = song.duration || '0:00';
                
                // 确保歌曲ID存在
                if (!song.id && (song.songId || song.song_id)) {
                    song.id = song.songId || song.song_id;
                }
                
                // 如果仍然没有ID，生成一个
                if (!song.id) {
                    song.id = `song_${Date.now()}_${index}`;
                    debug("为歌曲生成ID:", song.id, title);
                }
                
                // 确保歌曲标题和艺术家名称不含有可能导致HTML解析错误的字符
                const safeTitle = this.escapeHtml(title);
                const safeArtist = this.escapeHtml(artist);
                
                // 处理封面图片
                let safeCover = cover;
                // 处理无效的图片URL
                if (!safeCover || safeCover === 'null' || safeCover === 'undefined' || safeCover === '') {
                    safeCover = `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${encodeURIComponent((title || '').charAt(0))}`;
                }
                // 确保HTTPS
                if (typeof safeCover === 'string' && safeCover.startsWith('http:')) {
                    safeCover = safeCover.replace(/^http:/, 'https:');
                }
                
                // 确保ID是安全的
                const safeId = typeof song.id === 'string' ? `'${song.id.replace(/'/g, "\\'")}'` : song.id;
                
                // 保存到热门歌曲全局数据中
                if (window.currentView === 'ranking') {
                    // 确保window.hotSongs是一个数组
                    if (!window.hotSongs || !Array.isArray(window.hotSongs)) {
                        debug("初始化window.hotSongs为空数组");
                        window.hotSongs = [];
                    }
                    
                    // 检查歌曲是否已存在
                    let exists = false;
                    for (let i = 0; i < window.hotSongs.length; i++) {
                        if (window.hotSongs[i] && window.hotSongs[i].id === song.id) {
                            exists = true;
                            break;
                        }
                    }
                    
                    if (!exists) {
                        // 确保时长是秒数
                        if (typeof song.duration === 'string' && song.duration.includes(':')) {
                            if (!song.maxDuration && !song.durationInSeconds) {
                                const durationInSeconds = this.parseTimeString(song.duration);
                                song.maxDuration = durationInSeconds;
                                song.durationInSeconds = durationInSeconds;
                            }
                        }
                        window.hotSongs.push({...song});
                        debug("添加歌曲到热门列表:", song.id, title);
                    }
                }
                
                // 格式化时间
                let formattedDuration = duration;
                if (typeof duration === 'number') {
                    const minutes = Math.floor(duration / 60);
                    const seconds = Math.floor(duration % 60);
                    formattedDuration = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                } else if (typeof duration === 'string' && duration.includes(':')) {
                    // 已经是格式化的时间字符串，直接使用
                    formattedDuration = duration;
                    
                    // 如果没有设置秒数时长，设置它
                    if (!song.maxDuration && !song.durationInSeconds) {
                        const durationInSeconds = this.parseTimeString(duration);
                        song.maxDuration = durationInSeconds;
                        song.durationInSeconds = durationInSeconds;
                    }
                }
                
                return `
                <div class="song-item ${this.state.currentSong?.id === song.id ? 'playing' : ''}" 
                     data-song-id="${song.id}" 
                     data-song-url="${song.url || ''}">
                    <div class="song-index">${index + 1}</div>
                    <div class="song-item-cover">
                        <img src="${safeCover}" alt="${safeTitle}" onerror="this.src='https://via.placeholder.com/60'">
                    </div>
                    <div class="song-item-info">
                        <div class="song-item-title">${safeTitle}</div>
                        <div class="song-item-artist">${safeArtist}</div>
                    </div>
                    <div class="song-item-duration">${formattedDuration}</div>
                    <div class="song-item-actions">
                        <button class="song-action-btn" onclick="player.playSong(${safeId})" title="播放">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="song-action-btn" onclick="player.toggleSongFavorite(${safeId})" title="收藏">
                            <i class="fa${this.state.favorites instanceof Set && this.state.favorites.has(song.id) ? 's' : 'r'} fa-heart"></i>
                        </button>
                        <button class="song-action-btn" onclick="player.showContextMenu(event, ${safeId})" title="更多">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>
                `;
            }).join('')}
        `;
    }
    
    // 转义HTML特殊字符
    escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
    
    renderEmptyState(icon, title, message) {
        return `
            <div class="empty-state">
                <i class="fas fa-${icon}"></i>
                <h3>${title}</h3>
                <p>${message}</p>
            </div>
        `;
    }
    
    // ==================== 播放控制 ====================
    playSong(songId) {
        // 重要的调试信息，即使在非调试模式下也保留
        debug("播放歌曲:", songId);
        
        debug("点击播放歌曲:", songId, "当前音量:", this.state.volume, "静音状态:", this.state.isMuted);
        
        // 如果没有提供ID，不执行任何操作
        if (!songId) return;
        
        // 如果歌曲已经在播放列表中，直接播放该索引位置
        const songIndex = this.state.currentPlaylist.findIndex(s => s.id === songId);
        if (songIndex !== -1) {
            debug("歌曲已在播放列表中，直接播放索引位置:", songIndex);
            this.state.currentIndex = songIndex;
            
            // 设置当前歌曲
            if (this.state.songInfoList) {
                const songInfo = this.state.songInfoList.find(s => s.id === songId);
                if (songInfo) {
                    this.state.currentSong = songInfo;
                }
            }
            
            // 立即更新UI
            this.updateCurrentPlaylist();
            
            // 获取歌曲信息并直接使用服务端播放
            const songInfo = this.getSongInfo(songId);
            if (songInfo) {
                // 设置标记，表示我们正在处理播放列表中的歌曲
                window._isPlayingExistingSong = true;
                
                // 直接使用服务端播放，不修改播放列表
                this._useServerPlayback(songInfo);
                return; // 直接返回，不执行后续代码
            }
        }
        
        // 设置标记，表示正在通过播放按钮播放歌曲
        // 这将防止在_useServerPlayback中重复添加到播放列表
        window._isAddingToPlaylist = false; // 直接播放不设置此标记

        // 设置播放现有歌曲的标记，稍后清除
        setTimeout(() => {
            window._isPlayingExistingSong = false;
        }, 100);
        // 查找热门歌曲数据（首页显示的热门歌曲）
        let song = null;
        
        // 先从热门歌曲中查找（从loadDiscoverData函数加载的热门歌曲）
        const hotSongsContainer = document.getElementById('hotSongs');
        if (hotSongsContainer) {
            const songElements = hotSongsContainer.querySelectorAll(`[data-song-id="${songId}"]`);
            if (songElements && songElements.length > 0) {
                // 从DOM中获取歌曲信息
                const titleEl = songElements[0].querySelector('.song-title');
                const artistEl = songElements[0].querySelector('.song-artist');
                const title = titleEl ? titleEl.textContent : '未知歌曲';
                const artist = artistEl ? artistEl.textContent : '未知歌手';
                debug("在热门歌曲DOM中找到歌曲:", title, artist);
            }
        }
        
        // 从全局热门歌曲数据中查找
        if (!song && window.hotSongs && window.hotSongs.length > 0) {
            song = window.hotSongs.find(s => s.id === songId);
            if (song) {
                debug("在全局热门歌曲数据中找到歌曲:", song.title, song.artist);
                
                // 确保歌曲对象包含必要的字段
                if (!song.title && song.name) song.title = song.name;
                if (!song.artist && song.singer) song.artist = song.singer;
                
                // 确保有平台信息
                if (!song.platform) song.platform = this.currentPlatform;
                
                // 确保duration是秒数
                if (song.duration && typeof song.duration === 'string' && song.duration.includes(':')) {
                    song.maxDuration = this.parseTimeString(song.duration);
                    debug("将时间字符串转换为秒数:", song.duration, "->", song.maxDuration);
                }
            }
        }
        
        // 优先查找每日推荐歌曲
        if (!song && this.dailyRecommendSongs && this.dailyRecommendSongs.length > 0) {
            song = this.dailyRecommendSongs.find(s => s.id === songId);
            if (song) debug("在每日推荐中找到歌曲");
        }
        
        // 如果在每日推荐中未找到，查找排行榜数据
        if (!song && this.rankingSongs) {
            // 遍历所有排行榜类型
            for (const type in this.rankingSongs) {
                if (this.rankingSongs[type] && this.rankingSongs[type].length > 0) {
                    const foundSong = this.rankingSongs[type].find(s => s.id === songId);
                    if (foundSong) {
                        song = foundSong;
                        debug(`在排行榜[${type}]中找到歌曲:`, foundSong.title, foundSong.artist);
                        break;
                    }
                }
            }
        }
        
        // 查找歌单歌曲
        if (!song && window.playlistSongs && window.playlistSongs.length > 0) {
            song = window.playlistSongs.find(s => s.id === songId);
            if (song) {
                debug("在当前歌单中找到歌曲:", song.title, song.artist);
                
                // 确保歌曲对象包含必要的字段
                if (!song.title && song.name) song.title = song.name;
                if (!song.artist && song.singer) song.artist = song.singer;
                
                // 确保有平台信息
                if (!song.platform) song.platform = this.currentPlatform;
                
                // 确保duration是秒数
                if (song.duration && typeof song.duration === 'string' && song.duration.includes(':')) {
                    song.maxDuration = this.parseTimeString(song.duration);
                    debug("将时间字符串转换为秒数:", song.duration, "->", song.maxDuration);
                }
            }
        }
        
        // 如果在搜索结果中找到，使用搜索结果中的数据
        if (!song && this.searchResults && this.searchResults.length > 0) {
            song = this.searchResults.find(s => s.id === songId);
        }
        
        // 如果在历史记录中找到
        if (!song && window.historySongs && window.historySongs.length > 0) {
            song = window.historySongs.find(s => s.id === songId);
            if (song) {
                debug("在历史记录中找到歌曲:", song.title, song.artist);
                
                // 确保歌曲对象包含必要的字段
                if (!song.title && song.name) song.title = song.name;
                if (!song.artist && song.singer) song.artist = song.singer;
                
                // 确保有平台信息
                if (!song.platform) song.platform = this.currentPlatform;
            }
        }
        
        // 如果在收藏列表中找到
        if (!song && this.favoriteSongs && this.favoriteSongs.length > 0) {
            song = this.favoriteSongs.find(s => s.id === songId);
        }
        
                        // 如果仍然找不到，但是有ID，创建一个最小的歌曲对象
        if (!song && songId) {
            // 在服务器端查询歌曲信息
            debug("在客户端未找到歌曲信息，尝试从服务器获取:", songId);
            
            // 使用getSongInfo方法获取最基本的歌曲信息
            const songInfo = this.getSongInfo(songId);
            
            // 创建一个临时歌曲对象，确保播放功能可以继续
            song = {
                ...songInfo,
                id: songId,
                title: songInfo.title || "加载中...",
                artist: songInfo.artist || "请稍候",
                url: songInfo.url || "",
                platform: songInfo.platform || this.currentPlatform,
                search_keyword: songInfo.search_keyword || this._currentSearchKeyword || "每日推荐",
                duration: songInfo.duration || 180 // 默认时长3分钟
            };
            
            // 立即使用服务端播放，服务器会查询并返回正确的歌曲信息
            this._useServerPlayback(song);
            return;
        }
        
        // 如果没有找到歌曲信息，返回
        if (!song) {
            console.error("未找到歌曲信息，无法播放");
            return;
        }
        
        // 确保歌曲有标题和艺术家
        if (!song.title || song.title === "未知歌曲") {
            if (song.name) song.title = song.name;
        }
        
        if (!song.artist || song.artist === "未知歌手") {
            if (song.singer) song.artist = song.singer;
        }
        
        debug("准备播放歌曲:", song);
        
        // 强制重置进度条
        this._forceProgressBarReset();
        
        // 重置时间状态
        this.state.startTime = Date.now();
        this.state.pauseTime = null;
        this.state.currentTime = 0;
        
        // 设置播放状态为true，确保进度条开始更新
        this.state.isPlaying = true;
        
        // 直接显示歌曲信息，不添加加载状态
        this.updatePlayerInfo(song);
        
        // 确保歌曲URL是HTTPS
        let secureUrl = song.url || '';
        if (secureUrl.startsWith('http:')) {
            secureUrl = secureUrl.replace(/^http:/, 'https:');
        }
        
        // 停止当前可能正在播放的音频
        if (this.audio) {
            this.audio.pause();
            this.audio.currentTime = 0;
        }
        
        // 标记我们正在处理播放请求，避免重复处理
        if (this._playingInProgress) {
            return;
        }
        this._playingInProgress = true;
        
        // 设置一个标志，表示我们是否已经启动了服务端播放
        let serverPlaybackStarted = false;
        
        // 如果URL为空，尝试先获取URL再播放
        if (!secureUrl || secureUrl === '') {
            debug("歌曲URL为空，尝试获取URL后播放:", song.id);
            
            // 直接显示歌曲信息，不显示加载状态
            this.updatePlayerInfo(song);
            
            // 尝试从服务器获取完整歌曲信息
            fetch(`https://${GetParentResourceName()}/getSongUrl`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    songId: song.id,
                    platform: song.platform || song.source || this.currentPlatform
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === "success" && data.url) {
                    // 更新URL并重新尝试播放
                    song.url = data.url;
                    debug("获取到歌曲URL:", data.url);
                    this._useServerPlayback(song, data.url);
                } else {
                    // URL获取失败，使用服务端播放
                    debug("获取URL失败，使用服务端播放");
                    this._useServerPlayback(song);
                }
            })
            .catch(error => {
                console.error("获取歌曲URL出错:", error);
                // 错误处理，继续使用服务端播放
                this._useServerPlayback(song);
            })
            .finally(() => {
                this._playingInProgress = false;
            });
            
            // 确保进度条立即开始更新
            this.updatePlayButton();
            this.updateProgressDisplay();
            
            // 强制启动进度更新
            this._startProgressUpdates();
            
            return;
        }
        
        // 设置超时，如果浏览器播放在3秒内没有成功，则使用服务端播放
        const playbackTimeout = setTimeout(() => {
            if (!this.state.isPlaying && !serverPlaybackStarted) {
                serverPlaybackStarted = true;
                this._useServerPlayback(song, secureUrl);
            }
            this._playingInProgress = false;
        }, 3000);
        
        // 设置音频源
        this.audio.src = secureUrl;
        this.audio.volume = this.state.volume;
        
        // 尝试浏览器播放
        this.audio.play().then(() => {
            // 播放成功
            clearTimeout(playbackTimeout);
            this.state.isPlaying = true;
            this.updatePlayButton();
            
            // 处理可能包含特殊字符的字段，严格检查类型
            const title = typeof song.title === 'string' ? song.title : 
                        (typeof song.name === 'string' ? song.name : '未知歌曲');
            const artist = typeof song.artist === 'string' ? song.artist : 
                          (typeof song.singer === 'string' ? song.singer : '未知歌手');
            
            // 记录历史
            this.addToHistory(song.id);
            
            // 确保有songInfoList数组用于存储完整歌曲信息
            if (!this.state.songInfoList) {
                this.state.songInfoList = [];
            }
            
            // 保存完整的歌曲信息到songInfoList
            const songInfo = {
                id: song.id,
                title: song.title || song.name || `歌曲 ${song.id}`,
                artist: song.artist || song.singer || '未知歌手',
                cover: song.cover || song.pic || song.picUrl || `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${song.id}`,
                duration: song.duration || song.maxDuration || '0:00',
                url: song.url || '',
                platform: song.platform || song.source || 'unknown'
            };
            
            // 移除详细信息中已存在的记录
            this.state.songInfoList = this.state.songInfoList.filter(s => s.id !== song.id);
            // 添加详细信息
            this.state.songInfoList.push(songInfo);
            this.state.saveToStorage();
            
            // 添加到当前播放列表，避免重复添加
            if (!this.state.currentPlaylist.includes(song.id)) {
                // 将歌曲添加到播放列表的最上方（开头）
                this.state.currentPlaylist.unshift(song.id);
                this.state.currentIndex = 0; // 新歌曲位于索引0位置
                // 更新播放列表UI
                this.updateCurrentPlaylist();
                debug("歌曲已添加到播放列表顶部:", {
                    id: song.id,
                    title: song.title || song.name,
                    artist: song.artist || song.singer,
                    playlistLength: this.state.currentPlaylist.length,
                    currentIndex: this.state.currentIndex
                });
            } else {
                // 如果已在播放列表中，只更新当前索引，不改变歌曲位置
                this.state.currentIndex = this.state.currentPlaylist.indexOf(song.id);
                debug("歌曲已在播放列表中，更新索引:", {
                    id: song.id,
                    index: this.state.currentIndex
                });
            }
            
            // 告诉服务器当前正在播放此歌曲
            // 确保传递正确的时长
            let durationToSend = song.maxDuration;
            if (!durationToSend && song.duration) {
                if (typeof song.duration === 'string' && song.duration.includes(':')) {
                    durationToSend = this.parseTimeString(song.duration);
                } else {
                    durationToSend = parseInt(song.duration) || 180;
                }
            }
            
            // 确保时长至少为180秒（3分钟）
            if (!durationToSend || durationToSend < 180) {
                durationToSend = 180;
            }
            
            debug("发送给服务器的歌曲时长:", durationToSend, "秒");
            
            fetch(`https://${GetParentResourceName()}/playSong`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    songId: song.id,
                    url: secureUrl,
                    title: title,
                    artist: artist,
                    duration: durationToSend,
                    maxDuration: durationToSend,
                    durationInSeconds: durationToSend
                })
            })
            .catch(error => {
                // 禁用控制台错误日志
            // console.error("通知服务器播放状态失败:", error);
            });
            
            this._playingInProgress = false;
        }).catch(error => {
            // 禁用控制台错误日志
            // console.error("浏览器播放失败:", error);
            
            // 如果浏览器播放失败，使用服务端播放
            if (!serverPlaybackStarted) {
                serverPlaybackStarted = true;
                this._useServerPlayback(song, secureUrl);
            }
            
            this._playingInProgress = false;
        });
    }
    
    // 添加新方法，确保进度更新定时器启动
    _startProgressUpdates() {
        // 立即更新一次进度条
        this.updateProgressDisplay();
        
        // 确保进度条有动画效果
        const progressFill = document.getElementById('progressFill');
        if (progressFill) {
            progressFill.classList.add('playing');
        } else {
            // 禁用控制台错误日志
            // console.error("未找到进度条元素!");
        }
        
        // 强制立即更新一次进度值
        if (this.state.isPlaying) {
            if (!this.audio.src || this.audio.paused) {
                // 服务端播放模式，确保有初始进度
                if (this.state.currentTime === 0 || this.state.currentTime === undefined) {
                    this.state.currentTime = 0.1; // 设置一个小的初始值，确保进度条显示
                }
            }
            this.updateProgressDisplay();
        }
        
        // 确保洗牌和循环按钮状态正确
        const shuffleBtn = document.getElementById('shuffleBtn');
        if (shuffleBtn) {
            shuffleBtn.classList.toggle('active', this.state.isShuffling);
        }
        
        const repeatBtn = document.getElementById('repeatBtn');
        if (repeatBtn) {
            repeatBtn.classList.toggle('active', this.state.repeatMode !== 'none');
            const icon = repeatBtn.querySelector('i');
            if (icon) {
                icon.className = this.state.repeatMode === 'one' ? 'fas fa-redo-alt' : 'fas fa-redo';
            }
        }
        
        // 确保音量和静音状态正确
        this.updateVolumeDisplay();
        
        // 应用静音状态到音频元素
        if (this.audio) {
            this.audio.muted = this.state.isMuted;
            this.audio.volume = this.state.volume;
        }
        
        // 每次开始播放时，确保服务器端应用了当前的音量设置
        const effectiveVolume = this.state.isMuted ? 0 : this.state.volume;
        debug("进度更新启动时设置音量:", effectiveVolume, "静音状态:", this.state.isMuted);
        
        // 发送音量设置到服务器 - 添加重试机制
        const sendVolumeToServer = (retryCount = 0) => {
            fetch(`https://${GetParentResourceName()}/setVolume`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    volume: effectiveVolume
                })
            }).then(response => response.json())
            .then(data => {
                debug("启动时音量设置成功:", data);
            })
            .catch(error => {
                // 禁用控制台错误日志
                // console.error("启动时音量设置失败:", error);
                // 如果失败，最多重试2次
                if (retryCount < 2) {
                    debug(`启动时音量设置重试 (${retryCount + 1}/2)...`);
                    setTimeout(() => sendVolumeToServer(retryCount + 1), 300);
                }
            });
        };
        
        // 立即发送一次
        sendVolumeToServer();
        
        // 清除可能存在的旧定时器
        if (window._progressUpdateTimer) {
            clearInterval(window._progressUpdateTimer);
        }
        
        // 创建新的进度更新定时器
        window._progressUpdateTimer = setInterval(() => {
            if (this.state.isPlaying) {
                // 如果是服务端播放模式，手动更新时间
                if (!this.audio.src || this.audio.paused) {
                    // 增加当前时间
                    const oldTime = this.state.currentTime || 0;
                    this.state.currentTime = oldTime + 0.5; // 每500毫秒增加0.5秒
                    
                    // 直接操作DOM更新进度条，不依赖updateProgressDisplay方法
                    const duration = this.state.duration || 180;
                    const progress = duration > 0 ? (this.state.currentTime / duration) * 100 : 0;
                    
                    // 直接设置进度条宽度
                    if (progressFill) {
                        progressFill.style.width = `${progress}%`;
                    }
                    
                    // 更新进度条手柄位置
                    const progressHandle = document.getElementById('progressHandle');
                    if (progressHandle) {
                        progressHandle.style.left = `${progress}%`;
                    }
                    
                    // 更新时间显示
                    const currentTimeElement = document.getElementById('currentTime');
                    if (currentTimeElement) {
                        currentTimeElement.textContent = this.formatTime(this.state.currentTime);
                    }
                    
                    // 检查是否播放完毕
                    if (this.state.currentTime >= this.state.duration) {
                        // 重要的调试信息，即使在非调试模式下也保留
                        debug(`歌曲播放完毕，当前循环模式: ${this.state.repeatMode}`);
                        // 所有循环模式都使用handleSongEnd处理，确保一致性
                        debug("触发handleSongEnd处理歌曲结束");
                        this.handleSongEnd();
                        return; // 避免更新进度条
                    }
                } else {
                    // 浏览器播放模式，也直接更新进度条
                    const progress = this.audio.duration > 0 ? (this.audio.currentTime / this.audio.duration) * 100 : 0;
                    
                    // 直接设置进度条宽度
                    if (progressFill) {
                        progressFill.style.width = `${progress}%`;
                    }
                    
                    // 更新进度条手柄位置
                    const progressHandle = document.getElementById('progressHandle');
                    if (progressHandle) {
                        progressHandle.style.left = `${progress}%`;
                    }
                    
                    // 更新时间显示
                    const currentTimeElement = document.getElementById('currentTime');
                    if (currentTimeElement) {
                        currentTimeElement.textContent = this.formatTime(this.audio.currentTime);
                    }
                }
            }
        }, 500);
    }
    
    // 使用服务端播放（提取为单独方法以避免代码重复）
    _useServerPlayback(song, url = null, startTime = null) {
        try {
        // 记录函数调用信息
        // 重要的调试信息，即使在非调试模式下也保留
        debug("使用服务端播放歌曲:", song.id);
        debug("服务端播放详情 - URL:", url || song.url || "无", "静音状态:", this.state.isMuted, "音量:", this.state.volume);
        
        // 确保音量设置正确 - 每次播放新歌曲时都发送当前音量设置
        const effectiveVolume = this.state.isMuted ? 0 : this.state.volume;
        fetch(`https://${GetParentResourceName()}/setVolume`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                volume: effectiveVolume
            })
        }).catch(() => {
            // 错误处理，但不输出调试信息
        });
        
                    // 添加歌曲到历史记录
            if (song && song.id) {
                debug("服务端播放时添加歌曲到历史记录:", song.id);
                this.addToHistory(song.id);
                
                // 尝试获取更完整的歌曲信息
                const songInfo = this.getSongInfo(song.id);
                
                // 合并原始歌曲数据和获取到的信息
                song = {...song, ...songInfo};
                
                // 确保有songInfoList数组用于存储完整歌曲信息
                if (!this.state.songInfoList) {
                    this.state.songInfoList = [];
                }
                
                // 保存完整的歌曲信息到songInfoList
                // 处理可能包含特殊字符的字段
                const safeTitle = typeof song.title === 'string' ? song.title.substring(0, 100) : 
                                (typeof song.name === 'string' ? song.name.substring(0, 100) : `歌曲 ${song.id}`);
                const safeArtist = typeof song.artist === 'string' ? song.artist.substring(0, 100) : 
                                (typeof song.singer === 'string' ? song.singer.substring(0, 100) : '未知歌手');
                
                // 确保封面URL使用HTTPS
                let coverUrl = song.cover || song.pic || song.picUrl || `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${song.id}`;
                if (coverUrl && coverUrl.startsWith('http:')) {
                    coverUrl = coverUrl.replace(/^http:/, 'https:');
                }
                
                const songInfoObj = {
                    id: song.id,
                    title: safeTitle,
                    artist: safeArtist,
                    cover: coverUrl,
                    duration: song.duration || song.maxDuration || '0:00',
                    url: song.url || url || '',
                    platform: song.platform || song.source || 'unknown',
                    search_keyword: song.search_keyword || "每日推荐"
                };
                
                // 移除详细信息中已存在的记录
                this.state.songInfoList = this.state.songInfoList.filter(s => s.id !== song.id);
                // 添加详细信息
                this.state.songInfoList.push(songInfoObj);
                this.state.saveToStorage();
                
                // 设置当前歌曲
                this.state.currentSong = songInfoObj;
            
            // 检查歌曲是否已经在播放列表中
            const songIndex = this.state.currentPlaylist.indexOf(song.id);
            
            // 处理播放列表逻辑
            if (window._isPlayingExistingSong) {
                // 如果标记为"正在播放已存在的歌曲"，则不需要修改播放列表
                debug("正在播放已存在于播放列表中的歌曲，无需重复添加");
                // 不需要做任何操作，因为playSong方法已经设置了正确的currentIndex
            }
            else if (songIndex !== -1) {
                // 如果歌曲已经在播放列表中，只更新当前索引
                this.state.currentIndex = songIndex;
                debug("服务端播放: 歌曲已在播放列表中，更新索引:", this.state.currentIndex);
            }
            else {
                // 歌曲不在播放列表中，需要添加
                // 检查是否是通过右键菜单"添加到播放列表"操作
                if (window._isAddingToPlaylist) {
                    // 通过右键菜单添加，歌曲已经通过addToPlaylist添加到末尾
                    // 这里不需要再次添加，只需要找到它的索引
                    const newIndex = this.state.currentPlaylist.indexOf(song.id);
                    if (newIndex === -1) {
                        // 如果还没添加，手动添加到末尾
                        this.state.currentPlaylist.push(song.id);
                    }
                    // 不改变currentIndex，保持当前播放的歌曲
                    debug("服务端播放: 歌曲通过右键菜单添加到播放列表，不改变当前播放");
                } else {
                    // 直接点击播放，添加到开头并立即播放
                    this.state.currentPlaylist.unshift(song.id);
                    this.state.currentIndex = 0; // 新歌曲位于索引0位置
                    debug("服务端播放: 歌曲已添加到播放列表顶部并开始播放:", {
                        id: song.id,
                        title: song.title || song.name,
                        artist: song.artist || song.singer,
                        playlistLength: this.state.currentPlaylist.length
                    });
                }
            }
            
            // 更新播放列表UI
            this.updateCurrentPlaylist();
        }
        
        // 检查是否有原始数据，如果有则优先使用原始数据
        if (song.originalData) {
            debug("使用歌曲原始数据进行播放");
            // 合并原始数据和当前数据
            song = {...song.originalData, ...song};
        }
        
        // 如果是热门歌曲页面，尝试从window.hotSongs中查找
        if (window.hotSongs && window.hotSongs.length > 0 && song.id) {
            const hotSong = window.hotSongs.find(s => s.id === song.id);
            if (hotSong) {
                debug("在全局热门歌曲数据中找到更完整信息");
                // 合并热门歌曲数据
                song = {...hotSong, ...song};
            }
        }
        
        // 确保歌曲对象包含必要的字段
        if (!song.title && song.name) {
            song.title = song.name;
            debug("从name字段复制歌曲标题:", song.title);
        }
        
        if (!song.artist && song.singer) {
            song.artist = song.singer;
            debug("从singer字段复制歌手名称:", song.artist);
        }
        
        debug("服务端播放详细信息:", song);
            
        // 获取正确的歌曲时长（单位：秒）
        // 确保歌曲时长足够长，避免几秒钟就结束
        let duration;
        
        // 如果duration是字符串格式(如"3:45")，先转换为秒数
        if (song.duration && typeof song.duration === 'string' && song.duration.includes(':')) {
            duration = this.parseTimeString(song.duration);
        } else {
            duration = song.maxDuration || parseInt(song.duration) || 180;
        }
        
        // 确保所有歌曲都有合理的时长
        if (!song.duration && !song.maxDuration) {
            debug("歌曲没有明确时长，设置默认时长");
            song.duration = 180; // 默认3分钟
        }
        
        // 处理可能包含特殊字符的字段
        const safeTitle = typeof song.title === 'string' ? song.title.substring(0, 100) : 
                        (typeof song.name === 'string' ? song.name.substring(0, 100) : '未知歌曲');
        const safeArtist = typeof song.artist === 'string' ? song.artist.substring(0, 100) : 
                        (typeof song.singer === 'string' ? song.singer.substring(0, 100) : '未知歌手');
        
        // 更新歌曲对象的标题和歌手信息
        song.title = safeTitle;
        song.artist = safeArtist;
        
        // 使用提供的URL或歌曲原始URL
        let secureUrl = url || song.url || '';
        
        // 确保URL使用HTTPS
        if (typeof secureUrl === 'string' && secureUrl.startsWith('http:')) {
            secureUrl = secureUrl.replace(/^http:/, 'https:');
        }
        
        // 打印详细的歌曲信息用于调试
        debug("服务端播放详细信息:", {
            id: song.id,
            title: safeTitle,
            artist: safeArtist,
            duration: duration,
            url: secureUrl
        });
        
            // 强制重置进度条
            this._forceProgressBarReset();
            
            // 强制设置播放状态，不等待服务器响应
            this.state.isPlaying = true;
            
            // 使用保存的开始时间或设置一个小的初始值
            const effectiveStartTime = (startTime !== null && !isNaN(startTime)) ? Number(startTime) : 0.1;
            this.state.currentTime = effectiveStartTime;
            this.state.duration = duration;
            
            // 记录恢复播放的时间点，用于计算实际播放时间
            this.state.resumeTime = Date.now();
            // 保存开始播放的位置，用于计算实际播放时间
            this.state.resumePosition = effectiveStartTime;
            
            // 更新播放器信息显示
            this.updatePlayerInfo(song);
            
            // 立即更新UI
            this.updatePlayButton();
            
            // 直接操作DOM更新进度条
            const progressFill = document.getElementById('progressFill');
            const progressHandle = document.getElementById('progressHandle');
            const currentTimeElement = document.getElementById('currentTime');
            const totalTimeElement = document.getElementById('totalTime');
            
            // 计算初始进度
            const initialProgress = duration > 0 ? (effectiveStartTime / duration) * 100 : 0.5;
            
            if (progressFill) {
                progressFill.style.width = `${initialProgress}%`;
                progressFill.classList.add('playing');
            }
            
            if (progressHandle) {
                progressHandle.style.left = `${initialProgress}%`;
            }
            
            if (currentTimeElement) {
                currentTimeElement.textContent = this.formatTime(effectiveStartTime);
            }
            
            if (totalTimeElement) {
                totalTimeElement.textContent = this.formatTime(duration);
            }
            
            // 创建一个立即开始更新的进度条
            let animationFrame;
            
            const updateProgressAnimation = () => {
                if (!this.state.isPlaying) {
                    cancelAnimationFrame(animationFrame);
                    return;
                }
                
                // 计算从恢复播放到现在经过的时间（毫秒）
                const elapsedSinceResume = Date.now() - this.state.resumeTime;
                // 计算当前播放位置 = 恢复时的位置 + 经过的时间（秒）
                const currentPosition = this.state.resumePosition + (elapsedSinceResume / 1000);
                
                // 更新当前时间
                this.state.currentTime = currentPosition;
                
                const progress = duration > 0 ? (currentPosition / duration) * 100 : 0;
                
                if (progressFill) {
                    progressFill.style.width = `${Math.min(progress, 100)}%`;
                }
                
                if (progressHandle) {
                    progressHandle.style.left = `${Math.min(progress, 100)}%`;
                }
                
                if (currentTimeElement) {
                    currentTimeElement.textContent = this.formatTime(currentPosition);
                }
                
                if (currentPosition < duration) {
                    animationFrame = requestAnimationFrame(updateProgressAnimation);
                    // 更新保存的动画帧ID
                    this.animationFrameId = animationFrame;
                } else {
                    this.handleSongEnd();
                    // 清除动画帧ID
                    this.animationFrameId = null;
                }
            };
            
            // 启动动画帧更新
            animationFrame = requestAnimationFrame(updateProgressAnimation);
            // 保存动画帧ID以便可以在其他地方取消
            this.animationFrameId = animationFrame;
            
            // 使用服务端API播放
        // 确保时长至少为180秒（3分钟）
        if (duration < 180) {
            debug("歌曲时长过短，设置为默认3分钟:", duration, "->", 180);
            duration = 180;
        }
        
        fetch(`https://${GetParentResourceName()}/playServerSide`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                songId: song.id,
                url: secureUrl,
                title: safeTitle,
                artist: safeArtist,
                duration: duration, // 使用计算好的时长
                maxDuration: duration, // 添加maxDuration字段
                durationInSeconds: duration, // 添加durationInSeconds字段
                startTime: effectiveStartTime // 传递正确的开始时间点
            })
        }).then(response => response.json())
        .catch(error => {
            // 不显示错误，继续正常播放
            debug("服务端播放请求出错，但按要求不更新错误状态");
        });
        } catch (error) {
            // 忽略错误，保持当前状态
            debug("_useServerPlayback出现错误，但按要求继续保持正常显示");
        }
    }
    
    // 从保存的位置恢复播放（新增方法）
    _resumeFromSavedPosition(seekTime) {
        // 修复无效位置检查
        if (seekTime === undefined || seekTime === null) {
            seekTime = 0;
        } else if (isNaN(seekTime)) {
            seekTime = 0;
        } else if (seekTime < 0) {
            seekTime = 0;
        }
        
        // 确保seekTime是数字
        seekTime = Number(seekTime) || 0;
        
        // 清除任何现有的动画帧
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        
        // 设置播放状态
        this.state.isPlaying = true;
        this.state.currentTime = seekTime;
        
        // 更新UI
        this.updatePlayButton();
        
        // 更新进度条
        const duration = this.state.duration || 180;
        const progress = duration > 0 ? (seekTime / duration) * 100 : 0;
        
        const progressFill = document.getElementById('progressFill');
        const progressHandle = document.getElementById('progressHandle');
        const currentTimeElement = document.getElementById('currentTime');
        
        if (progressFill) {
            progressFill.style.width = `${progress}%`;
            progressFill.classList.add('playing');
        }
        
        if (progressHandle) {
            progressHandle.style.left = `${progress}%`;
        }
        
        if (currentTimeElement) {
            currentTimeElement.textContent = this.formatTime(seekTime);
        }
        
        // 创建一个动画帧来更新进度条，从保存的位置开始
        // 记录恢复播放的时间点，用于计算实际播放时间
        this.state.resumeTime = Date.now();
        // 保存开始播放的位置，用于计算实际播放时间
        this.state.resumePosition = seekTime;
        
        let animationFrame;
        
        const updateProgressAnimation = () => {
            if (!this.state.isPlaying) {
                cancelAnimationFrame(animationFrame);
                return;
            }
            
            // 计算从恢复播放到现在经过的时间（毫秒）
            const elapsedSinceResume = Date.now() - this.state.resumeTime;
            // 计算当前播放位置 = 恢复时的位置 + 经过的时间（秒）
            const currentPosition = this.state.resumePosition + (elapsedSinceResume / 1000);
            
            // 更新当前时间
            this.state.currentTime = currentPosition;
            
            const progress = duration > 0 ? (currentPosition / duration) * 100 : 0;
            
            if (progressFill) {
                progressFill.style.width = `${Math.min(progress, 100)}%`;
            }
            
            if (progressHandle) {
                progressHandle.style.left = `${Math.min(progress, 100)}%`;
            }
            
            if (currentTimeElement) {
                currentTimeElement.textContent = this.formatTime(currentPosition);
            }
            
            if (currentPosition < duration) {
                animationFrame = requestAnimationFrame(updateProgressAnimation);
                // 更新保存的动画帧ID
                this.animationFrameId = animationFrame;
            } else {
                this.handleSongEnd();
                // 清除动画帧ID
                this.animationFrameId = null;
            }
        };
        
        // 启动动画帧更新
        animationFrame = requestAnimationFrame(updateProgressAnimation);
        // 保存动画帧ID以便可以在其他地方取消
        this.animationFrameId = animationFrame;
    }
    
    togglePlay() {
        if (this.state.isPlaying) {
            // 暂停播放
            this.audio.pause();
            this.state.isPlaying = false;
            
            // 添加暂停状态标记
            this.state.isPaused = true;
            
            // 暂停播放时保存当前播放时间，以便恢复播放时使用
            if (this.audio.src && !isNaN(this.audio.currentTime)) {
                this.state.savedTime = this.audio.currentTime;
            } else {
                // 如果没有音频源，保存当前时间
                this.state.savedTime = this.state.currentTime || 0;
            }
            
            // 停止所有可能正在运行的动画帧
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
                this.animationFrameId = null;
            }
            
            // 调用暂停API而不是停止
            fetch(`https://${GetParentResourceName()}/pauseSong`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            }).then(response => response.json())
            .catch(() => {
                // 错误处理，但不输出调试信息
            });
        } else {
            // 恢复播放
            
            // 清除暂停状态标记
            this.state.isPaused = false;
            
            if (this.audio.src) {
                // 如果有保存的播放时间，恢复到该位置
                if (this.state.savedTime !== undefined && !isNaN(this.state.savedTime)) {
                    this.audio.currentTime = this.state.savedTime;
                }
                
            this.audio.play().catch(() => {
                    // 如果浏览器播放失败，尝试服务端播放
                    if (this.state.currentSong) {
                        // 确保使用正确的保存位置
                        const savedPosition = this.state.savedTime || 0;
                        
                        if (savedPosition > 0) {
                            // 先更新UI
                            this._resumeFromSavedPosition(savedPosition);
                        }
                        
                        // 调用恢复API
                        fetch(`https://${GetParentResourceName()}/resumeSong`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                seekTime: savedPosition
                            })
                        }).then(response => response.json())
                        .catch(() => {
                            // 错误处理，但不输出调试信息
                        });
                    }
                });
            } else if (this.state.currentSong) {
                // 如果没有音频源但有当前歌曲，直接调用resumeSong API
                // 而不是重新初始化播放器
                const savedPosition = this.state.savedTime || 0;
                if (savedPosition > 0) {
                    // 先更新UI
                    this._resumeFromSavedPosition(savedPosition);
                    
                    // 然后调用服务器API恢复播放
                    fetch(`https://${GetParentResourceName()}/resumeSong`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            seekTime: savedPosition
                        })
                    }).then(response => response.json())
                    .catch(() => {
                        // 错误处理，但不输出调试信息
            });
                } else {
                    // 如果没有保存的时间点，也尝试恢复播放
                    fetch(`https://${GetParentResourceName()}/resumeSong`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            seekTime: 0
                        })
                    }).then(response => response.json())
                    .catch(() => {
                        // 错误处理，但不输出调试信息
                    });
                }
            }
            
            this.state.isPlaying = true;
        }
        
        this.updatePlayButton();
        
        // 立即更新一次进度条，确保状态一致
        this.updateProgressDisplay();
        
        // 更新进度条动画状态
        const progressFill = document.getElementById('progressFill');
        if (progressFill) {
            if (this.state.isPlaying) {
                progressFill.classList.add('playing');
            } else {
                progressFill.classList.remove('playing');
            }
        }
    }
    
    playPrevious() {
        debug("播放上一首歌曲", {
            currentIndex: this.state.currentIndex,
            playlistLength: this.state.currentPlaylist.length
        });
        
        if (this.state.currentPlaylist.length === 0) {
            debug("播放列表为空，无法播放上一首");
            this.showToast("播放列表为空", 1500);
            return;
        }
        
        // 如果已经播放超过3秒，点击上一首会重新从头播放当前歌曲
        const resetThreshold = 3; // 3秒阈值
        if (this.state.currentTime > resetThreshold && this.state.currentSong) {
            debug("当前播放进度超过3秒，重新播放当前歌曲");
            this.state.currentTime = 0;
            
            // 始终使用playSong方法重新播放当前歌曲，避免浏览器播放模式错误
            if (this.state.currentSong) {
                this.playSong(this.state.currentSong.id);
            }
            
            this.updateProgressDisplay();
            return;
        }
        
        // 正常切换到上一首
        let newIndex = this.state.currentIndex - 1;
        if (newIndex < 0) {
            newIndex = this.state.currentPlaylist.length - 1;
        }
        
        debug("切换到上一首，索引:", newIndex);
        this.state.currentIndex = newIndex;
        const songId = this.state.currentPlaylist[newIndex];
        debug("播放上一首歌曲ID:", songId);
        this.playSong(songId);
    }
    
    playNext() {
        debug("播放下一首歌曲", {
            currentIndex: this.state.currentIndex,
            playlistLength: this.state.currentPlaylist.length,
            isShuffling: this.state.isShuffling
        });
        
        if (this.state.currentPlaylist.length === 0) {
            debug("播放列表为空，无法播放下一首");
            this.showToast("播放列表为空", 1500);
            return;
        }
        
        let newIndex;
        if (this.state.isShuffling) {
            // 随机播放模式 - 确保不重复播放当前歌曲
            if (this.state.currentPlaylist.length > 1) {
                do {
                    newIndex = Math.floor(Math.random() * this.state.currentPlaylist.length);
                } while (newIndex === this.state.currentIndex);
            } else {
                newIndex = 0; // 如果只有一首歌，就只能播放它
            }
            debug("随机播放模式选择索引:", newIndex);
        } else {
            newIndex = this.state.currentIndex + 1;
            if (newIndex >= this.state.currentPlaylist.length) {
                newIndex = 0;
            }
            debug("顺序播放模式选择索引:", newIndex);
        }
        
        this.state.currentIndex = newIndex;
        const songId = this.state.currentPlaylist[newIndex];
        debug("播放下一首歌曲ID:", songId);
        this.playSong(songId);
    }
    
    handleSongEnd() {
        // 输出调试信息
        debug("处理歌曲结束事件", {
            repeatMode: this.state.repeatMode,
            isShuffling: this.state.isShuffling, 
            currentIndex: this.state.currentIndex,
            playlistLength: this.state.currentPlaylist.length,
            volume: this.state.volume,
            isMuted: this.state.isMuted
        });
        
        // 如果是服务端播放模式，需要停止服务端播放
        if (!this.audio.src || this.audio.paused) {
            // 向服务端发送停止播放请求
            fetch(`https://${GetParentResourceName()}/stopSong`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            }).then(response => response.json())
            .then(data => {
                debug('停止请求已发送:', data);
            })
            .catch(error => {
                console.error('停止请求失败:', error);
            });
        }

        switch (this.state.repeatMode) {
            case 'one':
                debug("执行单曲循环逻辑");
                // 单曲循环 - 始终使用playSong方法重新播放，避免浏览器播放模式的错误
                this.state.currentTime = 0;
                if (this.state.currentSong) {
                    debug("单曲循环重新播放当前歌曲");
                    this.playSong(this.state.currentSong.id);
                }
                break;
            case 'all':
                debug("执行列表循环逻辑");
                // 列表循环 - 强制设置为循环模式
                if (this.state.currentIndex >= this.state.currentPlaylist.length - 1) {
                    // 如果是最后一首歌，手动设置索引到-1使playNext方法重新从头开始
                    debug("列表循环：到达列表末尾，重新从头开始");
                    this.state.currentIndex = -1;
                }
                this.playNext();
                break;
            default:
                debug("执行顺序播放逻辑");
                // 不循环
                if (this.state.currentIndex < this.state.currentPlaylist.length - 1) {
                    debug("顺序播放：播放下一首");
                    this.playNext();
                } else {
                    debug("顺序播放：已到达列表末尾，停止播放");
                    // 播放结束
                    this.state.isPlaying = false;
                    this.updatePlayButton();
                    this.state.currentTime = 0;
                    this.updateProgressDisplay();
                    this.showToast("播放结束", 1500);
                }
                break;
        }
    }
    
    toggleShuffle() {
        this.state.isShuffling = !this.state.isShuffling;
        document.getElementById('shuffleBtn').classList.toggle('active', this.state.isShuffling);
        this.showToast(this.state.isShuffling ? "已开启随机播放" : "已关闭随机播放", 1500);
        this.state.saveToStorage();
    }
    
    cycleRepeatMode() {
        const modes = ['none', 'all', 'one'];
        const currentIndex = modes.indexOf(this.state.repeatMode);
        this.state.repeatMode = modes[(currentIndex + 1) % modes.length];
        
        const repeatBtn = document.getElementById('repeatBtn');
        repeatBtn.classList.toggle('active', this.state.repeatMode !== 'none');
        
        const icon = repeatBtn.querySelector('i');
        if (this.state.repeatMode === 'one') {
            icon.className = 'fas fa-redo-alt';
            this.showToast("单曲循环", 1500);
        } else if (this.state.repeatMode === 'all') {
            icon.className = 'fas fa-redo';
            this.showToast("列表循环", 1500);
        } else {
            icon.className = 'fas fa-redo';
            this.showToast("顺序播放", 1500);
        }
        
        // 保存状态到存储
        this.state.saveToStorage();
    }
    
    // ==================== UI 更新 ====================
    updatePlayerInfo(song) {
        if (!song) return;
        
        // 如果是暂停状态，不显示错误
        if (song.isError && this.state.isPaused) {
            // 恢复原始标题
            if (song.title && (
                song.title.startsWith("加载失败:") || 
                song.title.startsWith("播放失败:") ||
                song.title.startsWith("直接播放失败:"))) {
                song.title = song.title.replace(/^(加载失败:|播放失败:|直接播放失败:)\s*/, "");
            }
            delete song.isError;
        }
        
        // 更新当前歌曲信息
        this.state.currentSong = song;
        
        // 获取DOM元素
        const titleElement = document.getElementById('currentSongTitle');
        const artistElement = document.getElementById('currentSongArtist');
        const coverElement = document.getElementById('currentSongCover');
        const favoriteBtn = document.getElementById('favoriteBtn');
        
        // 处理不同的字段名，确保它们不是undefined
        const title = typeof song.title === 'string' ? song.title : 
                      typeof song.name === 'string' ? song.name : '未知歌曲';
        const artist = typeof song.artist === 'string' ? song.artist : 
                       typeof song.singer === 'string' ? song.singer : '未知歌手';
        
        // 确保封面URL使用HTTPS
        let cover = song.cover || song.pic || song.picUrl || '';
        if (cover && cover.startsWith('http:')) {
            cover = cover.replace(/^http:/, 'https:');
        }
        
        debug("更新播放器信息:", title, artist);
        
        // 更新标题和艺术家
        if (titleElement) {
            titleElement.textContent = title;
            
            // 移除所有状态类
            titleElement.classList.remove('error-state');
            
            // 如果是错误状态，添加错误样式
            if (song.isError) {
                titleElement.classList.add('error-state');
            }
        }
        
        if (artistElement) {
            artistElement.textContent = artist || '-';
        }
        
        // 更新封面图片
        if (coverElement) {
            // 移除所有状态类
            coverElement.classList.remove('error-cover');
            
            // 如果有专辑封面，使用它
            if (cover && cover !== '') {
                coverElement.src = cover;
            } else {
                // 否则使用默认封面
                coverElement.src = 'https://via.placeholder.com/60x60/333/fff?text=♪';
            }
            
            // 如果是错误状态，添加错误样式
            if (song.isError) {
                coverElement.classList.add('error-cover');
            }
        }
        
        // 更新收藏按钮状态
        if (favoriteBtn) {
            // 确保favorites是一个Set对象
            if (!(this.state.favorites instanceof Set)) {
                this.state.favorites = new Set(Array.isArray(this.state.favorites) ? this.state.favorites : []);
            }
            
            if (this.state.favorites.has(song.id)) {
                favoriteBtn.innerHTML = '<i class="fas fa-heart"></i>';
                favoriteBtn.classList.add('active');
            } else {
                favoriteBtn.innerHTML = '<i class="far fa-heart"></i>';
                favoriteBtn.classList.remove('active');
            }
        }
        
        // 更新播放按钮状态
        this.updatePlayButton();
        
        // 更新进度条
        this.updateProgressDisplay();
        
        // 即使在错误状态下，也要确保播放状态和进度正确显示
        if (song.isError && this.state.isPlaying) {
            // 强制更新播放按钮为播放状态
            const playBtn = document.getElementById('playBtn');
            if (playBtn) {
                playBtn.innerHTML = '<i class="fas fa-pause"></i>';
                playBtn.title = '暂停';
            }
            
            // 强制更新进度条动画
            const progressFill = document.getElementById('progressFill');
            if (progressFill) {
                progressFill.classList.add('playing');
            }
        }
    }
    
    updatePlayButton() {
        const playBtn = document.getElementById('playBtn');
        const icon = playBtn.querySelector('i');
        icon.className = this.state.isPlaying ? 'fas fa-pause' : 'fas fa-play';
    }
    
    updateProgressDisplay() {
        // 获取进度条元素
        const progressFill = document.getElementById('progressFill');
        const progressHandle = document.getElementById('progressHandle');
        const currentTimeElement = document.getElementById('currentTime');
        const totalTimeElement = document.getElementById('totalTime');
        
        if (!progressFill || !progressHandle) return;
        
        // 如果没有当前歌曲，重置进度条
        if (!this.state.currentSong) {
            progressFill.style.width = '0%';
            progressHandle.style.left = '0%';
            
            if (currentTimeElement) {
                currentTimeElement.textContent = '0:00';
            }
            
            if (totalTimeElement) {
                totalTimeElement.textContent = '0:00';
            }
            
            return;
        }
        
        // 计算进度百分比
        const duration = this.state.duration || 0;
        const currentTime = this.state.currentTime || 0;
        
        // 防止除以零
        if (duration <= 0) {
            progressFill.style.width = '0%';
            progressHandle.style.left = '0%';
            return;
        }
        
        // 计算百分比并限制在0-100%之间
        let percentage = (currentTime / duration) * 100;
        percentage = Math.max(0, Math.min(100, percentage));
        
        // 更新进度条宽度和手柄位置
        progressFill.style.width = `${percentage}%`;
        progressHandle.style.left = `${percentage}%`;
        
        // 更新时间显示
        if (currentTimeElement) {
            currentTimeElement.textContent = this.formatTime(currentTime);
        }
        
        if (totalTimeElement) {
            totalTimeElement.textContent = this.formatTime(duration);
        }
        
        // 添加或移除动画类
        if (this.state.isPlaying && !this.state.isPaused) {
            progressFill.classList.add('playing');
        } else {
            progressFill.classList.remove('playing');
        }
    }
    
    updateVolumeDisplay() {
        const volumeFill = document.getElementById('volumeFill');
        const volumeHandle = document.getElementById('volumeHandle');
        const volumeBtn = document.getElementById('volumeBtn');
        
        if (!volumeFill || !volumeHandle || !volumeBtn) return;
        
        // 获取当前音量（考虑静音状态）
        const volume = this.state.isMuted ? 0 : this.state.volume;
        
        // 更新音量条宽度和手柄位置
        const percentage = volume * 100;
        volumeFill.style.width = `${percentage}%`;
        volumeHandle.style.left = `${percentage}%`;
        
        // 更新音量图标
        const volumeIcon = volumeBtn.querySelector('i');
        if (!volumeIcon) return;
        
        // 根据音量级别显示不同的图标
        volumeIcon.className = ''; // 清除现有类名
        if (this.state.isMuted || volume === 0) {
            volumeIcon.className = 'fas fa-volume-mute';
        } else if (volume < 0.33) {
            volumeIcon.className = 'fas fa-volume-off';
        } else if (volume < 0.66) {
            volumeIcon.className = 'fas fa-volume-down';
        } else {
            volumeIcon.className = 'fas fa-volume-up';
        }
    }
    
    updateFavoritesCount() {
        // 确保favorites是一个Set对象
        if (!(this.state.favorites instanceof Set)) {
            this.state.favorites = new Set(Array.isArray(this.state.favorites) ? this.state.favorites : []);
        }
        document.querySelector('.nav-item[data-view="favorites"] .count').textContent = this.state.favorites.size;
    }
    
    updateCurrentPlaylist() {
        const container = document.getElementById('currentPlaylist');
        const playlistCount = document.getElementById('playlistCount');
        
        // 移除重复的歌曲ID
        const uniqueIds = [...new Set(this.state.currentPlaylist)];
        if (uniqueIds.length !== this.state.currentPlaylist.length) {
            debug("检测到播放列表中有重复歌曲，已移除重复项");
            this.state.currentPlaylist = uniqueIds;
            
            // 确保currentIndex仍然有效
            if (this.state.currentIndex >= this.state.currentPlaylist.length) {
                this.state.currentIndex = 0;
            }
        }
        
        if (this.state.currentPlaylist.length === 0) {
            container.innerHTML = '<li class="empty-state">播放列表为空</li>';
            playlistCount.textContent = '0';
        } else {
            debug("开始更新播放列表UI，当前播放列表ID数量:", this.state.currentPlaylist.length);
            
            // 从多个可能的来源查找歌曲信息
            const songs = this.state.currentPlaylist.map((id, idx) => {
                // 首先检查state.songInfoList中是否有这首歌的信息
                let song = this.state.songInfoList ? this.state.songInfoList.find(s => s.id === id) : null;
                if (song) {
                    debug(`[播放列表项${idx+1}] ID ${id}：从songInfoList找到歌曲 "${song.title || song.name || '未知歌曲'}" - ${song.artist || song.singer || '未知歌手'}`);
                }
                
                // 如果不在songInfoList中，尝试从当前播放的歌曲中获取
                if (!song && this.state.currentSong && this.state.currentSong.id === id) {
                    song = this.state.currentSong;
                    debug(`[播放列表项${idx+1}] ID ${id}：从currentSong找到歌曲信息`);
                }
                
                // 检查热门歌曲列表
                if (!song && window.hotSongs && Array.isArray(window.hotSongs) && window.hotSongs.length > 0) {
                    song = window.hotSongs.find(s => s.id === id);
                    if (song) {
                        debug(`[播放列表项${idx+1}] ID ${id}：从热门歌曲找到信息`);
                    }
                }
                
                // 检查每日推荐歌曲
                if (!song && window.dailyRecommendSongs && Array.isArray(window.dailyRecommendSongs)) {
                    // 检查ID类型，可能需要转换
                    if (typeof id === 'string' && !isNaN(parseInt(id))) {
                        // 如果id是数字字符串，尝试转换为数字
                        const numId = parseInt(id);
                        
                        // 先用字符串ID查找
                        let recommendSong = window.dailyRecommendSongs.find(s => s.id === id);
                        
                        // 如果没找到，尝试用数字ID查找
                        if (!recommendSong) {
                            recommendSong = window.dailyRecommendSongs.find(s => s.id === numId);
                        }
                        
                        // 如果还没找到，尝试更宽松的比较
                        if (!recommendSong) {
                            recommendSong = window.dailyRecommendSongs.find(s => {
                                return String(s.id) === String(id);
                            });
                        }
                        
                        if (recommendSong) {
                            debug(`[播放列表项${idx+1}] ID ${id}：从每日推荐找到歌曲信息 - ${recommendSong.name || recommendSong.title}`);
                            // 确保字段名称正确
                            song = {
                                ...recommendSong,
                                // 确保标题和歌手字段正确
                                title: recommendSong.title || recommendSong.name || `歌曲 ${id}`,
                                artist: recommendSong.artist || recommendSong.singer || "未知歌手",
                                // 确保有搜索关键词和平台信息
                                search_keyword: recommendSong.search_keyword || "每日推荐",
                                platform: recommendSong.platform || "NETEASE"
                            };
                        }
                    } else {
                        // 正常查找
                        const recommendSong = window.dailyRecommendSongs.find(s => s.id === id);
                        if (recommendSong) {
                            debug(`[播放列表项${idx+1}] ID ${id}：从每日推荐找到歌曲信息 - ${recommendSong.name || recommendSong.title}`);
                            // 确保字段名称正确
                            song = {
                                ...recommendSong,
                                // 确保标题和歌手字段正确
                                title: recommendSong.title || recommendSong.name || `歌曲 ${id}`,
                                artist: recommendSong.artist || recommendSong.singer || "未知歌手",
                                // 确保有搜索关键词和平台信息
                                search_keyword: recommendSong.search_keyword || "每日推荐",
                                platform: recommendSong.platform || "NETEASE"
                            };
                        }
                    }
                }
                
                // 检查搜索结果
                if (!song && this.searchResults && this.searchResults.length > 0) {
                    song = this.searchResults.find(s => s.id === id);
                    if (song) {
                        debug(`[播放列表项${idx+1}] ID ${id}：从搜索结果找到信息`);
                    }
                }
                
                // 检查歌单歌曲
                if (!song && window.playlistSongs && window.playlistSongs.length > 0) {
                    song = window.playlistSongs.find(s => s.id === id);
                    if (song) {
                        debug(`[播放列表项${idx+1}] ID ${id}：从歌单找到信息`);
                    }
                }
                
                // 最后才尝试从模拟数据中查找
                if (!song) {
                    song = this.mockSongs ? this.mockSongs.find(s => s.id === id) : null;
                    if (song) {
                        debug(`[播放列表项${idx+1}] ID ${id}：从模拟数据找到信息`);
                    } else {
                        debug(`[播放列表项${idx+1}] ID ${id}：未找到任何信息，将使用默认值`);
                    }
                }
                
                return song;
            }).filter(Boolean);
            
            container.innerHTML = songs.map((song, index) => {
                // 处理不同的字段名，确保歌曲信息正确显示
                const title = song.title || song.name || '未知歌曲';
                const artist = song.artist || song.singer || '未知歌手';
                
                // 获取封面URL并确保使用HTTPS
                let coverUrl = song.cover || song.pic || song.picUrl || `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${title.charAt(0)}`;
                if (coverUrl && coverUrl.startsWith('http:')) {
                    coverUrl = coverUrl.replace(/^http:/, 'https:');
                }
                const cover = coverUrl;
                const duration = song.duration || '0:00';
                
                if (index < 3) {
                    debug(`[播放列表渲染${index+1}] 标题=${title}, 歌手=${artist}`);
                }
                
                return `
                <li class="song-item ${this.state.currentIndex === index ? 'playing' : ''}" 
                    onclick="player.playFromPlaylist(${index})">
                    <div class="song-item-cover">
                        <img src="${cover}" alt="${title}">
                    </div>
                    <div class="song-item-info">
                        <div class="song-item-title">${title}</div>
                        <div class="song-item-artist">${artist}</div>
                    </div>
                    <div class="song-item-duration">${duration}</div>
                </li>
                `;
            }).join('');
            
            // 使用播放列表的实际长度，而不是过滤后的songs长度
            playlistCount.textContent = this.state.currentPlaylist.length;
        }
    }
    
    // ==================== 工具函数 ====================
    formatTime(seconds) {
        // 确保输入是数字
        if (typeof seconds !== 'number') {
            // 尝试转换为数字
            seconds = Number(seconds);
        }
        
        // 检查是否为有效数字
        if (isNaN(seconds) || seconds < 0) {
            return '0:00';
        }
        
        // 转换为整数
        seconds = Math.floor(seconds);
        
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
    
    addToHistory(songId) {
        debug("添加歌曲到历史记录:", songId);
        
        // 使用getSongInfo方法获取歌曲信息
        const songInfo = this.getSongInfo(songId);
        
        // 确保有songInfoList数组用于存储完整歌曲信息
        if (!this.state.songInfoList) {
            this.state.songInfoList = [];
        }
        
        // 移除已存在的记录(ID列表)
        this.state.playHistory = this.state.playHistory.filter(id => id !== songId);
        // 添加到开头
        this.state.playHistory.unshift(songId);
        
        // 移除详细信息中已存在的记录
        this.state.songInfoList = this.state.songInfoList.filter(song => song.id !== songId);
        // 添加详细信息到开头
        if (songInfo) {
            // 确保不会添加undefined或null
            this.state.songInfoList.unshift(songInfo);
        }
        
        // 限制历史记录数量
        if (this.state.playHistory.length > 50) {
            this.state.playHistory = this.state.playHistory.slice(0, 50);
        }
        
        // 限制歌曲信息列表数量，但要确保不会删除播放列表中的歌曲信息
        if (this.state.songInfoList.length > 100) {
            // 创建一个保留列表，包含播放列表中的所有歌曲ID
            const keepIds = new Set(this.state.currentPlaylist);
            // 添加当前播放的歌曲ID
            if (this.state.currentSong && this.state.currentSong.id) {
                keepIds.add(this.state.currentSong.id);
            }
            
            // 过滤songInfoList，保留播放列表中的歌曲和最近的50首歌曲
            const recentIds = this.state.playHistory.slice(0, 50);
            recentIds.forEach(id => keepIds.add(id));
            
            this.state.songInfoList = this.state.songInfoList.filter(song => 
                keepIds.has(song.id)
            );
        }
        
        debug("更新后的历史记录:", {
            totalSongs: this.state.playHistory.length,
            firstFive: this.state.playHistory.slice(0, 5),
            songInfo: this.state.songInfoList.length > 0 ? this.state.songInfoList[0].title : null
        });
        
        this.state.saveToStorage();
        
        // 如果当前在历史记录视图，刷新数据显示
        if (this.state.currentView === 'history') {
            debug("当前在历史记录视图，刷新显示");
            this.loadHistoryData();
        }
    }
    
    toggleFavorite() {
        if (!this.state.currentSong) return;
        this.toggleSongFavorite(this.state.currentSong.id);
    }
    
    toggleSongFavorite(songId) {
        // 确保favorites是一个Set对象
        if (!(this.state.favorites instanceof Set)) {
            this.state.favorites = new Set(Array.isArray(this.state.favorites) ? this.state.favorites : []);
        }
        
        // 获取当前歌曲的搜索关键词和平台信息
        let searchKeyword = null;
        let platform = null;
        
        // 尝试从搜索结果中找到对应的歌曲
        if (this.searchResults && this.searchResults.length > 0) {
            const song = this.searchResults.find(s => s.id === songId);
            if (song) {
                searchKeyword = song.search_keyword || null;
                platform = song.platform || null;
            }
        }
        
        // 如果是当前播放的歌曲
        if (this.state.currentSong && this.state.currentSong.id === songId) {
            searchKeyword = this.state.currentSong.search_keyword || null;
            platform = this.state.currentSong.platform || null;
        }
        
        // 从每日推荐歌曲中查找
        if ((!searchKeyword || !platform) && window.dailyRecommendSongs && Array.isArray(window.dailyRecommendSongs) && window.dailyRecommendSongs.length > 0) {
            const recommendSong = window.dailyRecommendSongs.find(s => s.id === songId);
            if (recommendSong) {
                debug("找到每日推荐歌曲:", recommendSong);
                debug("每日推荐歌曲字段:", Object.keys(recommendSong).join(", "));
                searchKeyword = recommendSong.search_keyword || "每日推荐";
                platform = recommendSong.platform || "NETEASE";
                debug("从每日推荐歌曲中获取信息:", searchKeyword, platform);
            }
        }
        
        if (this.state.favorites.has(songId)) {
            this.state.favorites.delete(songId);
        } else {
            this.state.favorites.add(songId);
        }
        
        // 获取完整的歌曲数据
        let songData = null;
        
        // 从各个可能的来源获取完整的歌曲数据
        if (this.searchResults && this.searchResults.length > 0) {
            const song = this.searchResults.find(s => s.id === songId);
            if (song) {
                debug("从搜索结果中找到歌曲:", song.name || song.title);
                songData = song;
            }
        }
        
        if (!songData && this.state.currentSong && this.state.currentSong.id === songId) {
            debug("使用当前播放的歌曲:", this.state.currentSong.name || this.state.currentSong.title);
            songData = this.state.currentSong;
        }
        
        // 从每日推荐歌曲中查找
        if (!songData && window.dailyRecommendSongs && Array.isArray(window.dailyRecommendSongs)) {
            const recommendSong = window.dailyRecommendSongs.find(s => s.id === songId);
            if (recommendSong) {
                debug("从每日推荐中找到歌曲:", recommendSong.name || recommendSong.title);
                songData = {
                    ...recommendSong,
                    search_keyword: recommendSong.search_keyword || "每日推荐",
                    platform: recommendSong.platform || "NETEASE"
                };
            }
        }
        
        // 从全局热门歌曲中查找
        if (!songData && window.hotSongs && Array.isArray(window.hotSongs)) {
            const hotSong = window.hotSongs.find(s => s.id === songId);
            if (hotSong) {
                debug("从热门歌曲中找到歌曲:", hotSong.name || hotSong.title);
                songData = hotSong;
            }
        }
        
        // 如果找到了歌曲数据，确保它有必要的字段
        if (songData) {
            debug("找到歌曲数据:", songData);
            // 确保有搜索关键词和平台信息
            if (!songData.search_keyword) {
                songData.search_keyword = searchKeyword || "每日推荐";
            }
            if (!songData.platform) {
                songData.platform = platform || "NETEASE";
            }
        } else {
            debug("未找到歌曲数据，创建基本数据");
            // 如果没有找到歌曲数据，创建一个基本的数据对象
            songData = {
                id: songId,
                search_keyword: searchKeyword || "每日推荐",
                platform: platform || "NETEASE"
            };
        }
        
        // 发送请求到服务器
        fetch(`https://${GetParentResourceName()}/toggleFavoriteSong`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                songId: songId,
                isFavorite: this.state.favorites.has(songId),
                searchKeyword: searchKeyword,
                platform: platform,
                songData: songData // 传递完整的歌曲数据
            })
        }).then(response => response.json())
        .catch(error => {
            console.error('收藏歌曲请求失败:', error);
        });
        
        this.updateFavoritesCount();
        this.updatePlayerInfo();
        this.state.saveToStorage();
        
        // 刷新UI显示
        const isFavorite = this.state.favorites.has(songId);
        
        // 更新歌曲列表中的所有该歌曲的收藏图标
        const songButtons = document.querySelectorAll(`.song-action-btn[onclick="player.toggleSongFavorite(${songId})"]`);
        songButtons.forEach(button => {
            const icon = button.querySelector('i');
            if (icon) {
                if (isFavorite) {
                    icon.className = "fas fa-heart"; // 实心
                } else {
                    icon.className = "far fa-heart"; // 空心
                }
            }
        });
        
        // 显示提示信息
        this.showToast(isFavorite ? "已添加到我的收藏" : "已从收藏中移除", 1500);
        
        // 如果当前在收藏视图，刷新数据
        if (this.state.currentView === 'favorites') {
            this.loadFavoritesData();
        }
    }
    
    // ==================== 进度条和音量控制 ====================
    setupProgressBar() {
        const progressBar = document.querySelector('.progress-track');
        const progressHandle = document.getElementById('progressHandle');
        
        if (!progressBar || !progressHandle) return;
        
        const updateProgress = (e) => {
            // 计算点击位置相对于进度条的百分比
            const rect = progressBar.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const width = rect.width;
            const percentage = Math.min(Math.max(x / width, 0), 1);
            
            // 计算对应的时间
            const duration = this.state.duration || 180;
            const newTime = percentage * duration;
            
            // 设置播放位置
            this.setTimeStamp(newTime);
        };
        
        // 点击进度条直接跳转
        progressBar.addEventListener('click', updateProgress);
        
        // 拖动进度条
        let isDragging = false;
        
        progressHandle.addEventListener('mousedown', (e) => {
            isDragging = true;
            e.preventDefault(); // 防止选中文本
            
            // 添加临时事件监听器
            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', stopDrag);
        });
        
        const handleDrag = (e) => {
            if (!isDragging) return;
                updateProgress(e);
        };
        
        const stopDrag = () => {
            isDragging = false;
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', stopDrag);
        };
    }
    
    // 增强setupVolumeControl方法，添加拖动功能
    setupVolumeControl() {
        const volumeBar = document.querySelector('.volume-track');
        const volumeHandle = document.getElementById('volumeHandle');
        const volumeBtn = document.getElementById('volumeBtn');
        
        if (!volumeBar || !volumeHandle) return;
        
        const updateVolume = (e) => {
            // 计算点击位置相对于音量条的百分比
            const rect = volumeBar.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const width = rect.width;
            const percentage = Math.min(Math.max(x / width, 0), 1);
            
            // 设置音量
            this.setVolume(percentage);
        };
        
        // 点击音量条直接设置
        volumeBar.addEventListener('click', updateVolume);
        
        // 拖动音量条
        let isDragging = false;
        
        volumeHandle.addEventListener('mousedown', (e) => {
            isDragging = true;
            e.preventDefault(); // 防止选中文本
            
            // 添加临时事件监听器
            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', stopDrag);
        });
        
        const handleDrag = (e) => {
            if (!isDragging) return;
                updateVolume(e);
        };
        
        const stopDrag = () => {
            isDragging = false;
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', stopDrag);
        };
        
        // 音量按钮静音切换
        if (volumeBtn) {
            volumeBtn.addEventListener('click', () => {
                this.toggleMute();
            });
        }
    }
    
    // 切换静音状态
    toggleMute() {
        debug("切换静音状态，当前状态:", this.state.isMuted ? "已静音" : "未静音");
        this.state.isMuted = !this.state.isMuted;
        
        // 更新音频元素的静音状态（如果存在）
        if (this.audio) {
            this.audio.muted = this.state.isMuted;
        }
        
        // 如果静音，发送音量0；如果取消静音，发送保存的音量
        const volume = this.state.isMuted ? 0 : this.state.volume;
        
        // 更新音量显示
        this.updateVolumeDisplay();
        
        // 保存静音状态到存储
        this.state.saveToStorage();
        
        // 显示提示
        this.showToast(this.state.isMuted ? "已静音" : "已恢复音量", 1500);
        
        // 发送音量设置到服务器 - 添加重试机制
        const sendVolumeToServer = (retryCount = 0) => {
            fetch(`https://${GetParentResourceName()}/setVolume`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    volume: volume
                })
            }).then(response => response.json())
            .then(data => {
                debug("静音状态设置成功:", data);
            })
            .catch(error => {
                console.error("静音状态设置失败:", error);
                // 如果失败，最多重试3次
                if (retryCount < 3) {
                    debug(`静音状态设置重试 (${retryCount + 1}/3)...`);
                    setTimeout(() => sendVolumeToServer(retryCount + 1), 300);
                }
            });
        };
        
        // 立即发送一次
        sendVolumeToServer();
        
        // 0.5秒后再次发送，确保设置生效
        setTimeout(() => {
            debug("再次发送静音状态以确保生效:", volume);
            sendVolumeToServer();
        }, 500);
    }
    
    // ==================== 搜索功能 ====================
    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        const performSearch = () => {
            const query = searchInput.value.trim();
            if (query) {
                this.searchSongs(query);
                this.switchView('search');
            }
        };
        
        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // 热门搜索词点击
        document.querySelectorAll('.hot-word').forEach(word => {
            word.addEventListener('click', () => {
                searchInput.value = word.textContent;
                performSearch();
            });
        });
    }
    
    searchSongs(query) {
        if (!query || query.trim() === '') return;
        
        // 防抖处理
        if (this._searchTimeout) {
            clearTimeout(this._searchTimeout);
        }
        
        this._searchTimeout = setTimeout(() => {
            const searchInput = document.getElementById('searchInput');
            searchInput.value = query;
            
            // 保存当前搜索关键词，用于后续添加到收藏或歌单
            this._currentSearchKeyword = query;
            
            // 显示加载状态
            const resultsContainer = document.getElementById('searchResults');
            resultsContainer.innerHTML = this.renderEmptyState('spinner', '搜索中...', `正在搜索"${query}"，请稍候...`);
            
            // 发送搜索请求到后端
            // 确保平台代码是大写的，与HTML中的选项值保持一致
            const platform = this.currentPlatform.toUpperCase();
            debug('搜索音乐:', query, '平台:', platform);
            
            fetch(`https://${GetParentResourceName()}/searchMusic`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    keyword: query,
                    platform: platform,
                    limit: 20
                })
            }).then(response => {
                // 确保响应是有效的JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    throw new Error('返回的不是JSON数据');
                }
            })
            .then(data => {
                debug('搜索请求已发送:', data);
            })
            .catch(error => {
                console.error('搜索请求失败:', error);
                resultsContainer.innerHTML = this.renderEmptyState('exclamation-triangle', '搜索失败', `请求失败: ${error.message}`);
            });
        }, 300); // 300毫秒的防抖延迟
    }
    
    // ==================== 播放列表控制 ====================
    togglePlaylistPanel() {
        const panel = document.getElementById('playlistPanel');
        panel.classList.toggle('active');
        
        // 如果面板被激活（显示），更新播放列表
        if (panel.classList.contains('active')) {
            this.updateCurrentPlaylist();
        }
    }
    
    closePlaylistPanel() {
        const panel = document.getElementById('playlistPanel');
        panel.classList.remove('active');
    }
    
    playFromPlaylist(index) {
        this.state.currentIndex = index;
        const songId = this.state.currentPlaylist[index];
        this.playSong(songId);
    }
    
    clearPlaylist() {
        debug('清空播放列表，之前的长度:', this.state.currentPlaylist.length);
        this.state.currentPlaylist = [];
        this.state.currentIndex = -1;
        this.updateCurrentPlaylist();
        debug('播放列表已清空');
    }
    
    addToPlaylist(songId) {
        if (!this.state.currentPlaylist.includes(songId)) {
            // 查找歌曲的详细信息
            let songInfo = null;
            
            // 从当前播放的歌曲中查找
            if (this.state.currentSong && this.state.currentSong.id === songId) {
                songInfo = {...this.state.currentSong};
            }
            
            // 从搜索结果中查找
            if (!songInfo && this.searchResults && this.searchResults.length > 0) {
                const searchSong = this.searchResults.find(s => s.id === songId);
                if (searchSong) {
                    songInfo = {...searchSong};
                }
            }
            
            // 从热门歌曲中查找
            if (!songInfo && window.hotSongs && Array.isArray(window.hotSongs) && window.hotSongs.length > 0) {
                const hotSong = window.hotSongs.find(s => s.id === songId);
                if (hotSong) {
                    songInfo = {...hotSong};
                }
            }
            
            // 从每日推荐歌曲中查找
            if (!songInfo && window.dailyRecommendSongs && Array.isArray(window.dailyRecommendSongs)) {
                // 检查ID类型，可能需要转换
                if (typeof songId === 'string' && !isNaN(parseInt(songId))) {
                    // 如果songId是数字字符串，尝试转换为数字
                    const numId = parseInt(songId);
                    
                    // 先用字符串ID查找
                    let recommendSong = window.dailyRecommendSongs.find(s => s.id === songId);
                    
                    // 如果没找到，尝试用数字ID查找
                    if (!recommendSong) {
                        recommendSong = window.dailyRecommendSongs.find(s => s.id === numId);
                    }
                    
                    // 如果还没找到，尝试更宽松的比较
                    if (!recommendSong) {
                        recommendSong = window.dailyRecommendSongs.find(s => {
                            return String(s.id) === String(songId);
                        });
                    }
                    
                    if (recommendSong) {
                        debug("从每日推荐歌曲中找到信息:", recommendSong.name || recommendSong.title);
                        // 确保字段名称正确
                        songInfo = {
                            ...recommendSong,
                            // 确保标题和歌手字段正确
                            title: recommendSong.title || recommendSong.name || `歌曲 ${songId}`,
                            artist: recommendSong.artist || recommendSong.singer || "未知歌手",
                            // 确保有搜索关键词和平台信息
                            search_keyword: recommendSong.search_keyword || "每日推荐",
                            platform: recommendSong.platform || "NETEASE"
                        };
                    }
                } else {
                    // 正常查找
                    const recommendSong = window.dailyRecommendSongs.find(s => s.id === songId);
                    if (recommendSong) {
                        debug("从每日推荐歌曲中找到信息:", recommendSong.name || recommendSong.title);
                        // 确保字段名称正确
                        songInfo = {
                            ...recommendSong,
                            // 确保标题和歌手字段正确
                            title: recommendSong.title || recommendSong.name || `歌曲 ${songId}`,
                            artist: recommendSong.artist || recommendSong.singer || "未知歌手",
                            // 确保有搜索关键词和平台信息
                            search_keyword: recommendSong.search_keyword || "每日推荐",
                            platform: recommendSong.platform || "NETEASE"
                        };
                    }
                }
            }
            
            // 从歌单歌曲中查找
            if (!songInfo && window.playlistSongs && window.playlistSongs.length > 0) {
                const playlistSong = window.playlistSongs.find(s => s.id === songId);
                if (playlistSong) {
                    songInfo = {...playlistSong};
                }
            }
            
            // 如果还是找不到，创建一个基本信息
            if (!songInfo) {
                songInfo = {
                    id: songId,
                    title: `歌曲 ${songId}`,
                    artist: '未知歌手',
                    cover: `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${songId}`,
                    duration: '0:00'
                };
            }
            
            // 确保有songInfoList数组用于存储完整歌曲信息
            if (!this.state.songInfoList) {
                this.state.songInfoList = [];
            }
            
            // 移除详细信息中已存在的记录
            this.state.songInfoList = this.state.songInfoList.filter(song => song.id !== songId);
            // 添加详细信息
            this.state.songInfoList.push(songInfo);
            
            // 根据添加方式决定添加位置
            if (window._isAddingToPlaylist) {
                // 通过右键菜单"添加到播放列表"：添加到末尾，不改变当前播放
                this.state.currentPlaylist.push(songId);
                debug("歌曲已添加到播放列表末尾:", songInfo.title || songInfo.name);
            } else {
                // 直接点击播放：添加到顶部，准备播放
                this.state.currentPlaylist.unshift(songId);
                debug("歌曲已添加到播放列表顶部:", songInfo.title || songInfo.name);
            }

            this.updateCurrentPlaylist();
        }
    }
    
    // ==================== 歌单管理 ====================
    setupModal() {
        const overlay = document.getElementById('modalOverlay');
        const modal = document.getElementById('createPlaylistModal');
        
        if (!modal) {
            console.error('创建歌单模态框不存在');
            return;
        }
        
        // 关闭模态框
        const closeModal = () => {
            overlay.classList.remove('active');
        };
        
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                closeModal();
            }
        });
        
        const closeBtn = modal.querySelector('.btn-close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeModal);
        }
        
        const cancelBtn = modal.querySelector('.btn-cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', closeModal);
        }
        
        // 创建歌单确认
        const confirmBtn = document.getElementById('confirmCreatePlaylist');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.createPlaylist();
                closeModal();
            });
        }
    }
    
    showCreatePlaylistModal() {
        const overlay = document.getElementById('modalOverlay');
        
        // 清除所有现有模态框，确保不会出现重叠
        const existingModals = overlay.querySelectorAll('.modal:not(#createPlaylistModal)');
        existingModals.forEach(modal => {
            modal.remove();
        });
        
        // 确保创建歌单的模态框存在
        let createModal = document.getElementById('createPlaylistModal');
        if (!createModal) {
            createModal = document.createElement('div');
            createModal.id = 'createPlaylistModal';
            createModal.className = 'modal';
            createModal.innerHTML = `
                <div class="modal-header">
                    <h3>创建歌单</h3>
                    <button class="btn-close-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="playlistNameInput">歌单名称</label>
                        <input type="text" id="playlistNameInput" placeholder="请输入歌单名称">
                    </div>
                    <div class="form-group">
                        <label for="playlistDescInput">描述（选填）</label>
                        <textarea id="playlistDescInput" placeholder="请输入歌单描述"></textarea>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-cancel">取消</button>
                    <button id="confirmCreatePlaylist" class="btn-confirm">创建</button>
                </div>
            `;
            overlay.appendChild(createModal);
            this.setupModal();  // 重新设置模态框事件
        }
        
        const nameInput = document.getElementById('playlistNameInput');
        const descInput = document.getElementById('playlistDescInput');
        
        nameInput.value = '';
        descInput.value = '';
        overlay.classList.add('active');
        nameInput.focus();
    }
    
    createPlaylist() {
        const name = document.getElementById('playlistNameInput').value.trim();
        const description = document.getElementById('playlistDescInput').value.trim();
        
        if (!name) {
            alert('请输入歌单名称');
            return;
        }
        
        // 向服务器发送创建歌单请求
        fetch(`https://${GetParentResourceName()}/createPlaylist`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: name,
                description: description
            })
        })
        .then(resp => resp.json())
        .then(resp => {
            debug('歌单创建响应:', resp);
            
            if (resp.status === 'success') {
                debug('创建歌单成功:', resp.playlistId);
                
                // 更新本地状态
                const playlist = {
                    id: resp.playlistId,
                    name: name,
                    description: description,
                    songs: [],
                    created: new Date().toISOString(),
                    cover: `https://via.placeholder.com/180x180/ff6b6b/fff?text=${encodeURIComponent(name.charAt(0))}`
                };
                
                // 添加到本地数据
                this.state.playlists.push(playlist);
                this.state.saveToStorage();
                
                // 更新UI
                this.updateSidebarPlaylists();
                
                // 显示成功消息
                this.showToast('歌单创建成功');
                
                // 更新歌单计数
                document.querySelector('.nav-item[data-view="playlists"] .count').textContent = this.state.playlists.length;
                
                // 刷新歌单视图
                if (this.state.currentView === 'playlists') {
                    this.loadPlaylistsData();
                }
                
                // 关闭模态框
                document.getElementById('modalOverlay').classList.remove('active');
                
                // 检查是否有待添加的歌曲ID
                if (this.pendingAddSongId) {
                    const songId = this.pendingAddSongId;
                    const searchKeyword = this.pendingAddSongKeyword;
                    const platform = this.pendingAddSongPlatform;
                    
                    // 清除待添加的歌曲信息
                    this.pendingAddSongId = null;
                    this.pendingAddSongKeyword = null;
                    this.pendingAddSongPlatform = null;
                    
                    // 添加歌曲到新创建的歌单
                    fetch(`https://${GetParentResourceName()}/addSongToPlaylist`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            playlistId: resp.playlistId,
                            songId: songId,
                            searchKeyword: searchKeyword,
                            platform: platform
                        })
                    }).then(response => response.json())
                    .then(resp => {
                        if (resp.status === 'success') {
                            // 本地更新
                            if (!playlist.songs.includes(songId)) {
                                playlist.songs.push(songId);
                                playlist.songCount = playlist.songs.length; // 更新歌曲数量
                            }
                            this.state.saveToStorage();
                            
                            // 更新侧边栏歌单列表
                            this.updateSidebarPlaylists();
                            
                            // 提示成功
                            this.showToast('已添加到歌单');
                        } else {
                            this.showToast('添加失败: ' + (resp.message || '未知错误'));
                        }
                    })
                    .catch(error => {
                        console.error('添加歌曲到歌单失败:', error);
                        this.showToast('添加失败，请重试');
                    });
                }
            } else {
                console.error('创建歌单失败:', resp.message);
                alert(`创建歌单失败: ${resp.message || '未知错误'}`);
            }
        })
        .catch(error => {
            console.error('创建歌单请求失败:', error);
            alert('创建歌单请求失败，请重试');
        });
    }
    
    updateSidebarPlaylists() {
        const container = document.getElementById('playlistsList');
        container.innerHTML = this.state.playlists.map(playlist => `
            <li class="playlist-item" data-playlist-id="${playlist.id}">
                <i class="fas fa-music"></i>
                <div class="playlist-item-name">${playlist.name}</div>
                <span class="count">${playlist.songCount !== undefined ? playlist.songCount : playlist.songs.length}</span>
            </li>
        `).join('');
        
        // 添加点击事件
        document.querySelectorAll('.playlist-item').forEach(item => {
            item.addEventListener('click', () => {
                const playlistId = item.dataset.playlistId;
                // 切换活动状态
                document.querySelectorAll('.playlist-item').forEach(el => el.classList.remove('active'));
                item.classList.add('active');
                
                // 加载歌单歌曲并显示内容
                this.loadPlaylistSongs(playlistId);
            });
        });
    }
    
    // ==================== 右键菜单 ====================
    setupContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        let currentSongId = null;
        
        // 显示右键菜单
        window.showContextMenu = (e, songId) => {
            e.preventDefault();
            e.stopPropagation(); // 阻止事件冒泡
            currentSongId = songId;
            
            // 获取app元素的位置和偏移
            const app = document.getElementById('app');
            const appRect = app.getBoundingClientRect();
            
            // 计算鼠标点击位置相对于app的位置
            const relativeX = e.clientX - appRect.left;
            const relativeY = e.clientY - appRect.top;
            
            // 先将菜单添加到活动状态，但保持不可见
            contextMenu.classList.add('active');
            contextMenu.style.visibility = 'hidden';
            contextMenu.style.display = 'block';
            
            // 等待一个短暂的延迟，确保菜单已经渲染出来，这样才能获取正确的尺寸
                setTimeout(() => {
                // 获取菜单尺寸
                const menuWidth = contextMenu.offsetWidth || 180;
                const menuHeight = contextMenu.offsetHeight || 200;
                
                // 计算菜单位置，精确定位在鼠标位置（相对于app）
                let left = relativeX;
                let top = relativeY;
                
                // 如果右侧放不下，则放在左侧
                if (left + menuWidth > appRect.width) {
                    left = relativeX - menuWidth;
                }
                
                // 如果底部放不下，则向上显示
                if (top + menuHeight > appRect.height) {
                    top = relativeY - menuHeight;
                }
                
                // 确保不超出app边界
                left = Math.max(5, Math.min(left, appRect.width - menuWidth - 5));
                top = Math.max(5, Math.min(top, appRect.height - menuHeight - 5));
                
                // 设置菜单位置
                contextMenu.style.left = left + 'px';
                contextMenu.style.top = top + 'px';
                
                // 显示菜单
                contextMenu.style.visibility = 'visible';
            }, 50); // 增加延迟时间确保DOM更新
        };
        
        // 隐藏右键菜单
        document.addEventListener('click', (e) => {
            // 如果点击的不是菜单本身，则隐藏菜单
            if (!e.target.closest('#contextMenu')) {
                contextMenu.classList.remove('active');
                contextMenu.style.visibility = 'hidden';
            }
        });
        
        // 按下ESC键也隐藏菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                contextMenu.classList.remove('active');
                contextMenu.style.visibility = 'hidden';
            }
        });
        
        // 右键菜单项点击
        contextMenu.addEventListener('click', (e) => {
            const action = e.target.closest('li')?.dataset.action;
            if (action && currentSongId) {
                this.handleContextMenuAction(action, currentSongId);
            }
            contextMenu.classList.remove('active');
            contextMenu.style.visibility = 'hidden';
        });
    }
    
    showContextMenu(e, songId) {
        window.showContextMenu(e, songId);
    }
    
    handleContextMenuAction(action, songId) {
        debug(`执行右键菜单操作: ${action}, 歌曲ID: ${songId}`);
        
        switch (action) {
            case 'play':
                this.playSong(songId);
                break;
            case 'next':
                // 将歌曲添加到播放列表，直接调用addToPlaylist方法
                debug("添加歌曲到播放列表:", songId);
                
                // 设置标记，表示正在通过右键菜单添加歌曲到播放列表
                window._isAddingToPlaylist = true;

                // 先尝试获取完整的歌曲信息
                let songInfo = this.getSongInfo(songId);
                if (songInfo) {
                    debug("找到歌曲信息:", songInfo.title || songInfo.name);

                    // 确保songInfoList存在
                    if (!this.state.songInfoList) {
                        this.state.songInfoList = [];
                    }

                    // 添加或更新歌曲信息
                    const existingIndex = this.state.songInfoList.findIndex(s => s.id === songId);
                    if (existingIndex >= 0) {
                        this.state.songInfoList[existingIndex] = songInfo;
                    } else {
                        this.state.songInfoList.push(songInfo);
                    }
                }

                // 直接添加到播放列表，不播放
                this.addToPlaylist(songId);

                // 操作完成后清除标记
                setTimeout(() => {
                    window._isAddingToPlaylist = false;
                }, 500); // 增加到500毫秒，确保操作完成
                break;
            case 'favorite':
                this.toggleSongFavorite(songId);
                break;
            case 'playlist':
                this.showAddToPlaylistDialog(songId);
                break;
            case 'download':
                this.downloadSong(songId);
                break;
        }
    }
    
    // 获取歌曲信息的辅助方法
    getSongInfo(songId) {
        // 从各个可能的来源查找歌曲信息
        let songInfo = null;
        
        // 从当前播放的歌曲中查找
        if (this.state.currentSong && this.state.currentSong.id === songId) {
            songInfo = {...this.state.currentSong};
            debug("从当前播放歌曲获取信息");
        }
        
        // 从搜索结果中查找
        if (!songInfo && this.searchResults && this.searchResults.length > 0) {
            const searchSong = this.searchResults.find(s => s.id === songId);
            if (searchSong) {
                songInfo = {...searchSong};
                debug("从搜索结果获取歌曲信息");
            }
        }
        
        // 从热门歌曲中查找
        if (!songInfo && window.hotSongs && Array.isArray(window.hotSongs) && window.hotSongs.length > 0) {
            const hotSong = window.hotSongs.find(s => s.id === songId);
            if (hotSong) {
                songInfo = {...hotSong};
                debug("从热门歌曲获取歌曲信息");
            }
        }
        
        // 从每日推荐歌曲中查找
        if (!songInfo && window.dailyRecommendSongs && Array.isArray(window.dailyRecommendSongs)) {
            debug("查找每日推荐歌曲，ID:", songId, "推荐歌曲数量:", window.dailyRecommendSongs.length);
            
            // 打印前几首歌曲的ID，帮助调试
            window.dailyRecommendSongs.slice(0, 3).forEach((s, i) => {
                debug(`每日推荐歌曲 #${i+1} ID:`, s.id, "类型:", typeof s.id);
            });
            
            // 检查ID类型，可能需要转换
            let targetId = songId;
            if (typeof songId === 'string' && !isNaN(parseInt(songId))) {
                // 如果songId是数字字符串，尝试转换为数字
                const numId = parseInt(songId);
                debug("尝试将ID转换为数字:", numId);
                
                // 先用字符串ID查找
                let recommendSong = window.dailyRecommendSongs.find(s => s.id === songId);
                
                // 如果没找到，尝试用数字ID查找
                if (!recommendSong) {
                    recommendSong = window.dailyRecommendSongs.find(s => s.id === numId);
                    if (recommendSong) {
                        debug("使用数字ID找到了歌曲");
                    }
                }
                
                // 如果还没找到，尝试更宽松的比较
                if (!recommendSong) {
                    recommendSong = window.dailyRecommendSongs.find(s => {
                        return String(s.id) === String(songId);
                    });
                    if (recommendSong) {
                        debug("使用字符串比较找到了歌曲");
                    }
                }
                
                if (recommendSong) {
                    debug("从每日推荐获取歌曲信息:", recommendSong.name || recommendSong.title);
                    songInfo = {
                        ...recommendSong,
                        title: recommendSong.title || recommendSong.name || `歌曲 ${songId}`,
                        artist: recommendSong.artist || recommendSong.singer || "未知歌手",
                        search_keyword: recommendSong.search_keyword || "每日推荐",
                        platform: recommendSong.platform || "NETEASE"
                    };
                }
            } else {
                // 正常查找
                const recommendSong = window.dailyRecommendSongs.find(s => s.id === songId);
                if (recommendSong) {
                    debug("从每日推荐获取歌曲信息:", recommendSong.name || recommendSong.title);
                    songInfo = {
                        ...recommendSong,
                        title: recommendSong.title || recommendSong.name || `歌曲 ${songId}`,
                        artist: recommendSong.artist || recommendSong.singer || "未知歌手",
                        search_keyword: recommendSong.search_keyword || "每日推荐",
                        platform: recommendSong.platform || "NETEASE"
                    };
                }
            }
        }
        
        // 从歌单歌曲中查找
        if (!songInfo && window.playlistSongs && window.playlistSongs.length > 0) {
            const playlistSong = window.playlistSongs.find(s => s.id === songId);
            if (playlistSong) {
                songInfo = {...playlistSong};
                debug("从歌单获取歌曲信息");
            }
        }
        
        // 如果还是找不到，创建一个基本信息
        if (!songInfo) {
            songInfo = {
                id: songId,
                title: `歌曲 ${songId}`,
                artist: '未知歌手',
                cover: `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${songId}`,
                duration: '0:00',
                search_keyword: "每日推荐",
                platform: "NETEASE"
            };
            debug("未找到歌曲详情，创建基本信息");
            
            // 尝试从服务器获取歌曲信息
            fetch(`https://${GetParentResourceName()}/getSongInfo`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    songId: songId,
                    platform: "NETEASE"
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.status === 'success' && data.song) {
                    debug("从服务器获取到歌曲信息:", data.song);
                    // 更新songInfoList中的歌曲信息
                    if (!this.state.songInfoList) {
                        this.state.songInfoList = [];
                    }
                    this.state.songInfoList = this.state.songInfoList.filter(s => s.id !== songId);
                    this.state.songInfoList.unshift(data.song);
                    this.state.saveToStorage();
                }
            })
            .catch(error => {
                console.error("获取歌曲信息失败:", error);
            });
        }
        
        return songInfo;
    }
    
    showAddToPlaylistDialog(songId) {
        // 获取当前歌曲的搜索关键词和平台信息
        let searchKeyword = null;
        let platform = null;
        
        // 尝试从搜索结果中找到对应的歌曲
        if (this.searchResults && this.searchResults.length > 0) {
            const song = this.searchResults.find(s => s.id === songId);
            if (song) {
                searchKeyword = song.search_keyword || null;
                platform = song.platform || null;
            }
        }
        
        // 如果是当前播放的歌曲
        if (this.state.currentSong && this.state.currentSong.id === songId) {
            searchKeyword = this.state.currentSong.search_keyword || null;
            platform = this.state.currentSong.platform || null;
        }
        
        // 创建歌单选择对话框
        const modalOverlay = document.getElementById('modalOverlay');
        
        // 确保先清除所有可能存在的模态框内容
        // 先隐藏overlay，避免闪烁
        modalOverlay.classList.remove('active');
        
        // 删除所有现有的模态框
        const existingModals = modalOverlay.querySelectorAll('.modal');
        existingModals.forEach(modal => {
            modal.remove();
        });
        
        // 创建新的选择歌单模态框
        const modalContent = document.createElement('div');
        modalContent.id = 'selectPlaylistModal';
        modalContent.className = 'modal';
        modalOverlay.appendChild(modalContent);
        
        // 替换为歌单选择内容
        modalContent.innerHTML = `
            <div class="modal-header">
                <h3>选择歌单</h3>
                <button class="btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="playlist-select-list">
                    <!-- 添加创建新歌单选项 -->
                    <div class="playlist-select-item create-new-playlist">
                        <div class="playlist-select-name">
                            <i class="fas fa-plus"></i> 创建新歌单
                        </div>
                    </div>
                    ${this.state.playlists.map(p => `
                        <div class="playlist-select-item" data-id="${p.id}">
                            <div class="playlist-select-name">${p.name}</div>
                            <div class="playlist-select-count">${p.songCount !== undefined ? p.songCount : (p.songs ? p.songs.length : 0)}首</div>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn-cancel">取消</button>
            </div>
        `;
        
        // 显示模态框
        modalOverlay.classList.add('active');
        
        // 创建新歌单选项点击事件
        document.querySelector('.create-new-playlist').addEventListener('click', () => {
            // 保存要添加的歌曲ID，以便创建歌单后自动添加
            this.pendingAddSongId = songId;
            this.pendingAddSongKeyword = searchKeyword;
            this.pendingAddSongPlatform = platform;
            
            // 完全移除当前模态框
            modalContent.remove();
            
            // 显示创建歌单模态框
            this.showCreatePlaylistModal();
        });
        
        // 添加点击事件
        document.querySelectorAll('.playlist-select-item:not(.create-new-playlist)').forEach(item => {
            item.addEventListener('click', () => {
                const playlistId = item.dataset.id;
                
                // 添加歌曲到歌单
                fetch(`https://${GetParentResourceName()}/addSongToPlaylist`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        playlistId: playlistId,
                        songId: songId,
                        searchKeyword: searchKeyword,
                        platform: platform
                    })
                }).then(response => response.json())
                .then(resp => {
                    if (resp.status === 'success') {
                        // 本地更新
                        const playlist = this.state.playlists.find(p => p.id == playlistId);
                        if (playlist) {
                            if (!playlist.songs) playlist.songs = [];
                            if (!playlist.songs.includes(songId)) {
                                playlist.songs.push(songId);
                                playlist.songCount = playlist.songs.length; // 更新歌曲数量
                            }
                            this.state.saveToStorage();
                        }
                        
                        // 关闭模态框并彻底移除
                        modalOverlay.classList.remove('active');
                        modalContent.remove();
                        
                        // 更新侧边栏歌单列表
                        this.updateSidebarPlaylists();
                        
                        // 提示成功
                        this.showToast('已添加到歌单');
                    } else {
                        this.showToast('添加失败: ' + (resp.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('添加歌曲到歌单失败:', error);
                    this.showToast('添加失败，请重试');
                });
            });
        });
        
        // 取消按钮
        document.querySelector('.btn-cancel').addEventListener('click', () => {
            modalOverlay.classList.remove('active');
            modalContent.remove();
        });
        
        // 关闭按钮
        document.querySelector('.btn-close-modal').addEventListener('click', () => {
            modalOverlay.classList.remove('active');
            modalContent.remove();
        });
    }
    
    // 显示提示信息
    showToast(message, duration = 2000) {
        // 创建或获取toast元素
        let toast = document.getElementById('toast');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'toast';
            document.body.appendChild(toast);
        }
        
        // 设置消息并显示
        toast.textContent = message;
        toast.classList.add('show');
        
        // 定时隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, duration);
    }
    
    downloadSong(songId) {
        const song = this.mockSongs.find(s => s.id === songId);
        if (song) {
            // 模拟下载
            alert(`开始下载: ${song.title} - ${song.artist}`);
        }
    }
    
    // ==================== 轮播图控制 ====================
    setupBannerControls() {
        const dots = document.querySelectorAll('.dot');
        dots.forEach(dot => {
            dot.addEventListener('click', () => {
                const slideIndex = parseInt(dot.dataset.slide);
                this.showBannerSlide(slideIndex);
            });
        });
    }
    
    showBannerSlide(index) {
        const slides = document.querySelectorAll('.banner-slide');
        const dots = document.querySelectorAll('.dot');
        
        slides.forEach((slide, i) => {
            slide.classList.toggle('active', i === index);
        });
        
        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
        });
    }
    
    startBannerCarousel() {
        let currentSlide = 0;
        const totalSlides = document.querySelectorAll('.banner-slide').length;
        
        setInterval(() => {
            currentSlide = (currentSlide + 1) % totalSlides;
            this.showBannerSlide(currentSlide);
        }, 5000);
    }
    
    // ==================== 排行榜标签 ====================
    setupRankingTabs() {
        document.querySelectorAll('.ranking-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // 更新标签状态
                document.querySelectorAll('.ranking-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // 加载对应排行榜数据
                const rankingType = tab.dataset.ranking;
                this.loadRankingByType(rankingType);
            });
        });
    }
    
    loadRankingByType(type) {
        const container = document.getElementById('rankingList');
        container.innerHTML = this.renderEmptyState('spinner', '加载中...', '正在加载排行榜数据...');
        
        // 定义每种类型对应的搜索关键词
        const rankingKeywords = {
            'hot': '热歌榜',
            'new': '新歌榜',
            'original': '说唱榜'
        };
        
        const keyword = rankingKeywords[type] || '热歌榜';
        
        // 如果已经有缓存的排行榜数据，直接使用
        if (this.rankingSongs[type] && this.rankingSongs[type].length > 0) {
            debug(`使用缓存的${keyword}数据，共${this.rankingSongs[type].length}首歌曲`);
            container.innerHTML = this.renderSongList(this.rankingSongs[type]);
            return;
        }
        
        // 从服务器获取排行榜数据
        fetch(`https://${GetParentResourceName()}/triggerServerEvent`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                eventName: 'getRankingSongs',
                args: {  // 修改为直接传递对象，不使用数组包装
                    keyword: keyword,
                    platform: this.currentPlatform,
                    type: type,
                    source: true  // 添加source标记，表明需要服务器处理source
                }
            })
        }).catch(error => {
            console.error(`请求${keyword}失败:`, error);
            container.innerHTML = this.renderEmptyState('exclamation-triangle', '获取失败', `无法连接到音乐服务`);
            
            // 使用模拟数据作为备选
            setTimeout(() => {
                this.useRankingMockData(type, container);
            }, 1000);
        });
        
        // 设置超时，如果10秒内没有收到响应，则使用模拟数据
        setTimeout(() => {
            if (container.querySelector('.empty-state')) {
                debug(`${keyword}请求超时，使用模拟数据`);
                this.useRankingMockData(type, container);
            }
        }, 10000);
    }
    
    // 增加一个辅助方法，用于生成排行榜模拟数据
    useRankingMockData(type, container) {
        let songs = [...this.mockSongs];
        
        switch (type) {
            case 'hot':
                // 热歌榜 - 保持原顺序
                break;
            case 'new':
                // 新歌榜 - 反向排序模拟新歌
                songs = songs.reverse();
                break;
            case 'original':
                // 原创榜 - 随机排序
                songs = songs.sort(() => Math.random() - 0.5);
                break;
        }
        
        container.innerHTML = this.renderSongList(songs);
    }
    
    // ==================== 添加NUI消息监听器 ====================
    setupMessageListener() {
        // 确保只注册一次消息监听器
        if (window._messageListenerRegistered) {
            debug('消息监听器已注册，跳过');
            return;
        }
        
        window.addEventListener('message', (event) => {
            const data = event.data;
            
            if (data.action === "show") {
                this.showUI();
            } else if (data.action === "hide") {
                this.hideUI();
            } else if (data.action === "preloadData") {
                debug("接收到预加载数据指令");
                // 确保模拟数据已经生成
                if (!this.mockSongs || this.mockSongs.length === 0) {
                    debug("预加载阶段生成模拟数据");
                    this.generateMockData();
                }
                // 加载热门歌曲数据
                this.loadDiscoverData();
                // 设置预加载标记
                this.dataPreloaded = true;
                debug("数据预加载完成，标记已设置");
            } else if (data.action === "searchResult") {
                this.handleSearchResult(data.result);
            } else if (data.action === "supportedPlatforms") {
                this.updatePlatformSelector(data.platforms);
            } else if (data.action === "cacheStats") {
                debug("缓存统计:", data.stats);
            } else if (data.action === "cacheCleared") {
                debug("缓存已清除:", data.success);
            } else if (data.action === "directPlay") {
                // 处理直接播放请求
                debug("收到直接播放请求:", data.url);
                
                // 如果当前音频元素存在，尝试直接播放
                if (this.audio) {
                    // 停止当前播放
                    this.audio.pause();
                    
                    // 设置新的音频源
                    this.audio.src = data.url;
                    this.audio.volume = this.state.volume;
                    
                    // 尝试播放
                    this.audio.play().then(() => {
                        debug("直接播放成功");
                        
                        // 更新播放状态
                        this.state.isPlaying = true;
                        this.updatePlayButton();
                        
                        // 更新当前歌曲信息
                        if (!this.state.currentSong) {
                            this.state.currentSong = {
                                id: Date.now().toString(), // 生成临时ID
                                title: typeof data.title === 'string' ? data.title : "未知歌曲", 
                                artist: typeof data.artist === 'string' ? data.artist : "未知歌手",
                                url: data.url,
                                maxDuration: data.duration || 180
                            };
                        } else {
                            // 如果歌曲对象已存在，确保更新标题和艺术家
                            this.state.currentSong.title = typeof data.title === 'string' ? data.title : 
                                                        (this.state.currentSong.title || "未知歌曲");
                            this.state.currentSong.artist = typeof data.artist === 'string' ? data.artist : 
                                                         (this.state.currentSong.artist || "未知歌手");
                        }
                        
                        // 添加到当前播放列表，避免重复添加
                        if (this.state.currentSong && this.state.currentSong.id) {
                            // 确保有songInfoList数组用于存储完整歌曲信息
                            if (!this.state.songInfoList) {
                                this.state.songInfoList = [];
                            }
                            
                            // 保存完整的歌曲信息到songInfoList
                            const songInfo = {
                                id: this.state.currentSong.id,
                                title: this.state.currentSong.title || `歌曲 ${this.state.currentSong.id}`,
                                artist: this.state.currentSong.artist || '未知歌手',
                                cover: this.state.currentSong.cover || `https://via.placeholder.com/60x60/${this.getRandomColor()}/fff?text=${this.state.currentSong.id}`,
                                duration: this.state.currentSong.maxDuration || data.duration || '0:00',
                                url: data.url || '',
                                platform: this.state.currentSong.platform || 'unknown'
                            };
                            
                            // 移除详细信息中已存在的记录
                            this.state.songInfoList = this.state.songInfoList.filter(s => s.id !== this.state.currentSong.id);
                            // 添加详细信息
                            this.state.songInfoList.push(songInfo);
                            this.state.saveToStorage();
                            
                            if (!this.state.currentPlaylist.includes(this.state.currentSong.id)) {
                                // 将歌曲添加到播放列表的最上方（开头）
                                this.state.currentPlaylist.unshift(this.state.currentSong.id);
                                this.state.currentIndex = 0; // 新歌曲位于索引0位置
                                // 更新播放列表UI
                                this.updateCurrentPlaylist();
                                debug("直接播放: 歌曲已添加到播放列表顶部:", {
                                    id: this.state.currentSong.id,
                                    title: this.state.currentSong.title,
                                    artist: this.state.currentSong.artist,
                                    playlistLength: this.state.currentPlaylist.length
                                });
                            } else {
                                // 如果已在播放列表中，只更新当前索引，不改变歌曲位置
                                this.state.currentIndex = this.state.currentPlaylist.indexOf(this.state.currentSong.id);
                                debug("直接播放: 歌曲已在播放列表中，更新索引:", this.state.currentIndex);
                            }
                        }
                        
                        // 更新播放器信息
                        this.updatePlayerInfo(this.state.currentSong);
                        
                        // 强制启动进度更新
                        this._startProgressUpdates();
                        
                    }).catch(error => {
                        console.error("直接播放失败:", error);
                        
                        // 不显示错误状态，保持当前显示
                        debug("直接播放失败但按照要求保持当前显示");
                    });
                }
            } else if (data.action === "playbackError") {
                console.error("播放错误:", data.error);
                // 用户要求不显示错误消息，保持当前显示状态
                debug("收到播放错误但不更新UI状态");
            } else if (data.action === "resetErrorState") {
                // 处理重置错误状态的消息
                debug("重置错误状态");

                if (this.state.currentSong) {
                    // 创建更新后的歌曲对象
                    const updatedSong = {
                        ...this.state.currentSong,
                        title: data.title || this.state.currentSong.title,
                        artist: data.artist || this.state.currentSong.artist
                    };

                    // 移除错误标记
                    delete updatedSong.isError;

                    // 恢复原始标题（如果有错误前缀）
                    if (updatedSong.title && (
                        updatedSong.title.startsWith("加载失败:") ||
                        updatedSong.title.startsWith("播放失败:") ||
                        updatedSong.title.startsWith("直接播放失败:"))) {
                        updatedSong.title = updatedSong.title.replace(/^(加载失败:|播放失败:|直接播放失败:)\s*/, "");
                    }

                    // 更新当前歌曲信息
                    this.state.currentSong = updatedSong;

                    // 更新播放器显示
                    this.updatePlayerInfo(updatedSong);

                    debug("已重置错误状态，恢复正常显示");
                }
            } else if (data.action === "pausePlayback") {
                // 处理音乐盒暂停播放的消息
                debug("收到音乐盒暂停播放消息", data);

                // 更新播放状态
                this.state.isPlaying = false;
                this.state.isPaused = true;

                // 如果有当前时间，更新进度
                if (data.currentTime !== undefined) {
                    this.state.currentTime = data.currentTime;
                }

                // 更新播放按钮状态
                this.updatePlayButton();

                // 停止进度更新
                if (this.animationFrameId) {
                    cancelAnimationFrame(this.animationFrameId);
                    this.animationFrameId = null;
                }

            } else if (data.action === "resumePlayback") {
                // 处理音乐盒恢复播放的消息
                debug("收到音乐盒恢复播放消息", data);

                // 更新播放状态
                this.state.isPlaying = true;
                this.state.isPaused = false;

                // 如果有当前时间，更新进度
                if (data.currentTime !== undefined) {
                    this.state.currentTime = data.currentTime;
                }

                // 更新播放按钮状态
                this.updatePlayButton();

                // 重新启动进度更新
                this._startProgressUpdates();
            } else if (data.action === "getDailyRecommend:response") {
                // 处理每日推荐响应
                debug("收到每日推荐响应:", data);
                
                // 清除超时定时器
                if (this._dailyRecommendTimeout) {
                    clearTimeout(this._dailyRecommendTimeout);
                    this._dailyRecommendTimeout = null;
                }
                
                // 获取容器
                const container = document.getElementById('dailyRecommendList');
                if (!container) return;
                
                if (data.status === 'success' && data.songs && data.songs.length > 0) {
                    // 确保每首歌曲都有search_keyword字段
                    const processedSongs = data.songs.map(song => {
                        // 添加必要的字段
                        return {
                            ...song,
                            search_keyword: song.search_keyword || "每日推荐",
                            platform: song.platform || "NETEASE"
                        };
                    });
                    
                    // 保存处理后的每日推荐歌曲数据
                    this.dailyRecommendSongs = processedSongs;
                    window.dailyRecommendSongs = processedSongs;
                    
                    debug("处理后的每日推荐歌曲:", processedSongs.length, "首");
                    
                    // 打印前3首歌曲的详细信息，用于调试
                    processedSongs.slice(0, 3).forEach((song, index) => {
                        debug(`每日推荐歌曲 #${index+1}:`, {
                            id: song.id,
                            title: song.title || song.name,
                            artist: song.artist || song.singer,
                            search_keyword: song.search_keyword,
                            platform: song.platform
                        });
                    });
                    
                    // 显示歌曲
                    container.innerHTML = this.renderSongList(this.dailyRecommendSongs);
                } else {
                    // 获取失败，显示错误信息
                    container.innerHTML = this.renderEmptyState('exclamation-triangle', '获取失败', '无法获取每日推荐歌曲');
                    
                    // 使用模拟数据作为备选
                    setTimeout(() => {
                        container.innerHTML = this.renderSongList(this.mockSongs);
                    }, 1000);
                }
            } else if (data.action === "getRankingSongs:response") {
                // 处理排行榜响应
                debug("收到排行榜响应:", data);
                
                const rankingType = data.type || 'hot';
                const container = document.getElementById('rankingList');
                const songs = data.songs || [];
                
                // 清除超时定时器
                if (this._rankingTimeoutId) {
                    clearTimeout(this._rankingTimeoutId);
                    this._rankingTimeoutId = null;
                }
                
                if (data.status === 'success' && songs.length > 0) {
                    debug(`收到${rankingType}排行榜数据:`, songs.length, '首歌曲');
                    
                    // 确保rankingSongs是一个对象
                    if (!this.rankingSongs) this.rankingSongs = {};
                    
                    // 存储排行榜数据
                    this.rankingSongs[rankingType] = songs;
                    
                    // 更新UI，确保歌曲数据格式正确
                    const formattedSongs = songs.map((song, index) => {
                        // 提取歌曲ID，确保存在
                        const songId = song.id || song.songId || song.song_id || `song_${Date.now()}_${index}`;
                        
                        // 提取歌曲标题，确保存在
                        const title = song.name || song.title || song.songName || '未知歌曲';
                        
                        // 提取歌手名称，确保存在
                        const artist = song.singer || song.artist || song.artistName || '未知歌手';
                        
                        // 提取封面图片，确保存在
                        let cover = song.pic || song.picUrl || song.cover || song.cover_url || '';
                        if (!cover || cover === 'null' || cover === 'undefined' || cover === '') {
                            cover = `https://via.placeholder.com/200x200/${this.getRandomColor()}/fff?text=${encodeURIComponent((title || '').charAt(0))}`;
                        }
                        
                        // 确保封面URL使用HTTPS
                        if (typeof cover === 'string' && cover.startsWith('http:')) {
                            cover = cover.replace(/^http:/, 'https:');
                        }
                        
                        // 保留原始数据，确保播放时能获取完整信息
                        const originalData = {...song};
                        
                        // 提取歌曲URL，确保存在
                        const url = song.url || '';
                        
                        // 提取歌曲时长，确保是数字
                        let duration = 180; // 默认3分钟
                        if (song.duration) {
                            if (typeof song.duration === 'number') {
                                duration = song.duration;
                            } else if (typeof song.duration === 'string') {
                                const parsed = parseInt(song.duration);
                                if (!isNaN(parsed)) {
                                    duration = parsed;
                                }
                            }
                        }
                        
                        // 提取平台信息
                        const platform = song.platform || song.source || this.currentPlatform;
                        
                        // 确保时长是秒数
                        let durationInSeconds = duration;
                        if (typeof duration === 'string' && duration.includes(':')) {
                            const parts = duration.split(':');
                            if (parts.length === 2) {
                                const minutes = parseInt(parts[0]) || 0;
                                const seconds = parseInt(parts[1]) || 0;
                                durationInSeconds = minutes * 60 + seconds;
                            }
                        }
                        
                        // 返回格式化后的歌曲对象
                        return {
                            id: songId,
                            title: title,
                            artist: artist,
                            cover: cover,
                            url: url,
                            duration: duration,
                            durationInSeconds: durationInSeconds, // 添加秒数格式的时长
                            maxDuration: durationInSeconds, // 直接设置maxDuration字段
                            platform: platform,
                            // 保留原始数据
                            originalData: originalData || song,
                            // 添加调试信息
                            _debug_info: `${title} - ${artist}`
                        };
                    });
                    
                                            // 更新存储的数据
                        this.rankingSongs[rankingType] = formattedSongs;
                        
                        // 如果是热门歌曲榜，将数据保存到全局变量
                        if (rankingType === 'hot') {
                            // 首先将新的热门歌曲与现有歌曲合并，保留已存在的歌曲信息，避免丢失
                            // 确保window.hotSongs是一个数组
                            if (!window.hotSongs || !Array.isArray(window.hotSongs)) {
                                debug("初始化window.hotSongs为空数组");
                                window.hotSongs = [];
                            }
                            
                            // 将新的热门歌曲添加到现有列表中，避免重复
                            formattedSongs.forEach(newSong => {
                                if (!newSong || !newSong.id) return; // 跳过无效歌曲
                                
                                // 检查歌曲是否已存在
                                const existingIndex = window.hotSongs.findIndex(s => s && s.id === newSong.id);
                                if (existingIndex >= 0) {
                                    // 如果已存在，保留原有信息，但可以更新一些可能变化的字段
                                    window.hotSongs[existingIndex] = {
                                        ...window.hotSongs[existingIndex],
                                        title: newSong.title || window.hotSongs[existingIndex].title,
                                        artist: newSong.artist || window.hotSongs[existingIndex].artist,
                                        cover: newSong.cover || window.hotSongs[existingIndex].cover
                                    };
                                } else {
                                    // 如果不存在，添加新歌曲
                                    window.hotSongs.push(newSong);
                                }
                            });
                            
                            debug("已更新热门歌曲数据到全局变量，共", window.hotSongs.length, "首");
                            
                            // 打印前5首歌曲的详细信息，用于调试
                            formattedSongs.slice(0, 5).forEach((song, index) => {
                                debug(`热门歌曲 #${index+1}:`, {
                                    id: song.id,
                                    title: song.title || song.name,
                                    artist: song.artist || song.singer,
                                    url: song.url || '无',
                                    platform: song.platform || song.source || this.currentPlatform
                                });
                            });
                            
                            // 预先获取热门歌曲的URL
                            formattedSongs.forEach((song, index) => {
                                // 只处理前5首歌曲，避免过多请求
                                if (index < 5 && (!song.url || song.url === '')) {
                                    debug("预先获取热门歌曲URL:", song.id);
                                    fetch(`https://${GetParentResourceName()}/getSongUrl`, {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            songId: song.id,
                                            platform: song.platform || song.source || this.currentPlatform
                                        })
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.status === "success" && data.url) {
                                            debug("获取到热门歌曲URL:", song.id);
                                            song.url = data.url;
                                            // 更新全局数据
                                            const songIndex = window.hotSongs.findIndex(s => s.id === song.id);
                                            if (songIndex !== -1) {
                                                window.hotSongs[songIndex].url = data.url;
                                            }
                                        }
                                    })
                                    .catch(error => {
                                        console.error("获取热门歌曲URL出错:", error);
                                    });
                                }
                            });
                        }
                        
                        // 更新UI
                        if (container) {
                            container.innerHTML = this.renderSongList(formattedSongs);
                        }
                } else {
                    console.error("排行榜数据获取失败或为空");
                    if (container) {
                        container.innerHTML = this.renderEmptyState('exclamation-triangle', '获取失败', 
                                                               '无法获取排行榜数据，请稍后再试');
                    }
                    
                    // 使用模拟数据作为备选
                    setTimeout(() => {
                        this.useRankingMockData(rankingType, container);
                    }, 1000);
                }
            } else if (data.action === "playbackStatus") {
                // 处理服务端播放状态更新
                // 更新播放进度
                if (data.currentTime !== undefined) {
                    this.state.currentTime = Number(data.currentTime);
                }
                
                if (data.duration !== undefined) {
                    this.state.duration = Number(data.duration);
                }
                
                // 更新播放状态
                if (data.isPlaying !== undefined) {
                    this.state.isPlaying = data.isPlaying;
                    this.updatePlayButton();
                    
                    // 如果是正在播放状态，且当前显示的是错误状态，则恢复正常显示
                    // 或者如果收到强制更新标志，则无条件更新
                    if ((data.isPlaying && this.state.currentSong && this.state.currentSong.isError) || data.forceUpdate) {
                        // 移除错误标记
                        const updatedSong = {...this.state.currentSong};
                        delete updatedSong.isError;
                        
                        // 恢复原始标题（移除"加载失败:"或"播放失败:"前缀）
                        if (updatedSong.title && (
                            updatedSong.title.startsWith("加载失败:") || 
                            updatedSong.title.startsWith("播放失败:") ||
                            updatedSong.title.startsWith("直接播放失败:"))) {
                            updatedSong.title = updatedSong.title.replace(/^(加载失败:|播放失败:|直接播放失败:)\s*/, "");
                        }
                        
                        // 更新当前歌曲信息
                        this.state.currentSong = updatedSong;
                        
                        // 更新播放器显示
                        this.updatePlayerInfo(updatedSong);
                        
                        debug("已恢复正常播放状态显示" + (data.forceUpdate ? " (强制更新)" : ""));
                    }
                    
                    // 如果停止播放，重置进度
                    if (!data.isPlaying) {
                        this.state.currentTime = 0;
                    } else {
                        // 如果开始播放，确保进度条更新
                        this._startProgressUpdates();
                    }
                }
                
                // 更新进度条
                this.updateProgressDisplay();
            }
        });
        
        // 标记已注册
        window._messageListenerRegistered = true;
        
        // 默认隐藏UI
        this.hideUI();
        
        // 添加关闭按钮事件
        document.querySelector('.title-bar').insertAdjacentHTML('beforeend', 
            '<div class="close-btn"><i class="fas fa-times"></i></div>');
        
        document.querySelector('.close-btn').addEventListener('click', () => {
            this.closeUI();
        });
        
        // 清除可能存在的旧定时器
        if (window._progressTimer) {
            clearInterval(window._progressTimer);
            window._progressTimer = null;
        }
        
        if (window._progressUpdateTimer) {
            clearInterval(window._progressUpdateTimer);
            window._progressUpdateTimer = null;
        }
        
        // 添加定时器定期更新进度条，确保即使没有收到服务端消息也能更新进度
            window._progressTimer = setInterval(() => {
                if (this.state.isPlaying && this.state.currentTime !== undefined && this.state.duration) {
                    // 如果是服务端播放模式，手动更新时间
                    if (!this.audio.src || this.audio.paused) {
                        // 增加当前时间
                        this.state.currentTime += 0.5; // 每500毫秒增加0.5秒
                        
                        // 检查是否播放完毕
                        if (this.state.currentTime >= this.state.duration) {
                            if (this.state.repeatMode === 'one') {
                                // 单曲循环，重置时间
                                this.state.currentTime = 0;
                            } else {
                                // 播放结束
                                this.handleSongEnd();
                                return; // 避免更新进度条
                            }
                        }
                        
                        // 更新进度条
                        this.updateProgressDisplay();
                } else {
                    // 即使在浏览器播放模式下，也强制更新进度条以确保显示正确
                        this.updateProgressDisplay();
                    }
                }
            }, 500);
            
        debug("进度条定时器已设置");
    }
    
    showUI() {
        document.getElementById('app').style.display = 'block';
        this.state.visible = true;
    }
    
    hideUI() {
        document.getElementById('app').style.display = 'none';
        this.state.visible = false;
    }
    
    closeUI() {
        // 向客户端发送关闭UI的消息
        fetch(`https://${GetParentResourceName()}/close`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        });
        
        this.hideUI();
    }
    
    // 处理搜索结果
    handleSearchResult(result) {
        debug("收到搜索结果:", result);
        const resultsContainer = document.getElementById('searchResults');
        
        if (!result || result.code !== 200 || !result.data) {
            console.error("搜索结果无效:", result);
            resultsContainer.innerHTML = this.renderEmptyState('search', '搜索失败', 
                `未能获取搜索结果: ${result ? result.msg || '未知错误' : '未知错误'}`);
            this.searchResults = [];
            return;
        }
        
        try {
            debug("解析搜索结果数据:", result.data);
            const songs = result.data.map(song => {
                // 使用原始URL，只确保是HTTPS
                let url = song.url || '';
                if (url) {
                    url = url.replace(/^http:/, 'https:');
                }
                
                // 增强的编码处理函数
                const fixEncoding = (text) => {
                    if (!text) return '';
                    
                    // 确保text是字符串
                    text = String(text);
                    
                    // 移除可能导致HTML解析错误的字符
                    text = text.replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]/g, '');
                    
                    // 检测是否包含乱码特征
                    if (/[\ufffd\u9fa5-\u9fff]/.test(text)) {
                        try {
                            // 尝试使用不同的编码方式解码
                            return decodeURIComponent(escape(text));
                        } catch (e) {
                            // 如果解码失败，至少确保没有危险字符
                            return text.replace(/[<>&"']/g, '');
                        }
                    }
                    
                    // 确保没有危险字符
                    return text.replace(/[<>&"']/g, '');
                };
                
                // 确保所有字符串值都是安全的
                const name = fixEncoding(song.name) || '未知歌曲';
                const singer = fixEncoding(song.singer) || '未知歌手';
                const album = fixEncoding(song.album) || '未知专辑';
                const pic = song.pic || 'https://via.placeholder.com/60';
                
                // 确保正确获取歌曲时长（单位：秒）
                let duration = 0;
                if (song.duration) {
                    // 有些API返回的是毫秒，有些是秒
                    duration = song.duration > 1000 ? Math.floor(song.duration / 1000) : Math.floor(song.duration);
                } else if (song.interval) {
                    // 有些API使用interval字段
                    duration = Math.floor(song.interval);
                } else {
                    // 默认3分钟
                    duration = 180;
                }
                
                // 确保ID是安全的，不含特殊字符
                let id;
                if (song.mid) {
                    id = String(song.mid).replace(/[^\w-]/g, '_');
                } else if (song.id) {
                    id = String(song.id).replace(/[^\w-]/g, '_');
                } else {
                    id = Date.now() + '_' + Math.floor(Math.random() * 1000);
                }
                
                return {
                    id: id,
                    title: name,
                    artist: singer,
                    album: album,
                    maxDuration: duration, // 存储原始秒数
                    duration: this.formatTime(Math.floor(duration)), // 格式化的时间字符串用于显示
                    cover: pic.replace(/^http:/, 'https:'),
                    url: url,
                    originalId: song.id, // 保存原始ID，可能在后续处理中需要
                    platform: song.platform || this.currentPlatform, // 保存平台信息
                    search_keyword: this._currentSearchKeyword || '' // 保存搜索关键词
                };
            });
            
            debug("处理后的歌曲数据:", songs);
            // 保存搜索结果
            this.searchResults = songs;
            
            if (songs.length === 0) {
                resultsContainer.innerHTML = this.renderEmptyState('search', '未找到相关歌曲', `没有找到相关歌曲`);
            } else {
                resultsContainer.innerHTML = `
                    <h3>搜索结果 (${songs.length})</h3>
                    ${this.renderSongList(songs)}
                `;
            }
        } catch (error) {
            console.error("处理搜索结果时出错:", error);
            resultsContainer.innerHTML = this.renderEmptyState('error', '处理错误', `处理搜索结果时出错: ${error.message}`);
        }
    }
    
    // 更新平台选择器
    updatePlatformSelector(platforms) {
        this.supportedPlatforms = platforms;
        
        // 确保平台代码是大写的
        this.currentPlatform = (this.currentPlatform || (platforms[0] && platforms[0].code) || 'QQ').toUpperCase();
        
        debug("当前平台设置为:", this.currentPlatform);
        
        // 如果页面上有平台选择器，就更新它
        const platformSelector = document.getElementById('platformSelector');
        if (platformSelector) {
            // 先保存当前选中的平台
            const currentPlatform = platformSelector.value;
            
            platformSelector.innerHTML = platforms.map(platform => {
                const code = platform.code.toUpperCase();
                return `
                    <option value="${code}" ${this.currentPlatform === code ? 'selected' : ''}>
                    ${platform.name}
                </option>
                `;
            }).join('');
            
            // 如果之前有选中的平台，尝试恢复它
            if (currentPlatform) {
                platformSelector.value = currentPlatform;
                if (platformSelector.value !== currentPlatform) {
                    // 如果无法恢复，则使用当前设置的平台
                    platformSelector.value = this.currentPlatform;
                } else {
                    // 如果成功恢复，更新当前平台
                    this.currentPlatform = currentPlatform;
                }
            }
            
            // 添加平台选择器的change事件监听
            platformSelector.addEventListener('change', () => {
                this.switchPlatform(platformSelector.value);
            });
        }
    }
    
    // 切换平台
    switchPlatform(platformCode) {
        if (platformCode && platformCode !== this.currentPlatform) {
            // 确保平台代码是大写的
            platformCode = platformCode.toUpperCase();
            debug("切换到平台:", platformCode);
            this.currentPlatform = platformCode;
            
            // 更新平台选择器的显示
            const platformSelector = document.getElementById('platformSelector');
            if (platformSelector) {
                platformSelector.value = platformCode;
            }
            
            // 通知服务器平台已切换
            fetch(`https://${GetParentResourceName()}/switchPlatform`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    platform: platformCode
                })
            })
            .then(response => response.json())
            .then(data => {
                debug("平台切换结果:", data);
            })
            .catch(error => {
                console.error("平台切换错误:", error);
            });
            
            // 如果当前在搜索页面，可以自动重新搜索
            const searchInput = document.getElementById('searchInput');
            if (this.state.currentView === 'search' && searchInput.value.trim()) {
                this.searchSongs(searchInput.value.trim());
            }
        }
    }
    
    // 测试fetch连接
    testFetchConnection(resourceName) {
        console.log("开始测试fetch连接...");

        // 首先测试最简单的NUI回调
        console.log("1. 测试基本NUI回调...");
        fetch(`https://${resourceName}/getSupportedPlatforms`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        })
        .then(response => {
            console.log("✅ 基本NUI回调成功 - 状态码:", response.status);
            return response.json();
        })
        .then(data => {
            console.log("✅ 基本NUI回调响应数据:", JSON.stringify(data, null, 2));

            // 如果基本回调成功，测试服务器事件触发
            console.log("2. 测试服务器事件触发...");
            return fetch(`https://${resourceName}/triggerServerEvent`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    eventName: 'music:getSupportedPlatforms'
                })
            });
        })
        .then(response => {
            console.log("✅ 服务器事件触发成功 - 状态码:", response.status);
            return response.json();
        })
        .then(data => {
            console.log("✅ 服务器事件响应:", JSON.stringify(data, null, 2));
        })
        .catch(error => {
            console.error("❌ Fetch请求失败:", error);
            console.error("❌ 错误类型:", error.name);
            console.error("❌ 错误信息:", error.message);
            console.error("❌ 请求URL:", `https://${resourceName}/getSupportedPlatforms`);

            // 检查是否是网络错误
            if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
                console.error("❌ 这是一个网络连接错误，可能的原因：");
                console.error("   1. NUI回调未正确注册 - 检查client.lua中的RegisterNUICallback");
                console.error("   2. 资源名称不匹配 - 当前使用:", resourceName);
                console.error("   3. 客户端脚本未加载 - 检查fxmanifest.lua");
                console.error("   4. FiveM NUI系统问题 - 重启资源试试");
                console.error("   5. 资源未正确启动 - 检查服务器控制台");
            }
        });
    }

    // ==================== 初始化完成 ====================
    init() {
        // 显示资源名称信息用于调试
        const resourceName = GetParentResourceName();
        console.log("=== 音乐播放器调试信息 ===");
        console.log("资源名称:", resourceName);
        console.log("当前URL:", window.location.href);
        console.log("测试fetch URL:", `https://${resourceName}/getSupportedPlatforms`);
        console.log("========================");

        // 测试基本的fetch连接
        this.testFetchConnection(resourceName);

        // 确保全局标记被重置
        window._isAddingToPlaylist = false;
        window._isPlayingExistingSong = false;
        // 检查必要的DOM元素是否存在
        this._checkRequiredElements();

        // 获取支持的平台列表
        this.getSupportedPlatforms();
        
        // 添加数据加载状态标记
        this.dataPreloaded = false;
        
        // 播放列表关闭按钮事件 - 在DOMContentLoaded后再处理
        document.addEventListener('DOMContentLoaded', () => {
            const closePlaylistBtn = document.querySelector('.btn-close-playlist');
            if (closePlaylistBtn) {
                closePlaylistBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    debug('初始化时：关闭播放列表按钮被点击');
                    this.closePlaylistPanel();
                });
            }
            
            // 确保模态框初始化正确
            this.setupModal();
            
            // 在DOM完全加载后初始化数据，但仅在未预加载的情况下
            debug("DOM完全加载，检查是否需要加载发现页面数据...");
            if (!this.dataPreloaded) {
                debug("数据未预加载，现在加载发现页面数据...");
                // 确保模拟数据已经生成
                if (!this.mockSongs || this.mockSongs.length === 0) {
                    debug("初始化阶段生成模拟数据");
                    this.generateMockData();
                }
                // 初始化数据
                this.loadDiscoverData();
            } else {
                debug("数据已预加载，跳过重复加载");
            }
            
            // 确保歌单数据显示正确
            this.updateSidebarPlaylists();
            
            // 更新歌单计数
            document.querySelector('.nav-item[data-view="playlists"] .count').textContent = this.state.playlists.length;
            
            // 如果当前在歌单视图，刷新数据
            if (this.state.currentView === 'playlists') {
                this.loadPlaylistsData();
            }
        });
        
        // 从服务器加载玩家歌单数据
        this.loadPlayerData();
        
        // 应用启动后，预加载所有歌单的歌曲数量
        setTimeout(() => {
            if (this.state.playlists && this.state.playlists.length > 0) {
                debug('应用启动后预加载所有歌单的歌曲数量...');
                this.state.playlists.forEach(playlist => {
                    if (playlist.id) {
                        this.preloadPlaylistSongCount(playlist.id);
                    }
                });
            }
        }, 2000); // 延迟2秒执行，确保loadPlayerData已完成
        
        // 设置全局错误处理
        this._setupGlobalErrorHandling();
    }
    
    // 获取支持的平台列表
    getSupportedPlatforms() {
        debug("正在获取支持的平台列表...");
        
        // 默认平台列表，如果无法从服务器获取，将使用此列表
        const defaultPlatforms = [
            { code: "QQ", name: "QQ音乐" },
            { code: "NETEASE", name: "网易云音乐" },
            { code: "KUGOU", name: "酷狗音乐" },
            { code: "KUWO", name: "酷我音乐" },
            { code: "XMLY", name: "喜马拉雅" },
            { code: "QQ_RADIO", name: "蜻蜓FM" }
        ];
        
        // 直接使用默认平台列表初始化，确保界面始终有内容
        this.updatePlatformSelector(defaultPlatforms);
        
        // 然后尝试从服务器获取最新的平台列表
        const resourceName = GetParentResourceName();
        debug("获取平台列表 - 使用资源名称:", resourceName);

        fetch(`https://${resourceName}/getSupportedPlatforms`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            debug("获取平台列表响应:", data);
            
            if (data.status === 'success' && data.platforms && Array.isArray(data.platforms) && data.platforms.length > 0) {
                debug("支持的平台列表:", data.platforms);
                // 确保每个平台对象都有code和name属性
                const validPlatforms = data.platforms.filter(p => p && p.code && p.name);
                if (validPlatforms.length > 0) {
                    this.updatePlatformSelector(validPlatforms);
                }
            }
        })
        .catch(error => {
            console.error('获取平台列表失败:', error);
            console.error("请求URL:", `https://${resourceName}/getSupportedPlatforms`);
            console.error("资源名称:", resourceName);
            // 已经使用默认平台列表初始化，这里不需要再次初始化
        });
    }
    
    // 设置全局错误处理
    _setupGlobalErrorHandling() {
        // 添加全局错误处理
        window.onerror = (message, source, lineno, colno, error) => {
            console.error("全局错误:", message, "在", source, "行:", lineno, "列:", colno);
            
            // 记录详细的错误信息
            if (error && error.stack) {
                console.error("错误堆栈:", error.stack);
            }
            
            // 过滤一些已知的非关键错误
            const knownErrors = [
                "NotSupportedError: The element has no supported sources",
                "NotAllowedError",
                "AbortError"
            ];
            
            // 检查是否为已知错误
            let isKnownError = false;
            for (const knownError of knownErrors) {
                if (message.includes(knownError)) {
                    debug("捕获到已知错误，将尝试恢复:", knownError);
                    isKnownError = true;
                    break;
                }
            }
            
            // 对于音频播放错误，尝试使用服务端播放
            if (isKnownError && this.state.currentSong) {
                debug("尝试使用服务端播放恢复");
                setTimeout(() => {
                    if (this.state.currentSong) {
                        this.playSong(this.state.currentSong.id);
                    }
                }, 500);
                return true; // 阻止默认错误处理
            }
            
            return false;
        };
        
        // 添加未捕获的Promise错误处理
        window.addEventListener('unhandledrejection', (event) => {
            console.error("未处理的Promise错误:", event.reason);
            
            // 检查是否为音频播放相关错误
            if (event.reason && 
                (event.reason.name === "NotSupportedError" || 
                 event.reason.name === "NotAllowedError" || 
                 event.reason.name === "AbortError")) {
                
                debug("捕获到音频播放Promise错误，尝试恢复");
                
                // 如果是单曲循环模式下的错误，尝试使用服务端播放
                if (this.state.repeatMode === 'one' && this.state.currentSong) {
                    debug("单曲循环模式下发生错误，尝试使用服务端播放");
                    setTimeout(() => {
                        if (this.state.currentSong) {
                            this.playSong(this.state.currentSong.id);
                        }
                    }, 500);
                    
                    // 阻止错误冒泡
                    event.preventDefault();
                }
            }
        });
        
        // 添加全局调试命令
        window.debugMusicPlayer = {
            resetProgressBar: () => {
                return this._forceProgressBarReset();
            },
            forceUpdateProgress: (percent) => {
                const progressFill = document.getElementById('progressFill');
                const progressHandle = document.getElementById('progressHandle');
                if (progressFill && progressHandle) {
                    progressFill.style.width = `${percent}%`;
                    progressHandle.style.left = `${percent}%`;
                    return true;
                }
                return false;
            },
            checkElements: () => {
                return this._checkRequiredElements();
            },
            getState: () => {
                return {
                    isPlaying: this.state.isPlaying,
                    currentTime: this.state.currentTime,
                    duration: this.state.duration,
                    currentSong: this.state.currentSong ? {
                        title: this.state.currentSong.title,
                        artist: this.state.currentSong.artist,
                        duration: this.state.currentSong.maxDuration
                    } : null
                };
            },
            restartAnimation: () => {
                const progressFill = document.getElementById('progressFill');
                if (progressFill) {
                    progressFill.classList.remove('playing');
                    void progressFill.offsetWidth;
                    progressFill.classList.add('playing');
                    return true;
                }
                return false;
            }
        };
    }
    
    // 检查必要的DOM元素是否存在
    _checkRequiredElements() {
        const requiredElements = [
            'currentTime',
            'totalTime',
            'progressFill',
            'progressHandle',
            'playBtn',
            'prevBtn',
            'nextBtn',
            'volumeBtn',
            'volumeFill',
            'volumeHandle'
        ];
        
        let allFound = true;
        let missingElements = [];
        
        requiredElements.forEach(id => {
            const element = document.getElementById(id);
            if (!element) {
                console.error(`缺少必要的DOM元素: #${id}`);
                allFound = false;
                missingElements.push(id);
            }
        });
        
        if (!allFound) {
            console.error('缺少一些必要的DOM元素，这可能导致播放器功能异常', missingElements);
        }
        
        // 检查进度条是否正确设置
        const progressFill = document.getElementById('progressFill');
        if (progressFill) {
            // 确保进度条初始宽度为0
            progressFill.style.width = '0%';
        }
        
        return { allFound, missingElements };
    }
    
    // 添加强制重新初始化进度条的方法
    _forceProgressBarReset() {
        try {
            // 获取进度条元素
            const progressFill = document.getElementById('progressFill');
            const progressHandle = document.getElementById('progressHandle');
            const currentTimeElement = document.getElementById('currentTime');
            const totalTimeElement = document.getElementById('totalTime');
            
            if (!progressFill || !progressHandle) {
                console.error("找不到进度条元素，无法重置");
                return;
            }
            
            // 先移除所有类，确保进度条回到初始状态
            progressFill.classList.remove('playing');
            
            // 强制重置进度条宽度为0
            progressFill.style.width = '0%';
            progressHandle.style.left = '0%';
            
            // 重置时间显示
            if (currentTimeElement) {
                currentTimeElement.textContent = '0:00';
            }
            
            if (totalTimeElement && this.state.duration) {
                totalTimeElement.textContent = this.formatTime(this.state.duration);
            } else if (totalTimeElement) {
                totalTimeElement.textContent = '0:00';
            }
            
            // 强制浏览器重绘
            void progressFill.offsetWidth;
            
            // 设置一个小的初始宽度，确保进度条可见
            setTimeout(() => {
                // 再次检查元素是否存在
                if (progressFill && progressHandle) {
                    progressFill.style.width = '0.5%';
                    progressHandle.style.left = '0.5%';
                    
                    // 添加动画类
                    if (this.state.isPlaying) {
                        progressFill.classList.add('playing');
                    }
                    
                    // 再次强制更新一次
                    setTimeout(() => {
                        if (progressFill && this.state.isPlaying) {
                            progressFill.style.width = '1%';
                            progressHandle.style.left = '1%';
                        }
                    }, 500);
                }
            }, 50);
            
            // 确保进度更新定时器在运行
            this._startProgressUpdates();
            
            // 确保状态一致
            this.state.currentTime = 0.1;
            
            return true;
        } catch (error) {
            console.error("重置进度条时出错:", error);
            return false;
        }
    }
    
    // 设置播放位置
    setTimeStamp(time) {
        if (!this.state.isPlaying || !this.state.currentSong) return;
        
        // 确保时间是有效值
        time = Number(time) || 0;
        if (time < 0) time = 0;
        
        // 更新音频元素的播放位置（如果存在）
        if (this.audio && this.audio.src) {
            this.audio.currentTime = time;
        }
        
        // 更新当前时间
        this.state.currentTime = time;
        
        // 更新恢复播放的时间点和位置
        this.state.resumeTime = Date.now();
        this.state.resumePosition = time;
        
        // 更新进度条
        this.updateProgressDisplay();
        
        // 调用服务器API设置播放位置
        fetch(`https://${GetParentResourceName()}/setTimeStamp`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                time: time
            })
        }).then(response => response.json())
        .catch(() => {
            // 错误处理，但不输出调试信息
        });
    }
    
    // 设置音量
    setVolume(volume) {
        // 确保音量在有效范围内
        volume = Number(volume) || 0;
        if (volume < 0) volume = 0;
        if (volume > 1) volume = 1;
        
        debug("设置音量:", volume, "之前的音量:", this.state.volume);
        
        // 保存音量设置
        this.state.volume = volume;
        
        // 如果设置了新音量且大于0，自动取消静音状态
        if (volume > 0 && this.state.isMuted) {
            debug("音量大于0，自动取消静音状态");
            this.state.isMuted = false;
        }
        
        // 更新音频元素的音量（如果存在）
        if (this.audio) {
            this.audio.volume = volume;
            this.audio.muted = this.state.isMuted;
        }
        
        // 更新音量显示
        this.updateVolumeDisplay();
        
        // 保存设置到存储
        this.state.saveToStorage();
        
        // 调用服务器API设置音量 - 考虑静音状态
        const effectiveVolume = this.state.isMuted ? 0 : volume;
        
        // 显示提示
        this.showToast(`音量: ${Math.round(volume * 100)}%`, 1000);
        
        // 发送音量设置到服务器 - 添加重试机制
        const sendVolumeToServer = (retryCount = 0) => {
            fetch(`https://${GetParentResourceName()}/setVolume`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    volume: effectiveVolume
                })
            }).then(response => response.json())
            .then(data => {
                debug("音量设置成功:", data);
            })
            .catch(error => {
                console.error("音量设置失败:", error);
                // 如果失败，最多重试3次
                if (retryCount < 3) {
                    debug(`音量设置重试 (${retryCount + 1}/3)...`);
                    setTimeout(() => sendVolumeToServer(retryCount + 1), 300);
                }
            });
        };
        
        // 立即发送一次
        sendVolumeToServer();
        
        // 0.5秒后再次发送，确保设置生效
        setTimeout(() => {
            debug("再次发送音量设置以确保生效:", effectiveVolume);
            sendVolumeToServer();
        }, 500);
    }
    
    // 从服务器加载玩家数据（歌单、收藏等）
    loadPlayerData() {
        debug('正在从服务器加载玩家数据...');
        
        // 使用Promise.all和超时处理，确保即使请求失败也能继续
        const loadPlaylists = () => {
            return new Promise((resolve) => {
                // 设置超时
                const timeout = setTimeout(() => {
                    debug('获取玩家歌单超时，使用本地数据');
                    resolve({ status: 'error', message: '请求超时' });
                }, 5000);
                
                // 获取玩家歌单（包含歌曲数量信息）
                fetch(`https://${GetParentResourceName()}/getPlayerPlaylists`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ includeDetails: true })
                })
                .then(resp => resp.json())
                .then(resp => {
                    clearTimeout(timeout);
                    resolve(resp);
                })
                .catch(error => {
                    clearTimeout(timeout);
                    console.error('获取玩家歌单出错:', error);
                    resolve({ status: 'error', message: error.toString() });
                });
            });
        };
        
        const loadFavorites = () => {
            return new Promise((resolve) => {
                // 设置超时
                const timeout = setTimeout(() => {
                    debug('获取收藏歌曲超时，使用本地数据');
                    resolve({ status: 'error', message: '请求超时' });
                }, 5000);
                
                // 获取收藏歌曲
                fetch(`https://${GetParentResourceName()}/getFavoriteSongs`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                })
                .then(resp => resp.json())
                .then(resp => {
                    clearTimeout(timeout);
                    resolve(resp);
                })
                .catch(error => {
                    clearTimeout(timeout);
                    console.error('获取收藏歌曲出错:', error);
                    resolve({ status: 'error', message: error.toString() });
                });
            });
        };
        
        // 并行加载数据
        Promise.all([loadPlaylists(), loadFavorites()])
            .then(([playlistsResp, favoritesResp]) => {
                // 处理歌单数据
                if (playlistsResp.status === 'success' && playlistsResp.playlists) {
                    debug('加载到玩家歌单:', playlistsResp.playlists.length, '个');
                    
                    // 将服务器歌单数据转换为前端格式
                    const playlists = playlistsResp.playlists.map(playlist => ({
                        id: playlist.id,
                        name: playlist.name,
                        description: playlist.description || '',
                        songs: playlist.songs || playlist.songIds || [], // 使用服务器返回的歌曲ID列表
                        songCount: playlist.songCount || (playlist.songs ? playlist.songs.length : 0), // 使用服务器返回的歌曲数量
                        created: new Date(playlist.created_at || Date.now()).toISOString(),
                        cover: `https://via.placeholder.com/180x180/ff6b6b/fff?text=${encodeURIComponent((playlist.name || '').charAt(0))}`
                    }));
                    
                    // 更新本地状态
                    this.state.playlists = playlists;
                    this.state.saveToStorage();
                    
                    // 更新UI
                    document.querySelector('.nav-item[data-view="playlists"] .count').textContent = playlists.length;
                    this.updateSidebarPlaylists();
                    
                    // 预加载每个歌单的歌曲数量
                    playlists.forEach(playlist => {
                        if (!playlist.songCount && playlist.id) {
                            this.preloadPlaylistSongCount(playlist.id);
                        }
                    });
                    
                    // 如果当前在歌单视图，刷新数据
                    if (this.state.currentView === 'playlists') {
                        this.loadPlaylistsData();
                    }
                } else {
                    debug('加载玩家歌单失败或没有歌单:', playlistsResp.message || '未知错误');
                    // 使用本地存储的数据
                    if (!this.state.playlists) this.state.playlists = [];
                    document.querySelector('.nav-item[data-view="playlists"] .count').textContent = this.state.playlists.length;
                    this.updateSidebarPlaylists();
                }
                
                // 处理收藏歌曲数据
                if (favoritesResp.status === 'success' && favoritesResp.songs) {
                    debug('加载到收藏歌曲:', favoritesResp.songs.length, '首');
                    
                    // 更新本地状态
                    this.state.favorites = new Set(favoritesResp.songs.map(song => song.id));
                    this.state.saveToStorage();
                    
                    // 更新UI
                    document.querySelector('.nav-item[data-view="favorites"] .count').textContent = this.state.favorites.size;
                    
                    // 如果当前在收藏视图，刷新数据
                    if (this.state.currentView === 'favorites') {
                        this.loadFavoritesData();
                    }
                } else {
                    debug('加载收藏歌曲失败或没有收藏:', favoritesResp.message || '未知错误');
                    // 确保favorites是一个Set对象
                    if (!(this.state.favorites instanceof Set)) {
                        this.state.favorites = new Set(Array.isArray(this.state.favorites) ? this.state.favorites : []);
                    }
                    document.querySelector('.nav-item[data-view="favorites"] .count').textContent = this.state.favorites.size;
                }
            });
    }

    // 加载我的歌单数据
    loadPlaylistsData() {
        const container = document.getElementById('userPlaylistsGrid');
        
        if (!container) return;
        
        if (this.state.playlists.length === 0) {
            container.innerHTML = this.renderEmptyState(
                'fas fa-music',
                '没有歌单',
                '点击"创建新歌单"按钮创建您的第一个歌单'
            );
            return;
        }
        
        container.innerHTML = this.state.playlists.map(playlist => `
            <div class="playlist-card" data-playlist-id="${playlist.id}">
                <div class="playlist-cover">
                    <img src="${playlist.cover}" alt="${this.escapeHtml(playlist.name)}">
                    <div class="playlist-actions">
                        <button class="btn-play-playlist" data-playlist-id="${playlist.id}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="playlist-info">
                    <h4>${this.escapeHtml(playlist.name)}</h4>
                    <p>${playlist.songCount !== undefined ? playlist.songCount : playlist.songs.length} 首歌曲</p>
                </div>
            </div>
        `).join('');
        
        // 添加点击事件
        document.querySelectorAll('.btn-play-playlist').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const playlistId = btn.dataset.playlistId;
                
                // 加载歌单歌曲
                this.loadPlaylistSongs(playlistId);
            });
        });
        
        document.querySelectorAll('.playlist-card').forEach(card => {
            card.addEventListener('click', () => {
                const playlistId = card.dataset.playlistId;
                
                // 加载歌单歌曲
                this.loadPlaylistSongs(playlistId);
            });
        });
    }
    
    // 加载歌单歌曲
    loadPlaylistSongs(playlistId) {
        debug('正在加载歌单歌曲，歌单ID:', playlistId);
        
        fetch(`https://${GetParentResourceName()}/getPlaylistSongs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ playlistId })
        })
        .then(resp => resp.json())
        .then(resp => {
            if (resp.status === 'success' && resp.songs) {
                debug('加载到歌单歌曲:', resp.songs.length, '首');
                
                // 找到对应歌单
                const playlist = this.state.playlists.find(p => p.id == playlistId);
                if (playlist) {
                    // 更新歌单歌曲
                    playlist.songs = resp.songs.map(song => song.id);
                    playlist.songCount = playlist.songs.length; // 更新歌曲数量
                    this.state.saveToStorage();
                    
                    // 更新侧边栏歌单列表
                    this.updateSidebarPlaylists();
                    
                    // 更新歌单计数
                    document.querySelector('.nav-item[data-view="playlists"] .count').textContent = this.state.playlists.length;
                    
                    // 如果当前在歌单视图，刷新数据
                    if (this.state.currentView === 'playlists') {
                        this.loadPlaylistsData();
                    }
                    
                    // 显示歌单内容
                    this.showPlaylistContent(playlist, resp.songs);
                }
            } else {
                debug('加载歌单歌曲失败或歌单为空');
            }
        })
        .catch(error => {
            console.error('获取歌单歌曲出错:', error);
        });
    }
    
    // 显示歌单内容
    showPlaylistContent(playlist, songs) {
        debug('显示歌单内容，歌单ID:', playlist.id, '歌曲数量:', songs.length);
        
        // 切换到自定义视图
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });
        
        // 如果没有现成的歌单内容视图，可以临时创建一个
        let playlistContentView = document.getElementById('playlistContentView');
        if (!playlistContentView) {
            // 创建歌单内容视图
            const mainContent = document.querySelector('.content-views');
            playlistContentView = document.createElement('div');
            playlistContentView.id = 'playlistContentView';
            playlistContentView.className = 'view';
            mainContent.appendChild(playlistContentView);
            debug('创建了新的歌单内容视图');
        }
        
        // 处理歌曲时长，确保每首歌曲都有正确的时长格式
        const processedSongs = songs.map(song => {
            // 确保时长是秒数
            if (song.duration && typeof song.duration === 'string' && song.duration.includes(':')) {
                song.maxDuration = this.parseTimeString(song.duration);
                song.durationInSeconds = song.maxDuration;
                debug("将歌单歌曲时间字符串转换为秒数:", song.duration, "->", song.maxDuration);
            } else if (typeof song.duration === 'number') {
                song.maxDuration = song.duration;
                song.durationInSeconds = song.duration;
            } else {
                // 默认设置3分钟
                song.maxDuration = 180;
                song.durationInSeconds = 180;
            }
            return song;
        });
        
        // 保存处理后的歌曲到全局变量，以便播放时使用
        window.playlistSongs = processedSongs;
        debug('已将处理后的歌曲保存到全局变量 window.playlistSongs，长度:', processedSongs.length);
        
        // 填充歌单内容
        playlistContentView.innerHTML = `
            <div class="view-header">
                <h2>${this.escapeHtml(playlist.name)}</h2>
                <div class="view-actions">
                    <button class="btn-back-to-playlists">
                        <i class="fas fa-arrow-left"></i>
                        返回歌单列表
                    </button>
                </div>
            </div>
            <div class="song-list" id="playlistSongList">
                ${processedSongs.length > 0 ? this.renderSongList(processedSongs) : 
                  this.renderEmptyState('fas fa-music', '歌单为空', '歌单中还没有歌曲')}
            </div>
        `;
        
        // 显示歌单内容视图
        playlistContentView.classList.add('active');
        
        // 添加返回按钮点击事件
        document.querySelector('.btn-back-to-playlists').addEventListener('click', () => {
            debug('点击返回歌单列表按钮');
            this.switchView('playlists');
        });
        
        // 播放全部按钮相关代码已被移除
    }
    
    // 添加一个辅助函数，用于将时间字符串(如"3:45")转换为秒数
    parseTimeString(timeStr) {
        if (typeof timeStr === 'number') {
            return timeStr; // 已经是秒数，直接返回
        }
        
        if (typeof timeStr !== 'string') {
            return 180; // 默认3分钟
        }
        
        // 处理"分:秒"格式的字符串
        const parts = timeStr.split(':');
        if (parts.length === 2) {
            const minutes = parseInt(parts[0]) || 0;
            const seconds = parseInt(parts[1]) || 0;
            return minutes * 60 + seconds;
        }
        
        // 尝试直接解析为整数
        const seconds = parseInt(timeStr);
        return isNaN(seconds) ? 180 : seconds;
    }
    
    // 预加载歌单歌曲数量
    preloadPlaylistSongCount(playlistId) {
        debug('预加载歌单歌曲数量，歌单ID:', playlistId);
        
        fetch(`https://${GetParentResourceName()}/getPlaylistSongs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ playlistId })
        })
        .then(resp => resp.json())
        .then(resp => {
            if (resp.status === 'success') {
                // 使用歌曲数组的长度作为歌曲数量
                const songCount = resp.songs ? resp.songs.length : 0;
                debug('预加载歌单歌曲数量成功:', songCount, '首');
                
                // 找到对应歌单
                const playlist = this.state.playlists.find(p => p.id == playlistId);
                if (playlist) {
                    // 更新歌单歌曲数量
                    playlist.songCount = songCount;
                    // 同时更新歌曲ID数组
                    playlist.songs = resp.songs.map(song => song.id);
                    this.state.saveToStorage();
                    
                    // 更新侧边栏歌单列表
                    this.updateSidebarPlaylists();
                    
                    // 如果当前在歌单视图，刷新数据
                    if (this.state.currentView === 'playlists') {
                        this.loadPlaylistsData();
                    }
                }
            }
        })
        .catch(error => {
            console.error('预加载歌单歌曲数量出错:', error);
        });
    }
    
    // 按顺序添加歌曲到播放列表（添加到末尾）- 简化版
    addToPlaylistInOrder(songId) {
        debug('addToPlaylistInOrder 被调用:', songId);
        
        // 检查歌曲是否已经在播放列表中
        if (!this.state.currentPlaylist.includes(songId)) {
            debug('歌曲不在播放列表中，添加到末尾');
            
            // 获取歌曲信息
            let songInfo = null;
            
            // 从歌单歌曲中查找
            if (window.playlistSongs && window.playlistSongs.length > 0) {
                const playlistSong = window.playlistSongs.find(s => s.id === songId);
                if (playlistSong) {
                    debug('在歌单中找到歌曲信息:', playlistSong.title || playlistSong.name);
                    songInfo = {...playlistSong};
                }
            }
            
            // 如果找不到，创建基本信息
            if (!songInfo) {
                debug('创建基本歌曲信息');
                songInfo = {
                    id: songId,
                    title: `歌曲 ${songId}`,
                    artist: '未知歌手',
                    cover: `https://via.placeholder.com/60x60/ff6b6b/fff?text=${songId}`,
                    duration: '0:00'
                };
            }
            
            // 确保有songInfoList数组
            if (!this.state.songInfoList) {
                this.state.songInfoList = [];
            }
            
            // 添加到songInfoList
            this.state.songInfoList = this.state.songInfoList.filter(song => song.id !== songId);
            this.state.songInfoList.push(songInfo);
            
            // 添加到播放列表末尾
            this.state.currentPlaylist.push(songId);
            debug('歌曲已添加到播放列表末尾');
            
            // 更新UI
            this.updateCurrentPlaylist();
        } else {
            debug('歌曲已在播放列表中，跳过添加');
        }
    }
}

// ==================== 应用启动 ====================
let player;

document.addEventListener('DOMContentLoaded', () => {
    // 确保只初始化一次
    if (window._playerInitialized) {
        debug('播放器已初始化，跳过');
        return;
    }
    
    player = new MusicPlayer();
    player.init();
    
    // 标记已初始化
    window._playerInitialized = true;
    
    // 使标题栏可拖动
    makeDraggable(document.querySelector('.title-bar'));
    
    // 防止右键菜单
    document.addEventListener('contextmenu', (e) => {
        if (e.target.closest('.song-item')) {
            e.preventDefault();
            const songItem = e.target.closest('.song-item');
            const songId = songItem.dataset.songId;
            if (songId && player) {
                player.showContextMenu(e, songId);
            }
        } else {
            e.preventDefault();
        }
    });
    
    // 防止拖拽
    document.addEventListener('dragstart', (e) => {
        e.preventDefault();
    });
    
    // 防止选择文本
    document.addEventListener('selectstart', (e) => {
        if (!e.target.matches('input, textarea')) {
            e.preventDefault();
        }
    });
});

// 使元素可拖动的函数
function makeDraggable(element) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
    
    element.onmousedown = dragMouseDown;
    
    function dragMouseDown(e) {
        e.preventDefault();
        // 获取鼠标位置
        pos3 = e.clientX;
        pos4 = e.clientY;
        document.onmouseup = closeDragElement;
        // 鼠标移动时调用elementDrag
        document.onmousemove = elementDrag;
    }
    
    function elementDrag(e) {
        e.preventDefault();
        // 计算新位置
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;
        // 设置元素的新位置
        const app = document.getElementById('app');
        app.style.position = 'absolute';
        app.style.top = (app.offsetTop - pos2) + "px";
        app.style.left = (app.offsetLeft - pos1) + "px";
    }
    
    function closeDragElement() {
        // 停止移动
        document.onmouseup = null;
        document.onmousemove = null;
    }
}