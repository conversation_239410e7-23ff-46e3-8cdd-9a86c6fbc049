-- 主服务器文件

-- 确保Config对象存在
if not Config then
    Config = {}
end

-- 添加API配置，但不覆盖现有配置
if not Config.api_base_url then
    Config.api_base_url = "https://shybot.top/v2/music/api"
end
if not Config.api_key then
    Config.api_key = "96b3597b323680ef20195a49706546d07090ad3a3d05cde5820f2578ad510332"
end

-- 调试开关
local DEBUG = false

-- 调试日志函数
local function debugPrint(...)
    -- 完全禁用调试输出
    return
end

-- 全局初始化标记
local serverInitialized = false

-- 初始化模块
local function initializeServer()
    -- 避免重复初始化
    if serverInitialized then
        print("^3[音乐播放器] ^7服务器已经初始化，跳过重复初始化")
        return
    end
    
    -- 避免重复注册网络事件，如果Network模块已经注册了事件
    -- 注释掉下面这行，因为Network模块在lib/network.lua中已经调用了registerEvents
     Network.registerEvents()
    
    -- 获取支持的平台列表
    local platforms = MusicService.getSupportedPlatforms()
    
    -- 检查平台列表格式
    debugPrint("^3[音乐播放器] ^7平台列表类型: " .. type(platforms))
    if type(platforms) == "table" then
        debugPrint("^3[音乐播放器] ^7平台列表长度: " .. #platforms)
        for i, platform in ipairs(platforms) do
            debugPrint("^3[音乐播放器] ^7平台 #" .. i .. ": " .. platform.code .. " - " .. platform.name)
        end
    end
    
    local platformNames = {}
    for _, platform in ipairs(platforms) do
        table.insert(platformNames, platform.name)
    end
    
    -- 输出服务器信息
    debugPrint("^2[音乐播放器] ^7服务端已启动")
    debugPrint("^2[音乐播放器] ^7API地址: " .. API.buildUrl({test = "connection"}):gsub("shykey=[^&]+", "shykey=***"))
    debugPrint("^2[音乐播放器] ^7支持的平台: " .. table.concat(platformNames, ", "))
    
    -- 检查数据库模块是否加载
    if Database then
        debugPrint("^2[音乐播放器] ^7数据库模块已加载")
    else
        debugPrint("^1[音乐播放器] ^7数据库模块未加载，将使用内存缓存")
    end
    
    -- 标记为已初始化
    serverInitialized = true
end

-- 获取玩家标识符
local function GetPlayerIdentifier(source)
    local identifiers = GetPlayerIdentifiers(source)
    local steamId, license
    
    for _, identifier in ipairs(identifiers) do
        if string.find(identifier, "steam:") then
            steamId = identifier
        elseif string.find(identifier, "license:") then
            license = identifier
        end
    end
    
    -- 优先使用steamID，如果没有则使用license
    return steamId or license
end

-- 注册玩家
local function RegisterPlayerOnJoin(source)
    if not Database then return end
    
    local identifier = GetPlayerIdentifier(source)
    local playerName = GetPlayerName(source)
    
    if identifier then
        Database.registerPlayer(identifier, playerName)
    end
end

-- 播放音乐事件
RegisterNetEvent("xsound:playMusic")
AddEventHandler("xsound:playMusic", function(url, volume)
    local source = source
    local playerName = GetPlayerName(source)
    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    
    debugPrint("^3[音乐播放器] ^7玩家 " .. playerName .. " 正在播放音乐")
    debugPrint("^3[音乐播放器] ^7URL: " .. url)
    
    -- 确保URL使用HTTPS
    url = string.gsub(url, "^http:", "https:")
    
    -- 首先让播放者自己听到音乐
    TriggerClientEvent("xsound:playMusicAtClient", source, url, volume, source)
    
    -- 然后向附近的玩家广播音乐
    for _, player in ipairs(GetPlayers()) do
        local targetId = tonumber(player)
        if targetId ~= source then
            local targetCoords = GetEntityCoords(GetPlayerPed(targetId))
            local distance = #(playerCoords - targetCoords)
            
            -- 如果玩家在30米范围内，就让他们听到音乐
            if distance <= 30.0 then
                -- 根据距离计算音量
                local adjustedVolume = volume * (1.0 - (distance / 30.0))
                if adjustedVolume > 0.05 then
                    TriggerClientEvent("xsound:playMusicAtClient", targetId, url, adjustedVolume, source)
                end
            end
        end
    end
end)

-- 服务端直接播放音乐事件（用于处理CORS问题）
RegisterNetEvent("xsound:playMusicFromServer")
AddEventHandler("xsound:playMusicFromServer", function(url, volume, startTime, songData)
    local source = source
    local playerName = GetPlayerName(source)
    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    
    -- 如果传入的是完整的songData对象，直接使用；否则使用传统参数格式
    local songDetails
    if type(songData) == "table" then
        -- 新格式：完整的歌曲数据对象
        songDetails = {
            id = songData.id or songData.songId,
            mid = songData.mid, -- 保留QQ音乐的mid字段
            title = songData.title or songData.name or "未知歌曲",
            artist = songData.artist or songData.singer or "未知艺术家",
            platform = songData.platform or "QQ",
            -- 保留其他可能有用的字段
            album = songData.album,
            duration = songData.duration,
            pic = songData.pic or songData.picUrl or songData.cover,
            search_keyword = songData.search_keyword
        }
        
        -- 如果是QQ音乐但缺少mid字段，尝试从数据库获取
        if songDetails.platform == "QQ" and (not songDetails.mid or songDetails.mid == "") and songDetails.id then
            -- 禁用服务端修复日志
            -- print("^3[服务端修复] ^7QQ音乐缺少mid字段，尝试从数据库获取: " .. songDetails.id)
            
            -- 异步查询数据库获取mid字段，但不阻塞当前播放
            Citizen.CreateThread(function()
                exports.oxmysql:fetch("SELECT mid FROM music_songs WHERE id = ? AND platform = 'QQ' LIMIT 1", {songDetails.id}, function(results)
                    if results and #results > 0 and results[1].mid then
                        local dbMid = results[1].mid
                        -- 禁用服务端修复日志
                        -- print("^2[服务端修复] ^7从数据库获取到mid字段: " .. dbMid)
                        
                        -- 更新songDetails对象（虽然可能太晚了，但为下次做准备）
                        songDetails.mid = dbMid
                        
                        -- 重新广播包含mid字段的歌曲信息，包含播放者ID
                        TriggerClientEvent("xsound:songStarted", -1, songDetails, source)
                    end
                end)
            end)
        end
    else
        -- 兼容旧格式：单独的参数 (url, volume, startTime, songId, title, artist, platform)
        local songId = songData -- 在旧格式中，第4个参数是songId
        local title = args and args[5] or "未知歌曲"
        local artist = args and args[6] or "未知艺术家" 
        local platform = args and args[7] or "QQ"
        
        songDetails = {
            id = songId,
            title = title,
            artist = artist,
            platform = platform
        }
    end
    
    -- 调试输出
    -- print("^3[服务端调试] ^7播放歌曲: " .. songDetails.title .. " - " .. songDetails.artist)
    -- print("^3[服务端调试] ^7歌曲ID: " .. (songDetails.id or "未知"))
    -- print("^3[服务端调试] ^7歌曲平台: " .. songDetails.platform)
    -- print("^3[服务端调试] ^7歌曲MID: " .. (songDetails.mid or "nil"))
    -- print("^3[服务端调试] ^7原始songData类型: " .. type(songData))
    -- if type(songData) == "table" then
    --     print("^3[服务端调试] ^7原始songData.mid: " .. tostring(songData.mid))
    -- end
    
    -- 确保URL使用HTTPS
    url = string.gsub(url, "^http:", "https:")
    
    -- 添加API密钥（如果URL中不包含shykey参数）
    if string.find(url, "shybot.top") and not string.find(url, "shykey=") then
        local apiKey = "96b3597b323680ef20195a49706546d07090ad3a3d05cde5820f2578ad510332"
        if string.find(url, "?") then
            url = url .. "&shykey=" .. apiKey
        else
            url = url .. "?shykey=" .. apiKey
        end
    end
    
    -- 确保startTime是数字
    startTime = tonumber(startTime) or 0
    
    -- 首先让播放者自己听到音乐
    TriggerClientEvent("xsound:playMusicAtClient", source, url, volume, source, startTime)
    
    -- 然后向附近的玩家广播音乐
    for _, player in ipairs(GetPlayers()) do
        local targetId = tonumber(player)
        if targetId ~= source then
            local targetCoords = GetEntityCoords(GetPlayerPed(targetId))
            local distance = #(playerCoords - targetCoords)
            
            -- 如果玩家在30米范围内，就让他们听到音乐
            if distance <= 30.0 then
                -- 根据距离计算音量
                local adjustedVolume = volume * (1.0 - (distance / 30.0))
                if adjustedVolume > 0.05 then
                    TriggerClientEvent("xsound:playMusicAtClient", targetId, url, adjustedVolume, source, startTime)
                end
            end
        end
    end
    
    -- 广播歌曲开始事件，包含播放者ID
    TriggerClientEvent("xsound:songStarted", -1, songDetails, source)
end)

-- 停止音乐事件
RegisterNetEvent("xsound:stopMusic")
AddEventHandler("xsound:stopMusic", function()
    local source = source
    
    -- 通知所有玩家停止来自该玩家的音乐
    TriggerClientEvent("xsound:stopMusicAtClient", -1, source)
end)

-- 暂停音乐事件
RegisterNetEvent("xsound:pauseMusic")
AddEventHandler("xsound:pauseMusic", function()
    local source = source
    
    -- 通知所有玩家暂停来自该玩家的音乐
    TriggerClientEvent("xsound:pauseMusicAtClient", -1, source)
end)

-- 恢复音乐事件
RegisterNetEvent("xsound:resumeMusic")
AddEventHandler("xsound:resumeMusic", function(seekTime)
    local source = source
    local playerName = GetPlayerName(source)
    
    -- 通知所有玩家恢复来自该玩家的音乐
    TriggerClientEvent("xsound:resumeMusicAtClient", -1, source, seekTime)
end)

-- 注意：music:searchMusic事件已在Network模块中注册，这里不再重复注册

-- 设置播放位置事件
RegisterNetEvent("xsound:setTimeStamp")
AddEventHandler("xsound:setTimeStamp", function(time)
    local source = source
    local playerName = GetPlayerName(source)
    
    -- 通知所有玩家设置播放位置
    TriggerClientEvent("xsound:setTimeStampAtClient", -1, source, time)
end)

-- 设置音量事件
RegisterNetEvent("xsound:setVolume")
AddEventHandler("xsound:setVolume", function(volume)
    local source = source
    local playerName = GetPlayerName(source)
    
    -- 通知所有玩家设置音量
    TriggerClientEvent("xsound:setVolumeAtClient", -1, source, volume)
end)

-- 玩家歌单相关事件 --

-- 获取玩家歌单
RegisterNetEvent("music:getPlayerPlaylists")
AddEventHandler("music:getPlayerPlaylists", function()
    if not Database then
        TriggerClientEvent("music:playerPlaylists", source, {})
        return
    end
    
    local source = source
    local identifier = GetPlayerIdentifier(source)
    
    if identifier then
        Database.getPlayerPlaylists(identifier, function(playlists)
            TriggerClientEvent("music:playerPlaylists", source, playlists)
        end)
    else
        TriggerClientEvent("music:playerPlaylists", source, {})
    end
end)

-- 创建歌单
RegisterNetEvent("music:createPlaylist")
AddEventHandler("music:createPlaylist", function(name, description)
    if not Database then
        TriggerClientEvent("music:playlistCreated", source, false, nil, "数据库模块未加载")
        return
    end
    
    local source = source
    local identifier = GetPlayerIdentifier(source)
    local playerName = GetPlayerName(source)
    
    debugPrint("^2[音乐播放器] ^7玩家 " .. playerName .. " 正在创建歌单: " .. name)
    
    if identifier then
        Database.createPlaylist(identifier, name, description, function(playlistId)
            if playlistId and playlistId > 0 then
                debugPrint("^2[音乐播放器] ^7歌单创建成功，ID: " .. playlistId)
                TriggerClientEvent("music:playlistCreated", source, true, playlistId)
            else
                debugPrint("^1[音乐播放器] ^7歌单创建失败")
                TriggerClientEvent("music:playlistCreated", source, false, nil, "创建失败，可能是歌单名称重复")
            end
        end)
    else
        debugPrint("^1[音乐播放器] ^7创建歌单失败: 无法获取玩家标识符")
        TriggerClientEvent("music:playlistCreated", source, false, nil, "无法获取玩家标识符")
    end
end)

-- 添加歌曲到歌单
RegisterNetEvent("music:addSongToPlaylist")
AddEventHandler("music:addSongToPlaylist", function(playlistId, songId, searchKeyword, platform)
    if not Database then
        TriggerClientEvent("music:songAddedToPlaylist", source, false)
        return
    end
    
    local source = source
    
    Database.addSongToPlaylist(playlistId, songId, searchKeyword, platform, function(success)
        TriggerClientEvent("music:songAddedToPlaylist", source, success)
    end)
end)

-- 从歌单移除歌曲
RegisterNetEvent("music:removeSongFromPlaylist")
AddEventHandler("music:removeSongFromPlaylist", function(playlistId, songId)
    if not Database then
        TriggerClientEvent("music:songRemovedFromPlaylist", source, false)
        return
    end
    
    local source = source
    
    Database.removeSongFromPlaylist(playlistId, songId, function(success)
        TriggerClientEvent("music:songRemovedFromPlaylist", source, success)
    end)
end)

-- 获取歌单中的歌曲
RegisterNetEvent("music:getPlaylistSongs")
AddEventHandler("music:getPlaylistSongs", function(playlistId)
    if not Database then
        TriggerClientEvent("music:playlistSongs", source, {})
        return
    end
    
    local source = source
    
    Database.getPlaylistSongs(playlistId, function(songs)
        TriggerClientEvent("music:playlistSongs", source, songs)
    end)
end)

-- 收藏歌曲
RegisterNetEvent("music:addFavoriteSong")
AddEventHandler("music:addFavoriteSong", function(songId, searchKeyword, platform)
    if not Database then
        TriggerClientEvent("music:favoriteSongAdded", source, false)
        return
    end
    
    local source = source
    local identifier = GetPlayerIdentifier(source)
    
    debugPrint("^2[调试] 收藏歌曲: ID=" .. songId .. 
          ", 搜索词=" .. (searchKeyword or "无") .. 
          ", 平台=" .. (platform or "无") .. 
          ", 玩家=" .. (identifier or "无"))
    
    if identifier then
        Database.addFavoriteSong(identifier, songId, searchKeyword, platform, function(success)
            TriggerClientEvent("music:favoriteSongAdded", source, success)
        end)
    else
        TriggerClientEvent("music:favoriteSongAdded", source, false)
    end
end)

-- 取消收藏歌曲
RegisterNetEvent("music:removeFavoriteSong")
AddEventHandler("music:removeFavoriteSong", function(songId)
    if not Database then
        TriggerClientEvent("music:favoriteSongRemoved", source, false)
        return
    end
    
    local source = source
    local identifier = GetPlayerIdentifier(source)
    
    if identifier then
        Database.removeFavoriteSong(identifier, songId, function(success)
            TriggerClientEvent("music:favoriteSongRemoved", source, success)
        end)
    else
        TriggerClientEvent("music:favoriteSongRemoved", source, false)
    end
end)

-- 获取收藏歌曲
RegisterNetEvent("music:getFavoriteSongs")
AddEventHandler("music:getFavoriteSongs", function()
    if not Database then
        TriggerClientEvent("music:favoriteSongs", source, {})
        return
    end
    
    local source = source
    local identifier = GetPlayerIdentifier(source)
    
    if identifier then
        Database.getFavoriteSongs(identifier, function(songs)
            TriggerClientEvent("music:favoriteSongs", source, songs)
        end)
    else
        TriggerClientEvent("music:favoriteSongs", source, {})
    end
end)

-- 检查歌曲是否已收藏
RegisterNetEvent("music:checkSongFavorite")
AddEventHandler("music:checkSongFavorite", function(songId)
    if not Database then
        TriggerClientEvent("music:songFavoriteStatus", source, false)
        return
    end
    
    local source = source
    local identifier = GetPlayerIdentifier(source)
    
    if identifier then
        Database.checkSongFavorite(identifier, songId, function(isFavorite)
            TriggerClientEvent("music:songFavoriteStatus", source, isFavorite)
        end)
    else
        TriggerClientEvent("music:songFavoriteStatus", source, false)
    end
end)

-- 玩家加入时注册
AddEventHandler("playerConnecting", function(playerName, setKickReason, deferrals)
    local source = source
    RegisterPlayerOnJoin(source)
end)

-- 注册玩家
RegisterNetEvent("music:registerPlayer")
AddEventHandler("music:registerPlayer", function()
    if not Database then
        return
    end
    
    local source = source
    local identifier = GetPlayerIdentifier(source)
    local playerName = GetPlayerName(source)
    
    if identifier then
        debugPrint("^2[音乐播放器] ^7注册玩家: " .. playerName .. " (" .. identifier .. ")")
        Database.registerPlayer(identifier, playerName)
    else
        debugPrint("^1[音乐播放器] ^7注册玩家失败: 无法获取玩家标识符")
    end
end)

-- 保存歌曲到数据库
RegisterNetEvent("music:saveSongToDatabase")
AddEventHandler("music:saveSongToDatabase", function(songData)
    if not Database then
        return
    end
    
    if not songData or not songData.id then
        debugPrint("^1[音乐播放器] ^7保存歌曲失败: 无效的歌曲数据")
        return
    end
    
    -- 详细调试信息
    debugPrint("^2[调试] 保存歌曲: ID=" .. songData.id .. 
          ", 标题=" .. (songData.name or songData.title or "无") .. 
          ", 歌手=" .. (songData.singer or songData.artist or "无") .. 
          ", 平台=" .. (songData.platform or "无") .. 
          ", 搜索关键词=" .. (songData.search_keyword or "无"))
    
    -- 确保必要字段存在
    songData.platform = songData.platform or "NETEASE"
    songData.name = songData.name or songData.title or "未知歌曲"
    songData.singer = songData.singer or songData.artist or "未知歌手"
    songData.album = songData.album or "未知专辑"
    songData.duration = tonumber(songData.duration) or 0
    songData.pic = songData.pic or songData.picUrl or songData.cover or ""
    songData.url = songData.url or ""
    
    -- 使用Database模块保存歌曲
    Database.saveSong(songData.id, songData)
    debugPrint("^2[音乐播放器] ^7歌曲已保存到数据库: " .. songData.name)
end)

-- 添加一个通用的HTTP处理程序，用于触发服务器事件
RegisterCommand('triggerServerEvent', function(source, args, rawCommand)
    local eventName = args[1]
    if eventName and source and tonumber(source) > 0 then
        TriggerEvent(eventName, source)
    else
        debugPrint("^1[音乐播放器] ^7触发事件失败: 无效的source或事件名")
    end
end, false)

-- 添加HTTP处理程序
RegisterNetEvent('triggerServerEvent')
AddEventHandler('triggerServerEvent', function(data)
    -- 获取事件触发者的source
    local eventSource = source
    
    -- 调试输出
    debugPrint("^3调试: triggerServerEvent收到请求 - eventSource:", eventSource)
    
    -- 检查data是否有效
    if not data then
        debugPrint("^1错误: triggerServerEvent事件接收到无效的data")
        return
    end
    
    local eventName = data.eventName
    local args = data.args or {}
    
    -- 调试输出
    debugPrint("^3调试: 事件名:", eventName, "参数类型:", type(args))
    
    if eventName then
        -- 直接使用事件的source触发目标事件
        debugPrint("^2调试: 触发事件", eventName, "使用source:", eventSource)
        
        -- 检查args是否是表格
        if type(args) == "table" then
            -- 检查args中是否有source标记
            if args.source == true then
                -- 将事件的source传递给目标事件
                TriggerEvent(eventName, eventSource, args)
            else
                -- 不需要传递source，只传递args作为第二个参数
                TriggerEvent(eventName, args)
            end
        else
            -- 如果不是表格，则创建一个新表格
            TriggerEvent(eventName, {})
        end
    else
        debugPrint("^1错误: 未提供事件名")
    end
end)

-- 处理获取排行榜歌曲的请求
RegisterNetEvent("getRankingSongs")
AddEventHandler("getRankingSongs", function(rankingData)
    local playerSource = source
    
    -- 提取排行榜类型参数
    local rankingType
    
    -- 检查参数类型并获取rankingType
    if type(rankingData) == "table" then
        -- 如果是一个表，尝试提取关键词
        rankingType = rankingData.keyword or rankingData.type
    else
        -- 如果是字符串或其他类型，直接使用
        rankingType = rankingData
    end
    
    -- 打印参数信息用于调试
    print("^3[音乐播放器] ^7接收到排行榜请求 - 参数类型: " .. type(rankingData) .. ", 解析后的排行榜类型: " .. tostring(rankingType))
    
    -- 检查参数
    if not rankingType or rankingType == "" then
        TriggerClientEvent("getRankingSongs:response", playerSource, {
            status = "error",
            message = "缺少排行榜类型参数"
        })
        return
    end
    
    -- 优先从数据库缓存中获取（只查询QQ平台的排行榜）
    exports.oxmysql:fetch("SELECT s.*, o.search_order FROM music_songs s JOIN music_song_search_order o ON s.id = o.song_id WHERE o.search_keyword = ? AND o.platform = 'QQ' ORDER BY o.search_order ASC LIMIT 20", {
        rankingType
    }, function(results)
        if results and #results > 0 then
            debugPrint("从缓存中找到排行榜数据:", #results, "首歌曲")
            
            -- 处理返回格式
            for _, song in ipairs(results) do
                song.pic = song.cover_url
                song.picUrl = song.cover_url
                song.name = song.name
                song.singer = song.singer
                song.platform = "QQ" -- 强制设置为QQ平台
                song.source = "qq"
                
                -- 调试输出数据库中的歌曲数据
                print("^3[排行榜缓存] ^7歌曲: " .. (song.name or "未知") .. " | ID: " .. (song.id or "未知") .. " | MID: " .. (song.mid or "无mid字段"))
                
                -- 如果数据库中的歌曲缺少mid字段，尝试更新它
                if not song.mid or song.mid == "" then
                    print("^1[排行榜缓存] ^7数据库中的歌曲缺少mid字段，尝试获取: " .. (song.name or "未知"))
                    
                    -- 异步获取歌曲详情并更新数据库
                    Citizen.CreateThread(function()
                        API.getSongDetail("QQ", song.id, function(detailResponse)
                            if detailResponse and detailResponse.mid then
                                song.mid = detailResponse.mid
                                print("^2[排行榜缓存] ^7成功获取并更新mid字段: " .. detailResponse.mid .. " for " .. (song.name or "未知"))
                                
                                -- 更新数据库中的mid字段
                                exports.oxmysql:execute("UPDATE music_songs SET mid = ? WHERE id = ?", {detailResponse.mid, song.id})
                            else
                                print("^1[排行榜缓存] ^7无法获取mid字段 for " .. (song.name or "未知"))
                            end
                        end)
                    end)
                end
                
                -- 确保歌曲有URL，如果没有则尝试从API获取
                if (not song.url or song.url == "") and API and API.getSongUrl then
                    -- 尝试获取URL但不等待，直接返回结果
                    -- 这样用户体验更好，即使URL获取失败也能看到歌曲列表
                    Citizen.CreateThread(function()
                        local songUrl = API.getSongUrl(song.platform, song.id)
                        if songUrl and songUrl ~= "" then
                            -- 更新数据库中的URL
                            exports.oxmysql:execute("UPDATE music_songs SET url = ? WHERE id = ?", {songUrl, song.id})
                            -- 但不等待结果
                        end
                    end)
                end
            end
            
            -- 返回给客户端
            TriggerClientEvent("getRankingSongs:response", playerSource, {
                status = "success",
                songs = results,
                type = rankingType
            })
        else
            -- 如果缓存中没有，尝试从API获取
            if API and API.searchMusic then
                -- 排行榜固定使用网易云音乐平台
                local platform = "NETEASE"
                
                print("^2[音乐播放器] ^7排行榜固定使用网易云音乐平台获取数据")
                
                -- 从API获取排行榜数据
                API.searchMusic(platform, rankingType, function(response)
                    if response and #response > 0 then
                        debugPrint("从API获取排行榜数据:", #response, "首歌曲")
                        
                        -- 调试：检查第一首歌的数据结构
                        -- if response[1] then
                        --     print("^3[排行榜调试] ^7第一首歌数据结构:")
                        --     for key, value in pairs(response[1]) do
                        --         if type(value) == "string" and value ~= "" then
                        --             print("^3[排行榜调试] ^7  " .. key .. ": " .. value)
                        --         elseif value == nil then
                        --             print("^3[排行榜调试] ^7  " .. key .. ": nil")
                        --         else
                        --             print("^3[排行榜调试] ^7  " .. key .. ": " .. tostring(value))
                        --         end
                        --     end
                        -- end
                        
                        -- 确保所有歌曲都标记为QQ平台，并尝试获取缺失的mid字段
                        -- local songsNeedingMid = {}
                        -- for i, song in ipairs(response) do
                        --     song.platform = "QQ"
                        --     song.source = "qq"
                            
                        --     -- 如果没有mid字段，记录需要获取详情的歌曲
                        --     if not song.mid or song.mid == "" then
                        --         print("^1[排行榜调试] ^7歌曲缺少mid字段: " .. (song.name or "未知") .. " - ID: " .. (song.id or "未知"))
                        --         table.insert(songsNeedingMid, {index = i, song = song})
                        --     end
                        -- end
                        
                        -- 如果有歌曲缺少mid字段，尝试获取歌曲详情
                        -- if #songsNeedingMid > 0 then
                        --     print("^3[排行榜] ^7有 " .. #songsNeedingMid .. " 首歌曲缺少mid字段，尝试获取详情...")
                            
                        --     local detailsCompleted = 0
                        --     local totalNeeded = #songsNeedingMid
                            
                        --     for _, songInfo in ipairs(songsNeedingMid) do
                        --         API.getSongDetail("QQ", songInfo.song.id, function(detailResponse)
                        --             detailsCompleted = detailsCompleted + 1
                                    
                        --             if detailResponse and detailResponse.mid then
                        --                 response[songInfo.index].mid = detailResponse.mid
                        --                 print("^2[排行榜] ^7成功获取mid字段: " .. detailResponse.mid .. " for " .. (songInfo.song.name or "未知"))
                        --             else
                        --                 print("^1[排行榜] ^7无法获取mid字段 for " .. (songInfo.song.name or "未知"))
                        --             end
                                    
                        --             -- 当所有详情请求完成后，保存并返回结果
                        --             if detailsCompleted >= totalNeeded then
                        --                 -- 保存到数据库
                        --                 if Database then
                        --                     Database.saveSongResults(response, rankingType, platform)
                        --                 end
                                        
                        --                 -- 返回给客户端
                        --                 TriggerClientEvent("getRankingSongs:response", playerSource, {
                        --                     status = "success",
                        --                     songs = response,
                        --                     type = rankingType
                        --                 })
                        --             end
                        --         end)
                        --     end
                        -- else
                            -- 没有歌曲缺少mid字段，直接继续处理
                            -- 保存到数据库
                            if Database then
                                Database.saveSongResults(response, rankingType, platform)
                            end
                            
                            -- 返回给客户端
                            TriggerClientEvent("getRankingSongs:response", playerSource, {
                                status = "success",
                                songs = response,
                                type = rankingType
                            })
                        
                    else
                        -- API也没有数据，输出调试信息
                        print("^1[音乐播放器] ^7排行榜API返回空数据，平台：" .. platform .. "，关键词：" .. rankingType)
                        
                        -- 返回错误响应
                        TriggerClientEvent("getRankingSongs:response", playerSource, {
                            status = "error",
                            message = "未找到排行榜数据"
                        })
                    end
                end, 20) -- 20
            else
                -- API不可用
                TriggerClientEvent("getRankingSongs:response", playerSource, {
                    status = "error",
                    message = "API模块未加载"
                })
            end
        end
    end)
end)

-- 启动服务器
initializeServer()

-- 处理获取歌曲URL的请求
RegisterNetEvent("music:getSongUrl")
AddEventHandler("music:getSongUrl", function(songId, platform)
    if not songId or not platform then
        TriggerClientEvent("music:songUrl", source, nil)
        return
    end
    
    local source = source
    
    -- 先从数据库查询
    exports.oxmysql:fetch("SELECT url FROM music_songs WHERE id = ? AND platform = ? LIMIT 1", {songId, platform}, function(results)
        if results and #results > 0 and results[1].url and results[1].url ~= "" then
            -- 数据库中有URL，直接返回
            TriggerClientEvent("music:songUrl", source, results[1].url)
        else
            -- 数据库中没有URL，尝试从API获取
            if API and API.getSongUrl then
                local url = API.getSongUrl(platform, songId)
                
                -- 如果获取到URL，保存到数据库
                if url and url ~= "" then
                    exports.oxmysql:execute("UPDATE music_songs SET url = ? WHERE id = ? AND platform = ?", {url, songId, platform})
                    -- 这里不等待更新结果
                    
                    -- 返回给客户端
                    TriggerClientEvent("music:songUrl", source, url)
                else
                    -- 未获取到URL
                    TriggerClientEvent("music:songUrl", source, nil)
                end
            else
                -- API不可用
                TriggerClientEvent("music:songUrl", source, nil)
            end
        end
    end)
end)

-- 获取随机数据库歌曲
RegisterNetEvent("getRandomDatabaseSongs")
AddEventHandler("getRandomDatabaseSongs", function(data)
    local source = source
    local limit = 10
    
    -- 检查参数
    if data and type(data) == "table" and data.limit then
        limit = tonumber(data.limit) or 10
    end
    
    debugPrint("^3[音乐播放器] ^7触发服务器事件: getRandomDatabaseSongs, 参数类型:", type(data))
    
    -- 如果没有数据库模块，返回空数组
    if not Database then
        debugPrint("^1[音乐播放器] ^7数据库模块未加载，无法获取随机歌曲")
        -- 使用SendNUIMessage替代TriggerClientEvent
        TriggerClientEvent("send_player_nui_message", source, {
            type = "getRandomDatabaseSongs:response",
            status = "error",
            message = "数据库模块未加载",
            songs = {}
        })
        return
    end
    
    -- 从数据库获取随机歌曲
    exports.oxmysql:fetch("SELECT * FROM music_songs ORDER BY RAND() LIMIT ?", {limit}, function(results)
        if results and #results > 0 then
            debugPrint("^2[音乐播放器] ^7从数据库获取随机歌曲:", #results, "首")
            
            -- 处理歌曲数据格式
            for _, song in ipairs(results) do
                -- 确保图片URL属性存在
                song.pic = song.cover_url
                song.picUrl = song.cover_url
                
                -- 添加其他可能缺失的API返回字段
                -- 处理duration字段，确保是数字类型
                if song.duration then
                    if type(song.duration) == "number" then
                        -- 已经是数字，不需要处理
                    elseif type(song.duration) == "string" then
                        song.duration = tonumber(song.duration) or 0
                    elseif type(song.duration) == "table" then
                        -- 如果是表，尝试转换为字符串后再转为数字
                        song.duration = tonumber(tostring(song.duration)) or 0
                    else
                        song.duration = 0
                    end
                else
                    song.duration = 0
                end
                
                if not song.album then song.album = "未知专辑" end
                -- 确保平台信息一致，使用数据库中保存的平台
                song.source = song.platform:lower()
            end
            
            -- 返回结果，使用SendNUIMessage
            TriggerClientEvent("send_player_nui_message", source, {
                type = "getRandomDatabaseSongs:response",
                status = "success",
                songs = results
            })
        else
            debugPrint("^3[音乐播放器] ^7数据库中没有歌曲或查询失败")
            -- 使用SendNUIMessage替代TriggerClientEvent
            TriggerClientEvent("send_player_nui_message", source, {
                type = "getRandomDatabaseSongs:response",
                status = "error",
                message = "数据库中没有歌曲",
                songs = {}
            })
        end
    end)
end)

-- 获取歌曲信息
RegisterNetEvent("music:getSongInfo")
AddEventHandler("music:getSongInfo", function(songId, platform)
    if not Database then
        TriggerClientEvent("music:songInfo", source, nil)
        return
    end
    
    local source = source
    
    -- 从数据库中查询歌曲信息
    exports.oxmysql:execute('SELECT * FROM music_songs WHERE id = ?', {
        songId
    }, function(results)
        if results and #results > 0 then
            local song = results[1]
            
            -- 确保字段格式正确
            song.title = song.name
            song.artist = song.singer
            song.pic = song.cover_url
            song.picUrl = song.cover_url
            song.source = song.platform:lower()
            song.search_keyword = "每日推荐"
            
            -- 确保封面URL使用HTTPS
            if song.cover_url and song.cover_url:find("^http:") then
                song.cover_url = song.cover_url:gsub("^http:", "https:")
                song.pic = song.cover_url
                song.picUrl = song.cover_url
            end
            
            debugPrint("^2[音乐播放器] ^7从数据库获取到歌曲信息: " .. song.name)
            TriggerClientEvent("music:songInfo", source, song)
        else
            -- 如果数据库中没有，尝试从API获取
            if API and API.getSongDetail then
                API.getSongDetail(platform, songId, function(songDetail)
                    if songDetail then
                        -- 保存到数据库
                        Database.saveSong(songId, songDetail)
                        
                        -- 确保字段格式正确
                        songDetail.title = songDetail.name
                        songDetail.artist = songDetail.singer
                        songDetail.source = songDetail.platform:lower()
                        songDetail.search_keyword = "每日推荐"
                        
                        -- 确保封面URL使用HTTPS
                        if songDetail.pic and songDetail.pic:find("^http:") then
                            songDetail.pic = songDetail.pic:gsub("^http:", "https:")
                            songDetail.picUrl = songDetail.pic
                        end
                        
                        debugPrint("^2[音乐播放器] ^7从API获取到歌曲信息: " .. songDetail.name)
                        TriggerClientEvent("music:songInfo", source, songDetail)
                    else
                        debugPrint("^1[音乐播放器] ^7未找到歌曲信息: " .. songId)
                        TriggerClientEvent("music:songInfo", source, nil)
                    end
                end)
            else
                debugPrint("^1[音乐播放器] ^7API不可用，无法获取歌曲信息")
                TriggerClientEvent("music:songInfo", source, nil)
            end
        end
    end)
end)

-- 导入必要的模块
local HttpRequest = nil
if GetResourceState("http-wrapper") ~= "missing" then
    HttpRequest = exports["http-wrapper"]
else
    HttpRequest = {}
    HttpRequest.fetch = function(url, cb)
        PerformHttpRequest(url, function(err, resp, headers)
            cb(err, resp, headers)
        end, "GET", "", {["Content-Type"] = "application/json"})
    end
end

-- 注册命令处理函数
RegisterCommand('toggleMusicDebug', function(source, args)
    if source == 0 then
        -- 如果是控制台执行
        local isEnabled = MusicService.toggleDebug()
        print("^3[音乐播放器] ^7服务端调试模式: " .. (isEnabled and "已启用" or "已禁用"))
    else
        -- 如果是玩家执行
        local playerName = GetPlayerName(source)
        local isEnabled = MusicService.toggleDebug()
        print("^3[音乐播放器] ^7玩家 " .. playerName .. " 切换了服务端调试模式: " .. (isEnabled and "已启用" or "已禁用"))
        TriggerClientEvent('chatMessage', source, "[音乐播放器]", {0, 255, 255}, "服务端调试模式: " .. (isEnabled and "已启用" or "已禁用"))
    end
end, true) -- true表示只有管理员可以执行这个命令

-- 注册API调试命令
RegisterCommand('toggleAPIDebug', function(source, args)
    if source == 0 then
        -- 如果是控制台执行
        local isEnabled = API.toggleDebug()
        print("^3[音乐播放器] ^7API调试模式: " .. (isEnabled and "已启用" or "已禁用"))
    else
        -- 如果是玩家执行
        local playerName = GetPlayerName(source)
        local isEnabled = API.toggleDebug()
        print("^3[音乐播放器] ^7玩家 " .. playerName .. " 切换了API调试模式: " .. (isEnabled and "已启用" or "已禁用"))
        TriggerClientEvent('chatMessage', source, "[音乐播放器]", {0, 255, 255}, "API调试模式: " .. (isEnabled and "已启用" or "已禁用"))
    end
end, true) -- true表示只有管理员可以执行这个命令

-- 歌词相关事件处理

-- 处理客户端歌词请求
RegisterNetEvent("music:requestLyrics")
AddEventHandler("music:requestLyrics", function(songData, radioId)
    local source = source

    if not songData then
                    -- 禁用歌词系统日志
            -- print("^1[歌词系统] ^7收到无效的歌曲数据请求")
        TriggerClientEvent("music:broadcastLyrics", -1, {id = "unknown"}, "", source, radioId)
        return
    end
    
    -- 详细调试歌词请求收到的数据
    -- print("^3[歌词系统调试] ^7收到歌词请求:")
    -- print("^3[歌词系统调试] ^7  songData类型: " .. type(songData))
    -- if type(songData) == "table" then
    --     print("^3[歌词系统调试] ^7  id: " .. tostring(songData.id))
    --     print("^3[歌词系统调试] ^7  mid: " .. tostring(songData.mid))
    --     print("^3[歌词系统调试] ^7  title: " .. tostring(songData.title))
    --     print("^3[歌词系统调试] ^7  platform: " .. tostring(songData.platform))
    -- end
    
    -- 提取歌词ID和平台信息
    local lyricsId = extractLyricsId(songData)
    local platform = songData.platform or detectPlatformFromSongId(songData.id)
    
    if not lyricsId or lyricsId == "" then
        -- print("^1[歌词系统] ^7无法提取有效的歌词ID")
        TriggerClientEvent("music:broadcastLyrics", -1, songData, "", source, radioId)
        return
    end
    
    -- print("^3[歌词系统] ^7收到歌词请求，歌词ID: " .. tostring(lyricsId) .. ", 平台: " .. (platform or "AUTO"))
    
    -- 异步获取歌词
    Citizen.CreateThread(function()
        local lyrics = getLyrics(lyricsId, platform)

        if lyrics and lyrics ~= "" then
            -- 禁用歌词系统日志
            -- print("^2[歌词系统] ^7成功获取歌词，长度: " .. string.len(lyrics))
            -- 广播歌词给所有玩家，但指定特定的音乐盒ID
            TriggerClientEvent("music:broadcastLyrics", -1, songData, lyrics, source, radioId)
        else
            -- 禁用歌词系统日志
            -- print("^1[歌词系统] ^7未能获取歌词，尝试其他平台")
            -- 如果当前平台失败，尝试其他平台
            lyrics = tryOtherPlatforms(lyricsId, platform)
            TriggerClientEvent("music:broadcastLyrics", -1, songData, lyrics or "", source, radioId)
        end
    end)
end)

-- 获取歌词的函数
function getLyrics(songId, platform)
    if not songId or songId == "" then
        return nil
    end
    
    -- 默认平台为网易云音乐（通常歌词最全）
    platform = platform or "NETEASE"
    
    -- print("^3[歌词系统] ^7开始请求歌词，歌曲ID: " .. tostring(songId) .. ", 平台: " .. platform)
    
    -- 使用同步方式等待HTTP响应
    local lyricsData = nil
    local requestCompleted = false
    
    -- 确保平台存在于 API.PLATFORMS 中
    if not API.PLATFORMS[platform:upper()] then
        platform = "NETEASE" -- 默认使用网易云
        -- 禁用歌词系统日志
        -- print("^3[歌词系统] ^7使用默认平台: " .. platform)
    end
    
    -- 构建请求参数 - 使用_lrc后缀表示获取歌词
    local requestParams = {
        type = API.PLATFORMS[platform:upper()] .. "_lrc"
    }
    
    -- 不同平台使用不同的参数名
    if platform == "KUGOU" then
        requestParams.hash = tostring(songId)
        requestParams.f = "1"  -- 酷狗歌词API需要的额外参数
    else
        requestParams.id = tostring(songId)
    end
    
    -- 发起API请求
    API.request(requestParams, function(response, statusCode)
        -- print("^3[歌词系统] ^7收到响应，状态码: " .. tostring(statusCode))
        
        if statusCode == 200 and response then
            -- 检查响应结构
            if type(response) == "string" then
                -- 如果响应直接是歌词字符串
                lyricsData = response
                -- 禁用歌词系统日志
                -- print("^2[歌词系统] ^7成功获取歌词数据(字符串格式)")
            elseif response.lrc then
                -- 直接返回lrc字段
                lyricsData = response.lrc
                -- 禁用歌词系统日志
                -- print("^2[歌词系统] ^7成功获取歌词数据(lrc字段)")
            elseif response.data and response.data.lrc then
                -- 从data.lrc字段获取
                lyricsData = response.data.lrc
                -- 禁用歌词系统日志
                -- print("^2[歌词系统] ^7成功获取歌词数据(data.lrc字段)")
            elseif response.lyric then
                -- 某些API使用lyric字段
                lyricsData = response.lyric
                -- 禁用歌词系统日志
                -- print("^2[歌词系统] ^7成功获取歌词数据(lyric字段)")
            else
                -- 禁用歌词系统日志
                -- print("^1[歌词系统] ^7响应中未找到歌词数据")
                -- print("^1[歌词系统] ^7响应结构: " .. json.encode(response):sub(1, 500) .. "...")
            end
        else
            -- print("^1[歌词系统] ^7歌词请求失败，状态码: " .. tostring(statusCode))
            if response and response.msg then
                -- print("^1[歌词系统] ^7错误信息: " .. response.msg)
            end
        end
        
        requestCompleted = true
    end)
    
    -- 等待请求完成（最多等待10秒）
    local waitTime = 0
    local maxWaitTime = 10000 -- 10秒
    local checkInterval = 100 -- 100毫秒检查一次
    
    while not requestCompleted and waitTime < maxWaitTime do
        Citizen.Wait(checkInterval)
        waitTime = waitTime + checkInterval
    end
    
    if not requestCompleted then
        -- print("^1[歌词系统] ^7歌词请求超时")
        return nil
    end
    
    return lyricsData
end

-- 从歌曲数据中提取歌词ID
function extractLyricsId(songData)
    if not songData then return nil end
    
    -- 获取平台信息
    local originalPlatform = songData.platform
    local detectedPlatform = detectPlatformFromSongId(songData.id)
    local platform = originalPlatform or detectedPlatform
    
    -- 调试平台选择逻辑
    -- print("^3[平台选择调试] ^7歌曲ID: " .. (songData.id or "nil"))
    -- print("^3[平台选择调试] ^7原始平台: " .. (originalPlatform or "nil"))
    -- print("^3[平台选择调试] ^7检测平台: " .. detectedPlatform)
    -- print("^3[平台选择调试] ^7最终平台: " .. platform)
    
    -- 如果原始平台和检测平台不一致，使用检测平台（更准确）
    if originalPlatform and originalPlatform ~= detectedPlatform then
        -- print("^1[平台选择警告] ^7平台不一致！使用检测到的平台: " .. detectedPlatform)
        platform = detectedPlatform
    end
    
    if platform == "QQ" then
        -- QQ音乐必须使用 mid 字段作为歌词ID
        if songData.mid and songData.mid ~= "" then
            -- print("^3[歌词系统] ^7QQ音乐使用mid作为歌词ID: " .. songData.mid)
            return songData.mid
        else
            -- print("^1[歌词系统] ^7QQ音乐缺少mid字段，无法获取歌词。ID: " .. (songData.id or "未知"))
            return nil -- 没有mid字段就不请求歌词
        end
    elseif platform == "NETEASE" then
        -- 网易云音乐使用数字ID
        if songData.id then
            -- print("^3[歌词系统] ^7网易云音乐使用id作为歌词ID: " .. songData.id)
            return songData.id
        end
    elseif platform == "XMLY" then
        -- 喜马拉雅使用ID
        if songData.id then
            -- print("^3[歌词系统] ^7喜马拉雅使用id作为歌词ID: " .. songData.id)
            return songData.id
        end
    else
        -- 其他平台或未知平台，使用通用ID
        if songData.id then
            -- print("^3[歌词系统] ^7未知平台使用id作为歌词ID: " .. songData.id)
            return songData.id
        end
    end
    
    -- print("^1[歌词系统] ^7无法从歌曲数据中提取歌词ID")
    return nil
end

-- 从歌曲ID推断平台（优先使用QQ音乐）
function detectPlatformFromSongId(songId)
    if not songId then return "QQ" end
    
    local songIdStr = tostring(songId)
    local detectedPlatform
    
    -- 喜马拉雅ID格式（明确标识）
    if string.find(songIdStr, "XMLY") then
        detectedPlatform = "XMLY"
    -- 网易云音乐ID：纯数字且长度在6-8位之间
    elseif string.match(songIdStr, "^%d+$") and string.len(songIdStr) >= 6 and string.len(songIdStr) <= 15 then
        detectedPlatform = "NETEASE"
    else
        -- 其他所有情况都默认为QQ音乐（包括长数字ID和字母数字组合）
        detectedPlatform = "QQ"
    end
    
    -- print("^3[平台检测] ^7歌曲ID: " .. songIdStr .. " (长度: " .. string.len(songIdStr) .. ") -> 平台: " .. detectedPlatform)
    return detectedPlatform
end

-- 尝试其他平台获取歌词（只使用启用的平台）
function tryOtherPlatforms(songId, currentPlatform)
    local platforms = {"QQ", "NETEASE", "XMLY"}
    
    for _, platform in ipairs(platforms) do
        if platform ~= currentPlatform then
            -- print("^3[歌词系统] ^7尝试平台: " .. platform)
            local lyrics = getLyrics(songId, platform)
            if lyrics and lyrics ~= "" then
                -- print("^2[歌词系统] ^7在平台 " .. platform .. " 找到歌词")
                return lyrics
            end
        end
    end
    
    -- print("^1[歌词系统] ^7所有平台都未找到歌词")
    return nil
end

-- 测试歌词API的命令
RegisterCommand("testlyricsapi", function(source, args)
    if not args[1] then
        if source == 0 then
            print("^1[歌词测试] ^7请提供歌曲ID [平台]")
        else
            TriggerClientEvent('chatMessage', source, "[歌词测试]", {255, 0, 0}, "用法: /testlyricsapi [歌曲ID] [平台]")
        end
        return
    end
    
    local songId = args[1]
    local platform = args[2] or detectPlatformFromSongId(songId)
    
    if source == 0 then
        print("^3[歌词测试] ^7开始测试歌词API，歌曲ID: " .. songId .. ", 平台: " .. platform)
    else
        TriggerClientEvent('chatMessage', source, "[歌词测试]", {255, 165, 0}, "测试歌词API，ID: " .. songId .. ", 平台: " .. platform)
    end
    
    -- 异步测试
    Citizen.CreateThread(function()
        local lyrics = getLyrics(songId, platform)
        
        if lyrics and lyrics ~= "" then
            local lyricsLength = string.len(lyrics)
            local lineCount = 0
            for line in string.gmatch(lyrics, "[^\r\n]+") do
                lineCount = lineCount + 1
            end
            
            if source == 0 then
                print("^2[歌词测试] ^7成功获取歌词，长度: " .. lyricsLength .. " 字符，行数: " .. lineCount)
                print("^2[歌词测试] ^7歌词预览: " .. string.sub(lyrics, 1, 200) .. "...")
            else
                TriggerClientEvent('chatMessage', source, "[歌词测试]", {0, 255, 0}, "成功获取歌词，长度: " .. lyricsLength .. " 字符，行数: " .. lineCount)
            end
        else
            if source == 0 then
                print("^1[歌词测试] ^7未能获取歌词，尝试其他平台...")
                -- 尝试其他平台
                lyrics = tryOtherPlatforms(songId, platform)
                if lyrics and lyrics ~= "" then
                    print("^2[歌词测试] ^7在其他平台找到歌词")
                else
                    print("^1[歌词测试] ^7所有平台都未找到歌词")
                end
            else
                TriggerClientEvent('chatMessage', source, "[歌词测试]", {255, 0, 0}, "未能获取歌词")
            end
        end
    end)
end, false)

-- 测试排行榜数据结构的命令
RegisterCommand('testranking', function(source, args)
    local rankingType = args[1] or "热歌榜"
    
    TriggerClientEvent('chatMessage', source, "[排行榜测试]", {255, 255, 0}, "正在获取排行榜数据: " .. rankingType)
    print("^3[排行榜测试] 开始测试，排行榜类型: " .. rankingType)
    
    -- 固定使用QQ音乐平台
    local platform = "QQ"
    
    API.searchMusic(platform, rankingType, function(response)
        if response and #response > 0 then
            print("^2[排行榜测试] ^7成功获取 " .. #response .. " 首歌曲")
            TriggerClientEvent('chatMessage', source, "[排行榜测试]", {0, 255, 0}, "成功获取 " .. #response .. " 首歌曲")
            
            -- 检查前3首歌的数据结构
            for i = 1, math.min(3, #response) do
                local song = response[i]
                print("^3[排行榜测试] ^7歌曲 #" .. i .. " 数据结构:")
                print("^3[排行榜测试] ^7  名称: " .. (song.name or "nil"))
                print("^3[排行榜测试] ^7  歌手: " .. (song.singer or "nil"))
                print("^3[排行榜测试] ^7  ID: " .. (song.id or "nil"))
                print("^3[排行榜测试] ^7  MID: " .. (song.mid or "nil"))
                print("^3[排行榜测试] ^7  平台: " .. (song.platform or "nil"))
                
                TriggerClientEvent('chatMessage', source, "[排行榜测试]", {0, 255, 255}, 
                    "歌曲#" .. i .. ": " .. (song.name or "nil") .. " | ID:" .. (song.id or "nil") .. " | MID:" .. (song.mid or "nil"))
            end
        else
            print("^1[排行榜测试] ^7获取排行榜数据失败")
            TriggerClientEvent('chatMessage', source, "[排行榜测试]", {255, 0, 0}, "获取排行榜数据失败")
        end
    end)
end, false)



-- 测试歌曲详情API的命令
RegisterCommand('testsongdetail', function(source, args)
    if not args[1] then
        TriggerClientEvent('chatMessage', source, "[歌曲详情测试]", {255, 0, 0}, "使用方法: /testsongdetail [歌曲ID]")
        return
    end
    
    local songId = args[1]
    local platform = "QQ"
    
    TriggerClientEvent('chatMessage', source, "[歌曲详情测试]", {255, 255, 0}, "正在获取歌曲详情: " .. songId)
    print("^3[歌曲详情测试] 开始测试，歌曲ID: " .. songId .. ", 平台: " .. platform)
    
    API.getSongDetail(platform, songId, function(response)
        if response then
            print("^2[歌曲详情测试] ^7成功获取歌曲详情:")
            print("^2[歌曲详情测试] ^7  名称: " .. (response.name or "nil"))
            print("^2[歌曲详情测试] ^7  歌手: " .. (response.singer or "nil"))
            print("^2[歌曲详情测试] ^7  ID: " .. (response.id or "nil"))
            print("^2[歌曲详情测试] ^7  MID: " .. (response.mid or "nil"))
            print("^2[歌曲详情测试] ^7  平台: " .. (response.platform or "nil"))
            print("^2[歌曲详情测试] ^7  URL: " .. (response.url or "nil"))
            
            TriggerClientEvent('chatMessage', source, "[歌曲详情测试]", {0, 255, 0}, 
                "歌曲: " .. (response.name or "nil") .. "  < /dev/null |  MID: " .. (response.mid or "nil"))
        else
            print("^1[歌曲详情测试] ^7获取歌曲详情失败")
            TriggerClientEvent('chatMessage', source, "[歌曲详情测试]", {255, 0, 0}, "获取歌曲详情失败")
        end
    end)
end, false)

-- 在适当位置添加音乐盒使用权限提示
RegisterCommand("radioinfo1", function(source, args, rawCommand)
    -- 获取当前玩家坐标
    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    
    -- 获取音乐盒数据
    TriggerEvent('music:getRadioData', function(radioData, playerRadioData)
        -- 检查附近是否有音乐盒
        local nearbyRadios = {}
        
        if radioData then
            for id, radioInfo in pairs(radioData) do
                local distance = #(playerCoords - radioInfo.coords)
                if distance <= 5.0 then
                    table.insert(nearbyRadios, {
                        id = id,
                        creator = radioInfo.creator,
                        coords = radioInfo.coords,
                        distance = distance
                    })
                end
            end
        else
            TriggerClientEvent("chatMessage", source, "[音乐盒系统]", {255, 0, 0}, "无法获取音乐盒数据")
            return
        end
        
        -- 如果没有找到音乐盒
        if #nearbyRadios == 0 then
            TriggerClientEvent("chatMessage", source, "[音乐盒系统]", {255, 0, 0}, "附近没有音乐盒")
            return
        end
        
        -- 排序音乐盒，按距离从近到远
        table.sort(nearbyRadios, function(a, b)
            return a.distance < b.distance
        end)
        
        -- 输出信息
        TriggerClientEvent("chatMessage", source, "[音乐盒系统]", {0, 255, 0}, "附近的音乐盒：")
        for i, radio in ipairs(nearbyRadios) do
            local ownerName = GetPlayerName(radio.creator) or "离线玩家"
            local creatorId = radio.creator
            local isOwner = (creatorId == source)
            local statusMsg = isOwner and "（你的音乐盒）" or "（其他人的音乐盒，你不能使用）"
            
            TriggerClientEvent("chatMessage", source, "", {255, 255, 255}, "音乐盒 #" .. radio.id .. " - 所有者: " .. ownerName .. " (ID: " .. creatorId .. ") " .. statusMsg)
        end
    end)
end, false)

-- 添加提示信息
AddEventHandler('playerSpawned', function()
    local source = source
    Citizen.Wait(5000)
    TriggerClientEvent("chatMessage", source, "[音乐盒系统]", {255, 255, 0}, "提示：只有音乐盒所有者才能使用音乐盒！")
end)

-- 添加简单的radioinfo命令，方便玩家使用
RegisterCommand("radioinfo", function(source, args, rawCommand)
    -- 直接转发到详细命令
    ExecuteCommand('radioinfo1')
end, false)
