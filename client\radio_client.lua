-- 音乐盒客户端脚本

-- 添加字符串trim方法
function string:trim()
    return self:match("^%s*(.-)%s*$")
end

local radioObjects = {} -- 存储所有音乐盒对象
local nearestRadio = nil -- 最近的音乐盒
local radioInteractionDistance = 2.0 -- 交互距离
local radioModel = "prop_boombox_01" -- 便携式音响模型
local DEBUG = false -- 启用调试模式来诊断问题
local currentSongInfo = nil -- 当前播放的歌曲信息


-- 存储音乐播放相关数据
local currentLyrics = "" -- 当前歌词
local lyricsArray = {} -- 解析后的歌词数组
local currentLyricIndex = 1 -- 当前显示的歌词索引
local lyricsUpdateTime = 0 -- 歌词更新时间戳

-- 添加音乐盒与播放的歌曲映射关系
local radioSongMap = {} -- 存储每个音乐盒正在播放的歌曲
local radioLyricsMap = {} -- 存储每个音乐盒的歌词

-- 扛音响相关变量
local PlayerProps = {}
local PlayerHasProp = false
local IsInBoomboxAnimation = false
local currentRadioId = nil -- 当前交互的音乐盒ID

-- 歌词特效设置
local lyricsEffect = {
    enabled = true,     -- 是否启用特效
    type = "rainbow",   -- 特效类型：rainbow（彩虹）, pulse（脉动）, neon（霓虹灯）
    speed = 1.0,        -- 特效速度
    intensity = 1.0     -- 特效强度
}

-- 显示下一句歌词
local showNextLyric = true -- 是否显示下一句歌词

-- 字体大小设置
local fontSettings = {
    size = 0.45,          -- 当前歌词字体大小
    nextLineSize = 0.38,  -- 下一行歌词字体大小
    lineSpacing = 0.09,   -- 行间距
    nextLineOffset = 0.12, -- 下一句歌词的偏移量
    baseHeight = 0.8      -- 歌词基准高度
}

-- 创建3D文本
local function Draw3DText(x, y, z, text, effectType, isNextLine)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px, py, pz, x, y, z, 1)
    
    local scale = (1 / dist) * 1.8
    local fov = (1 / GetGameplayCamFov()) * 100
    local scale = scale * fov
    
    if onScreen then
        -- 如果是下一句歌词，字体稍小且透明度降低
        if isNextLine then
            SetTextScale(0.0, fontSettings.nextLineSize * scale)
            -- 为下一句歌词设置特殊颜色或效果
            SetTextColour(180, 180, 180, 200) -- 灰色，半透明
        else
            SetTextScale(0.0, fontSettings.size * scale)
            
            -- 应用特效，只对当前歌词应用特效
            if lyricsEffect.enabled and effectType then
                local r, g, b = 255, 255, 255 -- 默认白色
                
                if effectType == "rainbow" then
                    -- 彩虹颜色效果
                    local time = GetGameTimer() / 1000 * lyricsEffect.speed
                    r = math.floor(math.sin(time) * 127 + 128)
                    g = math.floor(math.sin(time + 2) * 127 + 128)
                    b = math.floor(math.sin(time + 4) * 127 + 128)
                elseif effectType == "pulse" then
                    -- 脉动效果
                    local pulse = math.sin(GetGameTimer() / 300 * lyricsEffect.speed) * 0.2 * lyricsEffect.intensity + 0.8
                    SetTextScale(0.0, fontSettings.size * scale * pulse)
                elseif effectType == "neon" then
                    -- 霓虹灯效果
                    r, g, b = 0, 162, 255 -- 霓虹蓝色
                    SetTextEdge(4, 0, 162, 255, 255) -- 增强边缘发光效果
                end
                
                SetTextColour(r, g, b, 255)
            else
                SetTextColour(255, 255, 255, 255)
            end
        end
        
        SetTextFont(0)
        SetTextProportional(1)
        SetTextDropshadow(0, 0, 0, 0, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- 绘制3D歌词
local function Draw3DLyrics(x, y, z, lyrics, songInfo)
    -- 显示歌曲信息（总是显示，不再依赖DEBUG变量）
    local songInfoText = "正在播放..."
    if songInfo then
        -- 如果有歌曲信息，显示标题和艺术家
        local title = songInfo.title or "未知歌曲"
        local artist = songInfo.artist or "未知艺术家"
        songInfoText = title .. " - " .. artist
    end
    
    -- 在歌词的上方显示歌曲信息
    Draw3DText(x, y, z + fontSettings.baseHeight + 0.1, songInfoText, "neon") 

    -- 检查歌词中是否有换行符
    if string.find(lyrics, "\n") then
        -- 分割歌词为多行
        local lines = {}
        for line in string.gmatch(lyrics, "[^\r\n]+") do
            table.insert(lines, line)
        end
        
        -- 绘制每一行，每行之间有一定的高度偏移
        local yOffset = 0
        for _, line in ipairs(lines) do
            Draw3DText(x, y, z + fontSettings.baseHeight - yOffset, line, lyricsEffect.type)
            yOffset = yOffset + fontSettings.lineSpacing
        end
    else
        -- 显示当前歌词
        Draw3DText(x, y, z + fontSettings.baseHeight, lyrics, lyricsEffect.type)
        
        -- 显示下一行歌词（如果启用了显示下一行且有关联的歌词数据）
        if showNextLyric and songInfo and songInfo.nextLyric then
            Draw3DText(x, y, z + fontSettings.baseHeight - fontSettings.nextLineOffset, songInfo.nextLyric, nil, true)
        end
    end
end

-- 调试日志函数
local function debugPrint(...)
    if DEBUG then
        -- 只在控制台输出，不在游戏中显示
        print("^3[音乐盒调试]^7", ...)
    end
end

-- 创建提示文本
local function DisplayHelpText(text)
    BeginTextCommandDisplayHelp("STRING")
    AddTextComponentSubstringPlayerName(text)
    EndTextCommandDisplayHelp(0, false, true, -1)
end

-- 播放动画
local function StartAnimation(animDict, animName)
    RequestAnimDict(animDict)
    local timeout = 0
    while not HasAnimDictLoaded(animDict) and timeout < 100 do
        Wait(10)
        timeout = timeout + 1
    end
    
    if HasAnimDictLoaded(animDict) then
        TaskPlayAnim(PlayerPedId(), animDict, animName, 8.0, -8.0, -1, 0, 0, false, false, false)
    end
end





-- 同步音乐盒事件
RegisterNetEvent("radio:syncRadio")
AddEventHandler("radio:syncRadio", function(radioData)
    -- 如果已经存在这个音乐盒，保留歌曲和歌词数据
    local existingSongData = nil
    local existingLyricsData = nil

    if radioObjects[radioData.id] then
        -- 保存现有的歌曲和歌词数据
        existingSongData = radioSongMap[radioData.id]
        existingLyricsData = radioLyricsMap[radioData.id]

        -- 删除旧的音乐盒对象
        if radioObjects[radioData.id].object then
            DeleteObject(radioObjects[radioData.id].object)
        end
    end
    
    -- 请求模型
    local modelHash = GetHashKey(radioModel)
    RequestModel(modelHash)
    while not HasModelLoaded(modelHash) do
        Wait(10)
    end
    
    -- 在指定位置创建音乐盒物体
    local radio = CreateObject(modelHash, 
        radioData.coords.x, radioData.coords.y, radioData.coords.z,
        true, false, false)
    
    -- 设置音乐盒方向
    SetEntityRotation(radio, 
        radioData.rotation.x, radioData.rotation.y, radioData.rotation.z, 
        2, true)
    
    -- 设置音乐盒物理属性
    SetEntityAsMissionEntity(radio, true, true)
    FreezeEntityPosition(radio, true)
    SetEntityCollision(radio, true, true)
    
    -- 存储音乐盒信息
    radioObjects[radioData.id] = {
        id = radioData.id,
        creator = radioData.creator,
        creatorName = radioData.creatorName,
        creatorServerId = radioData.creator, -- 保存创建者的服务器ID
        coords = radioData.coords,
        rotation = radioData.rotation,
        object = radio,
        timestamp = radioData.timestamp,
        testSongIndex = radioData.testSongIndex, -- 保存测试歌曲索引
        isTest = radioData.isTest -- 保存是否为测试音乐盒的标记
    }

    -- 恢复保存的歌曲和歌词数据（如果有的话）
    if existingSongData then
        radioSongMap[radioData.id] = existingSongData
        -- print("^2[音乐盒-扛音响] 恢复歌曲数据: " .. radioData.id)
    end

    if existingLyricsData then
        radioLyricsMap[radioData.id] = existingLyricsData
        -- print("^2[音乐盒-扛音响] 恢复歌词数据: " .. radioData.id)
    end

    -- 释放模型
    SetModelAsNoLongerNeeded(modelHash)

    -- 添加ox_target交互菜单
    AddRadioTargetOptions(radio, radioData.id)
end)

-- 向音乐盒添加ox_target交互选项
function AddRadioTargetOptions(radioObj, radioId)
    -- 检查ox_target是否可用
    if not exports['ox_target'] then return end
    
    -- 获取音乐盒数据
    local radioData = radioObjects[radioId]
    if not radioData then return end
    
    -- 获取自己的服务器ID
    local myServerId = GetPlayerServerId(PlayerId())
    
    -- 是否是自己的音乐盒
    local isOwner = (radioData.creator == myServerId)
    
    -- 基础选项
    local options = {}
    
    -- 音乐盒所有者的选项
    if isOwner then
        table.insert(options, {
            name = 'radio_open_player',
            icon = 'fas fa-music',
            label = '打开播放器',
            onSelect = function()
                -- 触发打开音乐播放器事件
                TriggerEvent("music:openFromRadio")
            end
        })
        
        -- 添加所有者可用的暂停音乐选项
        table.insert(options, {
            name = 'radio_pause_music_owner',
            icon = 'fas fa-pause',
            label = '暂停音乐',
            onSelect = function()
                -- 获取音频ID
                local audioId = "music_" .. myServerId
                
                -- 检查是否有音频在播放
                if exports["xsound"] and exports["xsound"]:soundExists(audioId) and exports["xsound"]:isPlaying(audioId) then
                    -- 发送暂停请求到服务器
                    TriggerServerEvent("radio:requestPauseMusic", myServerId, radioId)
                    
                    -- 显示提示
                    TriggerEvent('chat:addMessage', {
                        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 165, 0, 0.6); border-radius: 3px;"><i class="fas fa-pause"></i> {0}: {1}</div>',
                        args = {"音乐盒", "您已暂停自己的音乐盒 #" .. radioId .. " 的音乐"}
                    })
                else
                    -- 没有音乐在播放
                    TriggerEvent('chat:addMessage', {
                        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 0, 0, 0.6); border-radius: 3px;"><i class="fas fa-times"></i> {0}: {1}</div>',
                        args = {"音乐盒", "您的音乐盒当前没有播放音乐"}
                    })
                end
            end
        })
        
        -- 添加所有者可用的恢复音乐选项
        table.insert(options, {
            name = 'radio_resume_music_owner',
            icon = 'fas fa-play',
            label = '恢复音乐',
            onSelect = function()
                -- 获取音频ID
                local audioId = "music_" .. myServerId
                
                -- 检查是否有音频被暂停
                if exports["xsound"] and exports["xsound"]:soundExists(audioId) and not exports["xsound"]:isPlaying(audioId) then
                    -- 发送恢复请求到服务器
                    TriggerServerEvent("radio:requestResumeMusic", myServerId, radioId)
                    
                    -- 显示提示
                    TriggerEvent('chat:addMessage', {
                        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(0, 255, 0, 0.6); border-radius: 3px;"><i class="fas fa-play"></i> {0}: {1}</div>',
                        args = {"音乐盒", "您已恢复自己的音乐盒 #" .. radioId .. " 的音乐"}
                    })
                else
                    -- 没有被暂停的音乐
                    TriggerEvent('chat:addMessage', {
                        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 0, 0, 0.6); border-radius: 3px;"><i class="fas fa-times"></i> {0}: {1}</div>',
                        args = {"音乐盒", "您的音乐盒当前没有被暂停的音乐"}
                    })
                end
            end
        })
        

        
        -- 扛起音响动作选项
        table.insert(options, {
            name = 'radio_carry_boombox',
            icon = 'fas fa-music',
            label = '恐龙抗狼',
            onSelect = function()
                -- 触发扛起音响动作
                TriggerCarryBoomboxAction(radioId)
            end
        })

        table.insert(options, {
            name = 'radio_pickup',
            icon = 'fas fa-hand-paper',
            label = '收起音乐盒',
            onSelect = function()
                -- 拾起音乐盒
                PickupRadio(radioId)
            end
        })
    else
        -- 不是所有者时显示提示选项
        table.insert(options, {
            name = 'radio_check_owner',
            icon = 'fas fa-info-circle',
            label = '查看音乐盒所有者',
            onSelect = function()
                -- 显示所有者信息
                local ownerName = GetPlayerName(radioData.creator) or "未知玩家"
                TriggerEvent('chat:addMessage', {
                    template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(48, 124, 232, 0.6); border-radius: 3px;"><i class="fas fa-music"></i> {0}: {1}</div>',
                    args = {"音乐盒", "这个音乐盒属于 " .. ownerName .. " (ID: " .. radioData.creator .. ")"}
                })
            end
        })
        
        -- 添加暂停音乐的选项（适用于所有非所有者玩家）
        table.insert(options, {
            name = 'radio_pause_music',
            icon = 'fas fa-pause',
            label = '暂停音乐',
            onSelect = function()
                -- 获取音乐盒所有者的音频ID
                local audioId = "music_" .. radioData.creator
                
                -- 检查是否有音频在播放
                if exports["xsound"] and exports["xsound"]:soundExists(audioId) and exports["xsound"]:isPlaying(audioId) then
                    -- 发送暂停请求到服务器（由服务器广播给所有玩家）
                    TriggerServerEvent("radio:requestPauseMusic", radioData.creator, radioId)
                    
                    -- 显示提示
                    TriggerEvent('chat:addMessage', {
                        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 165, 0, 0.6); border-radius: 3px;"><i class="fas fa-pause"></i> {0}: {1}</div>',
                        args = {"音乐盒", "您已请求暂停此音乐盒 #" .. radioId .. " 的音乐"}
                    })
                else
                    -- 没有音乐在播放
                    TriggerEvent('chat:addMessage', {
                        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 0, 0, 0.6); border-radius: 3px;"><i class="fas fa-times"></i> {0}: {1}</div>',
                        args = {"音乐盒", "此音乐盒当前没有播放音乐"}
                    })
                end
            end
        })
        
        -- 添加恢复音乐的选项
        table.insert(options, {
            name = 'radio_resume_music',
            icon = 'fas fa-play',
            label = '恢复音乐',
            onSelect = function()
                -- 获取音乐盒所有者的音频ID
                local audioId = "music_" .. radioData.creator
                
                -- 检查是否有音频被暂停
                if exports["xsound"] and exports["xsound"]:soundExists(audioId) and not exports["xsound"]:isPlaying(audioId) then
                    -- 发送恢复请求到服务器
                    TriggerServerEvent("radio:requestResumeMusic", radioData.creator, radioId)
                    
                    -- 显示提示
                    TriggerEvent('chat:addMessage', {
                        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(0, 255, 0, 0.6); border-radius: 3px;"><i class="fas fa-play"></i> {0}: {1}</div>',
                        args = {"音乐盒", "您已请求恢复此音乐盒 #" .. radioId .. " 的音乐"}
                    })
                else
                    -- 没有被暂停的音乐
                    TriggerEvent('chat:addMessage', {
                        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 0, 0, 0.6); border-radius: 3px;"><i class="fas fa-times"></i> {0}: {1}</div>',
                        args = {"音乐盒", "此音乐盒当前没有被暂停的音乐"}
                    })
                end
            end
        })
    end
    
    -- 添加选项到实体
    if #options > 0 then
    exports['ox_target']:addLocalEntity(radioObj, options)
    end
end

-- 拾起音乐盒
function PickupRadio(radioId)
    -- 检查是否是自己的音乐盒
    local radioData = radioObjects[radioId]
    if not radioData then return end
    
    -- 获取自己的服务器ID
    local myServerId = GetPlayerServerId(PlayerId())
    
    -- 检查是否是自己放置的音乐盒
    if radioData.creator ~= myServerId then
        TriggerEvent("chatMessage", "[系统]", {255, 0, 0}, "这不是您放置的音乐盒，您不能收起它")
        return
    end
    

    
    -- 播放拾起动画
    local playerPed = PlayerPedId()
    StartAnimation("anim@heists@narcotics@trash", "pickup")
    
    -- 等待动画播放
    Wait(700)
    
    -- 清理动画
    ClearPedTasks(playerPed)
    
    -- 停止正在播放的音乐
    -- 获取玩家服务端ID
    local myServerId = GetPlayerServerId(PlayerId())
    
    -- 停止所有音频播放
    TriggerServerEvent("xsound:stopMusic")
    
    -- 确保所有音频停止
    if myServerId then
        local audioId = "music_" .. myServerId
        if exports["xsound"] and exports["xsound"]:soundExists(audioId) then
            exports["xsound"]:Destroy(audioId)
        end
    end
    
    -- 重置歌曲信息
    currentSongInfo = nil
    
    -- 清理该音乐盒的歌曲和歌词数据
    radioSongMap[radioId] = nil
    radioLyricsMap[radioId] = nil
    
    -- 删除音乐盒
    TriggerServerEvent("radio:removeRadio", radioId)
    
    -- 添加音乐盒物品到物品栏
    TriggerServerEvent("radio:giveRadioItem")
    
    -- 通知玩家
    TriggerEvent("chatMessage", "[系统]", {0, 255, 0}, "您收起了音乐盒并停止了音乐播放")
end

-- 监听删除音乐盒事件
RegisterNetEvent("radio:removeRadio")
AddEventHandler("radio:removeRadio", function(radioId)
    -- 检查音乐盒是否存在
    if radioObjects[radioId] then


        -- 删除音乐盒对象
        DeleteObject(radioObjects[radioId].object)

        -- 清理音乐盒的歌曲和歌词数据
        radioSongMap[radioId] = nil
        radioLyricsMap[radioId] = nil

        -- 从列表中移除
        radioObjects[radioId] = nil
    end
end)

-- 监听临时隐藏音乐盒事件（用于扛音响功能）
RegisterNetEvent("radio:temporaryHideRadio")
AddEventHandler("radio:temporaryHideRadio", function(radioId)
    -- 检查音乐盒是否存在
    if radioObjects[radioId] then
        -- 确保对象存在才删除，避免重复删除
        if radioObjects[radioId].object and DoesEntityExist(radioObjects[radioId].object) then
            DeleteObject(radioObjects[radioId].object)
            -- 等待一帧确保删除完成
            Citizen.Wait(0)
        end

        -- 标记为临时隐藏状态，不删除歌曲和歌词数据
        radioObjects[radioId].isTemporaryHidden = true
        radioObjects[radioId].object = nil

        -- print("^3[音乐盒-扛音响] 临时隐藏音乐盒: " .. radioId)
    end
end)

-- 放置音乐盒
RegisterNetEvent("radio:placeRadio")
AddEventHandler("radio:placeRadio", function()
    -- 检查玩家是否在车内
    local playerPed = PlayerPedId()
    if IsPedInAnyVehicle(playerPed, false) then
        -- 玩家在车内，不允许放置音乐盒
        TriggerEvent("chatMessage", "[系统]", {255, 0, 0}, "您不能在车辆内放置音乐盒")
        -- 返回物品到玩家库存
        TriggerServerEvent("radio:giveRadioItem")
        return
    end
    
    -- 添加调试日志
    -- 禁用音乐盒系统日志
    -- print("^2[音乐盒系统]^7 客户端接收到放置音乐盒事件，开始放置流程")
    TriggerEvent("chatMessage", "[系统]", {255, 165, 0}, "音乐盒放置流程已触发")
    
    -- 获取玩家位置和朝向
    local coords = GetEntityCoords(playerPed)
    local heading = GetEntityHeading(playerPed)
    
    -- 在玩家前方计算放置位置
    local forwardVector = GetEntityForwardVector(playerPed)
    local placementCoords = vector3(
        coords.x + forwardVector.x * 0.5,  -- 减小距离，让音乐盒更靠近玩家
        coords.y + forwardVector.y * 0.5, 
        coords.z - 0.95  -- 放在地面上
    )
    
    -- 播放放置动画
    StartAnimation("anim@heists@money_grab@briefcase", "put_down_case")
    
    -- 等待动画播放
    Wait(1000)
    
    -- 清理动画
    ClearPedTasks(playerPed)
    
    -- 发送到服务器创建音乐盒
    TriggerServerEvent("radio:createRadio", placementCoords, vector3(0.0, 0.0, heading))
    
    -- 通知
    TriggerEvent("chatMessage", "[系统]", {0, 255, 0}, "您放置了一个音乐盒")
end)

-- 注册使用音乐盒物品命令
RegisterCommand("placeradio", function()
    -- 检查玩家是否在车内
    local playerPed = PlayerPedId()
    if IsPedInAnyVehicle(playerPed, false) then
        -- 玩家在车内，不允许放置音乐盒
        TriggerEvent("chatMessage", "[系统]", {255, 0, 0}, "您不能在车辆内放置音乐盒")
        return
    end
    
    -- 触发服务器事件
    TriggerServerEvent("radio:useRadioItem")
end, false)

-- 玩家加载时请求同步音乐盒
AddEventHandler('playerSpawned', function()
    TriggerServerEvent("radio:requestSync")
end)

-- 主循环
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(100)
        
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local closestDistance = radioInteractionDistance + 1.0
        local closestRadio = nil
        
        -- 遍历所有音乐盒，找出最近的
        for radioId, radioData in pairs(radioObjects) do
            -- 跳过临时物体
            if not radioData.isTemporary and DoesEntityExist(radioData.object) then
                local dist = #(playerCoords - radioData.coords)
                if dist < closestDistance then
                    closestDistance = dist
                    closestRadio = radioData
                end
                
                -- 如果距离大于50米，不渲染实体以节省资源
                if dist > 50.0 then
                    SetEntityAlpha(radioData.object, 0, false)
                    SetEntityCollision(radioData.object, false, false)
                else
                    SetEntityAlpha(radioData.object, 255, false)
                    SetEntityCollision(radioData.object, true, true)
                end
            end
        end
        
        -- 更新最近的音乐盒
        nearestRadio = nil
        if closestRadio and closestDistance <= radioInteractionDistance then
            nearestRadio = closestRadio
        end
    end
end)

-- 提示信息线程（当没有ox_target时作为后备方案）
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        -- 如果无法检测到ox_target并且有最近的音乐盒
        if not exports['ox_target'] and nearestRadio then
            local radioCoords = nearestRadio.coords
            -- 显示基础文本提示
            
            -- 显示帮助文本
            DisplayHelpText("按 ~INPUT_CONTEXT~ 使用音乐盒")
        end
    end
end)

-- 打开音乐播放器UI事件（普通模式，不是音乐盒模式）
RegisterNetEvent("music:openFromRadio")
AddEventHandler("music:openFromRadio", function()
    -- 显示UI（不传递isRadio参数，使用普通UI）
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "show"
    })
end)

-- 监听歌曲播放事件，获取歌词
RegisterNetEvent("xsound:songStarted")
AddEventHandler("xsound:songStarted", function(songData, playerId)
    -- 保存歌曲信息
    currentSongInfo = songData

    -- 获取自己的服务器ID
    local myServerId = GetPlayerServerId(PlayerId())

    -- 只有当播放者是自己时，才绑定歌曲到音乐盒
    if playerId and playerId == myServerId then
        -- 查找我创建的最新音乐盒（按时间戳排序）
        local myRadioId = nil
        local latestTimestamp = 0
        for radioId, radioData in pairs(radioObjects) do
            if not radioData.isTemporary and radioData.creatorServerId == myServerId then
                if radioData.timestamp and radioData.timestamp > latestTimestamp then
                    latestTimestamp = radioData.timestamp
                    myRadioId = radioId
                end
            end
        end

        -- 如果找到了我创建的音乐盒，将当前歌曲绑定到这个音乐盒
        if myRadioId then
            radioSongMap[myRadioId] = songData

            -- 重置该音乐盒的歌词状态
            radioLyricsMap[myRadioId] = {
                lyrics = "加载歌词中...",
                lyricsArray = {},
                currentIndex = 1,
                updateTime = GetGameTimer()
            }

            -- 向服务器发送音乐盒歌曲同步信息
            TriggerServerEvent("radio:syncRadioSong", myRadioId, songData)

            -- 请求歌词（只有音乐盒创建者才请求歌词，避免重复请求）
            if songData and songData.id then
                debugPrint("请求歌词，歌曲ID: " .. tostring(songData.id) .. "，音乐盒ID: " .. myRadioId)
                if songData.mid then
                    debugPrint("歌曲MID: " .. tostring(songData.mid))
                end
                debugPrint("歌曲平台: " .. tostring(songData.platform or "未知"))
                TriggerServerEvent("music:requestLyrics", songData, myRadioId)
            else
                debugPrint("无法请求歌词：歌曲ID为空")
                currentLyrics = "无歌词信息"
                radioLyricsMap[myRadioId].lyrics = "无歌词信息"
            end
        end
    end

    -- 重置歌词状态（全局变量，保留向后兼容性）
    currentLyrics = "加载歌词中..."
    currentLyricIndex = 1
    lyricsArray = {}

    -- 调试歌曲信息
    debugPrint("歌曲开始播放: " .. (songData.title or "未知") .. " - " .. (songData.artist or "未知艺术家"))
    debugPrint("歌曲ID: " .. (songData.id or "未知"))
    debugPrint("歌曲平台: " .. (songData.platform or "未知"))
end)

-- 接收音乐盒歌曲同步信息
RegisterNetEvent("radio:syncRadioSong")
AddEventHandler("radio:syncRadioSong", function(radioId, songData, creatorId)
    -- 检查音乐盒是否存在
    if not radioObjects[radioId] then
        return
    end

    -- 绑定歌曲到音乐盒（所有玩家都会收到这个事件）
    radioSongMap[radioId] = songData

    -- 初始化歌词状态
    if not radioLyricsMap[radioId] then
        radioLyricsMap[radioId] = {
            lyrics = "加载歌词中...",
            lyricsArray = {},
            currentIndex = 1,
            updateTime = GetGameTimer()
        }
    end

    debugPrint("收到音乐盒歌曲同步: " .. radioId .. " - " .. (songData.title or "未知"))
end)

-- 接收广播的歌词数据
RegisterNetEvent("music:broadcastLyrics")
AddEventHandler("music:broadcastLyrics", function(songData, lyrics, requesterId, targetRadioId)
    debugPrint("收到歌词广播 - 歌曲ID: " .. (songData.id or "nil") .. ", 目标音乐盒ID: " .. (targetRadioId or "nil") .. ", 请求者ID: " .. (requesterId or "nil"))

    -- 如果没有指定音乐盒ID，直接忽略（不再使用向后兼容逻辑）
    if not targetRadioId then
        debugPrint("歌词广播被忽略：未指定目标音乐盒ID")
        return
    end

    -- 检查指定的音乐盒是否存在
    if not radioObjects[targetRadioId] then
        debugPrint("歌词广播被忽略：音乐盒 " .. targetRadioId .. " 不存在")
        return
    end

    -- 检查音乐盒是否有对应的歌曲
    if not radioSongMap[targetRadioId] or radioSongMap[targetRadioId].id ~= songData.id then
        debugPrint("歌词广播被忽略：音乐盒 " .. targetRadioId .. " 的歌曲不匹配")
        return
    end

    debugPrint("歌词广播验证通过，更新音乐盒 " .. targetRadioId .. " 的歌词")

    if not lyrics or lyrics == "" then
        debugPrint("广播歌词内容为空或无效")
        if radioLyricsMap[targetRadioId] then
            radioLyricsMap[targetRadioId].lyrics = "无歌词"
            radioLyricsMap[targetRadioId].lyricsArray = {}
        end
        return
    end

    -- 处理歌词中的\n字符，将其转换为实际的换行符
    lyrics = lyrics:gsub("\\n", "\n")

    -- 解析歌词数据
    local parsedLyricsArray = {}

    -- 逐行解析歌词
    for line in string.gmatch(lyrics, "[^\r\n]+") do
        line = line:trim() -- 去除首尾空白

        if line ~= "" then
            -- 尝试解析时间标签 [mm:ss.xx] 或 [mm:ss]
            local timeStr, content = string.match(line, "%[([%d:%.]+)%](.*)")

            if timeStr and content then
                content = content:trim()
                if content ~= "" then
                    -- 解析时间
                    local minutes, seconds = string.match(timeStr, "(%d+):([%d%.]+)")
                    if minutes and seconds then
                        local timeMs = (tonumber(minutes) * 60 + tonumber(seconds)) * 1000
                        table.insert(parsedLyricsArray, {
                            time = timeMs,
                            content = content
                        })
                    end
                end
            end
        end
    end

    -- 按时间顺序排序歌词
    table.sort(parsedLyricsArray, function(a, b)
        return a.time < b.time
    end)

    -- 更新音乐盒的歌词
    if #parsedLyricsArray > 0 then
        debugPrint("成功解析广播歌词，共 " .. #parsedLyricsArray .. " 行，音乐盒ID: " .. targetRadioId)
        radioLyricsMap[targetRadioId] = {
            lyrics = parsedLyricsArray[1].content,
            lyricsArray = parsedLyricsArray,
            currentIndex = 1,
            updateTime = GetGameTimer()
        }
    else
        debugPrint("解析广播歌词结果：无可用歌词")
        if radioLyricsMap[targetRadioId] then
            radioLyricsMap[targetRadioId].lyrics = "无可用歌词"
            radioLyricsMap[targetRadioId].lyricsArray = {}
        end
    end
end)

-- 调试同步状态事件
RegisterNetEvent("radio:debugSync")
AddEventHandler("radio:debugSync", function()
    local playerCoords = GetEntityCoords(PlayerPedId())
    local myServerId = GetPlayerServerId(PlayerId())

    print("^3[同步调试]^7 ========== 音乐盒同步状态 ==========")
    print("^3[同步调试]^7 玩家ID: " .. myServerId)
    print("^3[同步调试]^7 玩家坐标: " .. playerCoords.x .. ", " .. playerCoords.y .. ", " .. playerCoords.z)

    local radioCount = 0
    for radioId, radioData in pairs(radioObjects) do
        radioCount = radioCount + 1
        local dist = #(playerCoords - radioData.coords)
        local songInfo = radioSongMap[radioId] and (radioSongMap[radioId].title .. " - " .. radioSongMap[radioId].artist) or "无歌曲"
        local lyricsInfo = radioLyricsMap[radioId] and radioLyricsMap[radioId].lyrics or "无歌词"

        print("^3[同步调试]^7 音乐盒 #" .. radioId .. ":")
        print("^3[同步调试]^7   创建者ID: " .. (radioData.creatorServerId or "未知"))
        print("^3[同步调试]^7   距离: " .. string.format("%.2f", dist) .. "米")
        print("^3[同步调试]^7   歌曲: " .. songInfo)
        print("^3[同步调试]^7   歌词: " .. (string.len(lyricsInfo) > 50 and string.sub(lyricsInfo, 1, 50) .. "..." or lyricsInfo))
    end

    print("^3[同步调试]^7 总计音乐盒数量: " .. radioCount)
    print("^3[同步调试]^7 =====================================")
end)

-- 添加测试命令来验证歌词同步
RegisterCommand("testlyrics", function(source, args)
    local playerCoords = GetEntityCoords(PlayerPedId())
    local myServerId = GetPlayerServerId(PlayerId())

    TriggerEvent("chatMessage", "[歌词测试]", {0, 255, 255}, "检查附近音乐盒的歌词状态...")

    local foundRadios = 0
    for radioId, radioData in pairs(radioObjects) do
        local dist = #(playerCoords - radioData.coords)
        if dist <= 30.0 then
            foundRadios = foundRadios + 1
            local songInfo = radioSongMap[radioId] and (radioSongMap[radioId].title .. " - " .. radioSongMap[radioId].artist) or "无歌曲"
            local lyricsInfo = radioLyricsMap[radioId] and radioLyricsMap[radioId].lyrics or "无歌词"
            local creatorInfo = radioData.creatorServerId or "未知"

            TriggerEvent("chatMessage", "[歌词测试]", {255, 255, 0},
                string.format("音乐盒#%s (创建者:%s, 距离:%.1fm)", radioId, creatorInfo, dist))
            TriggerEvent("chatMessage", "[歌词测试]", {255, 255, 0},
                string.format("  歌曲: %s", songInfo))
            TriggerEvent("chatMessage", "[歌词测试]", {255, 255, 0},
                string.format("  歌词: %s", string.len(lyricsInfo) > 30 and string.sub(lyricsInfo, 1, 30) .. "..." or lyricsInfo))
        end
    end

    if foundRadios == 0 then
        TriggerEvent("chatMessage", "[歌词测试]", {255, 0, 0}, "附近没有找到音乐盒")
    else
        TriggerEvent("chatMessage", "[歌词测试]", {0, 255, 0}, "检查完成，找到 " .. foundRadios .. " 个音乐盒")
    end
end, false)

-- 添加调试开关命令
RegisterCommand("radiodebug", function(source, args)
    DEBUG = not DEBUG
    TriggerEvent("chatMessage", "[调试]", {255, 255, 0}, "音乐盒调试模式: " .. (DEBUG and "开启" or "关闭"))
end, false)

-- 添加音乐盒ID检查命令
RegisterCommand("radioids", function(source, args)
    local myServerId = GetPlayerServerId(PlayerId())
    TriggerEvent("chatMessage", "[音乐盒ID]", {0, 255, 255}, "我的服务器ID: " .. myServerId)

    for radioId, radioData in pairs(radioObjects) do
        local isMyRadio = radioData.creatorServerId == myServerId
        local songInfo = radioSongMap[radioId] and radioSongMap[radioId].title or "无歌曲"
        TriggerEvent("chatMessage", "[音乐盒ID]", {255, 255, 0},
            string.format("音乐盒ID: %s, 创建者: %s%s, 歌曲: %s",
                radioId,
                radioData.creatorServerId or "未知",
                isMyRadio and " (我的)" or "",
                songInfo
            ))
    end
end, false)

-- 旧的接收歌词数据事件已被 music:broadcastLyrics 替代
-- 保留此注释以说明变更

-- 歌词显示线程
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        -- 获取自己的服务器ID
        local myServerId = GetPlayerServerId(PlayerId())

        -- 遍历所有音乐盒
        for radioId, radioData in pairs(radioObjects) do
            -- 检查音乐盒是否存在（正常状态）或者是否被当前玩家扛起
            local shouldShowLyrics = false
            local displayCoords = nil
            local isCarriedByMe = (currentRadioId == radioId and IsInBoomboxAnimation)



            if not radioData.isTemporary and not radioData.isTemporaryHidden and DoesEntityExist(radioData.object) then
                -- 正常状态的音乐盒
                local playerPos = GetEntityCoords(PlayerPedId())
                local radioPos = GetEntityCoords(radioData.object)
                local dist = #(playerPos - radioPos)

                -- 只在一定距离内处理
                if dist <= 15.0 then
                    shouldShowLyrics = true
                    displayCoords = radioPos
                end
            elseif isCarriedByMe then
                -- 被当前玩家扛起的音乐盒，显示在玩家身上
                shouldShowLyrics = true
                local playerPos = GetEntityCoords(PlayerPedId())
                -- 在玩家头顶上方显示歌词，调整为合适的高度
                displayCoords = vector3(playerPos.x, playerPos.y, playerPos.z + 1.2)
            elseif radioData.isTemporaryHidden and radioData.creatorServerId then
                -- 被其他玩家扛起的音乐盒，检查扛起者是否在附近
                local carrierPlayer = GetPlayerFromServerId(radioData.creatorServerId)
                if carrierPlayer ~= -1 then
                    local carrierPed = GetPlayerPed(carrierPlayer)
                    if DoesEntityExist(carrierPed) then
                        local playerPos = GetEntityCoords(PlayerPedId())
                        local carrierPos = GetEntityCoords(carrierPed)
                        local dist = #(playerPos - carrierPos)

                        -- 如果扛起者在15米范围内，显示歌词
                        if dist <= 15.0 then
                            shouldShowLyrics = true
                            -- 在扛起者头顶上方显示歌词
                            displayCoords = vector3(carrierPos.x, carrierPos.y, carrierPos.z + 1.2)
                        end
                    end
                end
            end

            if shouldShowLyrics then
                -- 检查是否有绑定的歌曲
                if radioSongMap[radioId] then
                    -- 确保radioLyricsMap存在
                    if not radioLyricsMap[radioId] then
                        radioLyricsMap[radioId] = {
                            lyrics = "正在播放...",
                            lyricsArray = {},
                            currentIndex = 1,
                            updateTime = GetGameTimer()
                        }
                    end

                    -- 获取对应玩家的音频ID
                    local audioId = "music_" .. radioData.creatorServerId

                    -- 检查对应玩家的音频是否在播放
                    local isPlaying = false
                    local soundExists = false
                    if exports["xsound"] and exports["xsound"]:soundExists(audioId) then
                        isPlaying = exports["xsound"]:isPlaying(audioId)
                        soundExists = true
                    end



                    -- 如果音乐存在（无论是否在播放），都更新歌词
                    if soundExists then
                        local lyricData = radioLyricsMap[radioId]
                        local lyricsArray = lyricData.lyricsArray or {}

                        -- 如果有歌词数据
                        if #lyricsArray > 0 then
                            -- 获取实际音乐播放时间（毫秒）
                            local actualPosition = 0
                            if exports["xsound"] and exports["xsound"]:soundExists(audioId) then
                                -- 获取xsound的实际播放位置（秒）并转换为毫秒
                                actualPosition = (exports["xsound"]:getTimeStamp(audioId) or 0) * 1000
                            else
                                -- 如果无法获取实际位置，回退到使用计时器
                                actualPosition = GetGameTimer() - lyricData.updateTime
                            end

                            -- 使用实际音乐位置
                            local currentTime = actualPosition

                            -- 查找应该显示的歌词
                            for i = 1, #lyricsArray do
                                if i == #lyricsArray or (currentTime >= lyricsArray[i].time and currentTime < lyricsArray[i+1].time) then
                                    if lyricData.currentIndex ~= i then
                                        lyricData.currentIndex = i
                                        lyricData.lyrics = lyricsArray[i].content:gsub("\\n", "\n")

                                        -- 设置下一句歌词
                                        if i < #lyricsArray then
                                            radioSongMap[radioId].nextLyric = lyricsArray[i+1].content:gsub("\\n", "\n")
                                        else
                                            radioSongMap[radioId].nextLyric = nil
                                        end
                                    end
                                    break
                                end
                            end
                        else
                            -- 没有歌词数据，显示默认信息
                            lyricData.lyrics = "正在播放..."
                        end
                    else
                        -- 音频不存在，但仍然显示歌词信息（如果有的话）
                        if isCarriedByMe and radioId == currentRadioId then
                            print("^1[歌词调试] 音频不存在，但仍显示歌词")
                        end
                    end

                    -- 确保有歌词内容
                    local lyricsToShow = radioLyricsMap[radioId].lyrics or "正在播放..."



                    -- 绘制歌词
                    local isCarriedRadio = radioData.isTemporaryHidden -- 判断是否是被扛起的音乐盒

                    if (isCarriedByMe and radioId == currentRadioId) or isCarriedRadio then
                        -- 扛起状态下，显示两行歌词和歌曲信息（自己扛起或其他人扛起）
                        local songInfo = radioSongMap[radioId]

                        -- 绘制歌曲信息（使用彩虹效果，位置最高）
                        if songInfo then
                            local songInfoText = songInfo.title .. " - " .. (songInfo.artist or "未知")
                            Draw3DText(displayCoords.x, displayCoords.y, displayCoords.z + 0.15, songInfoText, "rainbow")
                        end

                        -- 绘制当前歌词（使用霓虹蓝色效果）
                        Draw3DText(displayCoords.x, displayCoords.y, displayCoords.z, lyricsToShow, "neon")

                        -- 绘制下一行歌词（如果有的话，使用较淡的灰色）
                        if songInfo and songInfo.nextLyric and songInfo.nextLyric ~= "" then
                            Draw3DText(displayCoords.x, displayCoords.y, displayCoords.z - 0.1, songInfo.nextLyric, nil, true)
                        end
                    else
                        -- 正常状态下使用原来的绘制函数
                        Draw3DLyrics(
                            displayCoords.x, displayCoords.y, displayCoords.z,
                            lyricsToShow,
                            radioSongMap[radioId] -- 传入歌曲信息
                        )
                    end
                end
            end
        end
    end
end)

-- 歌词调试命令
RegisterCommand("lyricsinfo", function()
    if not currentSongInfo then
        TriggerEvent("chatMessage", "[歌词调试]", {255, 0, 0}, "没有正在播放的歌曲")
        return
    end
    
    -- 显示当前歌曲和歌词信息
    TriggerEvent("chatMessage", "[歌词调试]", {255, 165, 0}, "当前歌曲：" .. 
        (currentSongInfo.title or "未知") .. " - " .. 
        (currentSongInfo.artist or "未知"))
    TriggerEvent("chatMessage", "[歌词调试]", {255, 165, 0}, "歌曲ID：" .. 
        (currentSongInfo.id or "未知"))
    TriggerEvent("chatMessage", "[歌词调试]", {255, 165, 0}, "歌词行数：" .. 
        #lyricsArray)
    TriggerEvent("chatMessage", "[歌词调试]", {255, 165, 0}, "当前歌词：" .. 
        currentLyrics)
        
    -- 重新请求歌词
    if currentSongInfo and currentSongInfo.id then
        TriggerEvent("chatMessage", "[歌词调试]", {255, 165, 0}, "正在重新请求歌词...")
        TriggerServerEvent("music:requestLyrics", currentSongInfo)
    end
end, false)

-- 测试歌词命令
RegisterCommand("testlyrics", function()
    -- 使用多平台的知名歌曲ID进行测试，包含QQ音乐的mid字段
    local testSongs = {
        {id = "338067506", mid = "0012g3Et1iFQCC", title = "夜曲", artist = "周杰伦", platform = "QQ"},
        {id = "001faIUs4M2aVc", title = "稻香", artist = "周杰伦", platform = "QQ"},
        {id = "28754031", title = "海阔天空", artist = "Beyond", platform = "NETEASE"},
        {id = "347230", title = "爱你", artist = "陈芳语", platform = "NETEASE"},
        {id = "XMLY_123456", title = "有声小说测试", artist = "播音员", platform = "XMLY"}
    }
    
    -- 随机选择一首测试歌曲
    local song = testSongs[math.random(1, #testSongs)]
    
    TriggerEvent("chatMessage", "[歌词测试]", {255, 165, 0}, "正在测试歌词功能")
    TriggerEvent("chatMessage", "[歌词测试]", {255, 165, 0}, "歌曲: " .. song.title .. " - " .. song.artist)
    TriggerEvent("chatMessage", "[歌词测试]", {255, 165, 0}, "平台: " .. song.platform .. ", ID: " .. song.id) 
    
    -- 记录歌曲信息到全局变量，以便调试显示
    currentSongInfo = song
    
    -- 请求歌词
    TriggerServerEvent("music:requestLyrics", song)
    
    -- 模拟广播歌曲开始事件
    TriggerEvent("xsound:songStarted", song)
end, false)

-- 歌词特效控制命令
RegisterCommand("lyricseffect", function(source, args)
    if not args[1] then
        TriggerEvent("chatMessage", "[歌词特效]", {0, 255, 255}, "当前特效: " .. 
            (lyricsEffect.enabled and lyricsEffect.type or "关闭"))
        TriggerEvent("chatMessage", "[歌词特效]", {0, 255, 255}, "用法: /lyricseffect [特效类型]")
        TriggerEvent("chatMessage", "[歌词特效]", {0, 255, 255}, "特效类型: rainbow, pulse, neon, off")
        return
    end
    
    local effectType = args[1]
    if effectType == "off" then
        lyricsEffect.enabled = false
        TriggerEvent("chatMessage", "[歌词特效]", {0, 255, 255}, "歌词特效已关闭")
        return
    end
    
    local validEffects = {rainbow = true, pulse = true, neon = true}
    if not validEffects[effectType] then
        TriggerEvent("chatMessage", "[歌词特效]", {255, 0, 0}, "无效的特效类型！可用选项: rainbow, pulse, neon, off")
        return
    end
    
    lyricsEffect.enabled = true
    lyricsEffect.type = effectType
    
    TriggerEvent("chatMessage", "[歌词特效]", {0, 255, 255}, "已设置歌词特效: " .. effectType)
end, false)

-- 帮助命令
RegisterCommand("radiohelp", function()
    TriggerEvent("chatMessage", "[音乐盒帮助]", {255, 255, 0}, "/placeradio - 放置一个音乐盒")
    TriggerEvent("chatMessage", "[音乐盒帮助]", {255, 255, 0}, "/lyricsinfo - 显示歌词调试信息")
    TriggerEvent("chatMessage", "[音乐盒帮助]", {255, 255, 0}, "/testlyrics - 测试歌词功能")
    TriggerEvent("chatMessage", "[音乐盒帮助]", {255, 255, 0}, "/lyricseffect [类型] - 设置歌词特效")
    TriggerEvent("chatMessage", "[音乐盒帮助]", {255, 255, 0}, "/clearradios - 删除所有音乐盒模型")
    TriggerEvent("chatMessage", "[音乐盒帮助]", {255, 255, 0}, "右键点击地上的音乐盒使用ox_target交互菜单")
    TriggerEvent("chatMessage", "[音乐盒帮助]", {255, 255, 0}, "使用'扛起音响'选项可以与附近玩家同步播放音响动作")
end, false)

-- ========================================
-- 扛起音响功能实现
-- ========================================

-- 音响动作相关变量（已移至文件顶部）

-- 音响2动作定义
local Boombox2Emote = {
    dict = "molly@boombox1",
    anim = "boombox1_clip",
    name = "音箱 2",
    AnimationOptions = {
        Prop = "prop_cs_sol_glasses",
        PropBone = 31086,
        PropPlacement = {
            0.0440,
            0.0740,
            0.0000,
            -160.9843,
            -88.7288,
            -0.6197
        },
        SecondProp = 'prop_ghettoblast_02',
        SecondPropBone = 10706,
        SecondPropPlacement = {
            -0.2310,
            -0.0770,
            0.2410,
            -179.7256,
            176.7406,
            23.0190
        },
        EmoteLoop = true,
        EmoteMoving = true,
    }
}

-- 工具函数
function LoadBoomboxAnim(dict)
    if not DoesAnimDictExist(dict) then
        -- print("^1[音乐盒-扛音响] 动画字典不存在: " .. dict)
        return false
    end

    while not HasAnimDictLoaded(dict) do
        RequestAnimDict(dict)
        Wait(10)
    end
    -- print("^2[音乐盒-扛音响] 动画字典加载成功: " .. dict)
    return true
end

function LoadBoomboxPropDict(model)
    -- print("^3[音乐盒-扛音响] 正在加载道具模型: " .. model)
    local modelHash = GetHashKey(model)
    while not HasModelLoaded(modelHash) do
        RequestModel(modelHash)
        Wait(10)
    end
    -- print("^2[音乐盒-扛音响] 道具模型加载成功: " .. model)
end

function DestroyAllBoomboxProps()
    for _, v in pairs(PlayerProps) do
        if DoesEntityExist(v) then
            DeleteEntity(v)
            -- print("^3[音乐盒-扛音响] 删除道具: " .. tostring(v))
        end
    end
    PlayerProps = {}
    PlayerHasProp = false
    -- print("^2[音乐盒-扛音响] 所有道具已清理")
end

function AddBoomboxPropToPlayer(prop1, bone, off1, off2, off3, rot1, rot2, rot3)
    local Player = PlayerPedId()
    local x, y, z = table.unpack(GetEntityCoords(Player))

    local modelHash = GetHashKey(prop1)
    if not HasModelLoaded(modelHash) then
        LoadBoomboxPropDict(prop1)
    end

    local prop = CreateObject(modelHash, x, y, z + 0.2, true, true, true)
    if DoesEntityExist(prop) then
        AttachEntityToEntity(prop, Player, GetPedBoneIndex(Player, bone), off1, off2, off3, rot1, rot2, rot3, true, true,
            false, true, 1, true)
        table.insert(PlayerProps, prop)
        PlayerHasProp = true
        SetModelAsNoLongerNeeded(modelHash)
        -- print("^2[音乐盒-扛音响] 道具创建成功: " .. prop1 .. " (ID: " .. prop .. ")")
    else
        -- print("^1[音乐盒-扛音响] 道具创建失败: " .. prop1)
    end
end

function CancelBoomboxEmote()
    if IsInBoomboxAnimation then
        local ped = PlayerPedId()
        ClearPedTasks(ped)
        DetachEntity(ped, true, false)
        IsInBoomboxAnimation = false
        -- print("^3[音乐盒-扛音响] 动画已取消")
    end

    if PlayerHasProp then
        DestroyAllBoomboxProps()
    end

    -- 通知服务端恢复音乐盒到当前位置
    if currentRadioId then
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local heading = GetEntityHeading(ped)

        -- 在玩家前方计算放置位置
        local forwardVector = GetEntityForwardVector(ped)
        local placementCoords = vector3(
            coords.x + forwardVector.x * 0.5,
            coords.y + forwardVector.y * 0.5,
            coords.z - 0.95  -- 放在地面上
        )

        -- 恢复音频位置到音乐盒位置
        local radioData = radioObjects[currentRadioId]
        if radioData and radioData.creatorServerId then
            local audioId = "music_" .. radioData.creatorServerId
            if exports["xsound"]:soundExists(audioId) then
                -- 将音频位置设置回音乐盒的新位置
                exports["xsound"]:Position(audioId, placementCoords)
                -- print("^2[音乐盒-扛音响] 音频位置已恢复到音乐盒位置")
            end
        end

        -- print("^3[音乐盒-扛音响] 准备恢复音乐盒到当前位置，ID: " .. tostring(currentRadioId))

        -- 等待一小段时间确保所有客户端都处理完隐藏事件
        Citizen.Wait(200)

        TriggerServerEvent("radio:restoreRadio", currentRadioId, placementCoords, vector3(0.0, 0.0, heading))

        -- 延迟清理currentRadioId，确保恢复完成
        Citizen.SetTimeout(500, function()
            currentRadioId = nil
        end)
    end
end

-- 核心播放函数
function PlayBoombox2EmoteFromRadio(radioId)
    local ped = PlayerPedId()

    -- 检查是否在载具中
    if IsPedInAnyVehicle(ped, true) then
        TriggerEvent('chat:addMessage', {
            template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 0, 0, 0.6); border-radius: 3px;"><i class="fas fa-times"></i> {0}: {1}</div>',
            args = {"音乐盒", "无法在载具中使用扛音响动作"}
        })
        return false
    end

    -- 先取消当前动作（但不恢复音乐盒显示）
    if IsInBoomboxAnimation then
        local ped = PlayerPedId()
        ClearPedTasks(ped)
        DetachEntity(ped, true, false)
        IsInBoomboxAnimation = false
        -- print("^3[音乐盒-扛音响] 动画已取消")
    end

    if PlayerHasProp then
        DestroyAllBoomboxProps()
    end

    Wait(300)

    -- 通知服务端临时删除音乐盒
    if radioId then
        -- 先在本地立即隐藏，避免延迟导致的重复显示
        if radioObjects[radioId] and radioObjects[radioId].object and DoesEntityExist(radioObjects[radioId].object) then
            SetEntityAlpha(radioObjects[radioId].object, 0, false)
        end

        -- print("^3[音乐盒-扛音响] 准备删除音乐盒，ID: " .. tostring(radioId))
        TriggerServerEvent("radio:temporaryRemoveRadio", radioId)
        currentRadioId = radioId

        -- 等待一小段时间确保服务器处理完成
        Citizen.Wait(100)
    else
        -- print("^1[音乐盒-扛音响] 警告：radioId为空，无法删除音乐盒")
    end

    -- 加载动画字典
    if not LoadBoomboxAnim(Boombox2Emote.dict) then
        -- print("^1[音乐盒-扛音响] 动画加载失败")
        -- 恢复音乐盒到当前位置
        if radioId then
            local coords = GetEntityCoords(ped)
            local heading = GetEntityHeading(ped)

            -- 在玩家前方计算放置位置
            local forwardVector = GetEntityForwardVector(ped)
            local placementCoords = vector3(
                coords.x + forwardVector.x * 0.5,
                coords.y + forwardVector.y * 0.5,
                coords.z - 0.95  -- 放在地面上
            )

            TriggerServerEvent("radio:restoreRadio", radioId, placementCoords, vector3(0.0, 0.0, heading))
            currentRadioId = nil
        end
        return false
    end

    -- 播放动画
    local MovementType = 51 -- EmoteLoop + EmoteMoving
    TaskPlayAnim(ped, Boombox2Emote.dict, Boombox2Emote.anim, 2.0, 2.0, -1, MovementType, 0, false, false, false)
    RemoveAnimDict(Boombox2Emote.dict)
    IsInBoomboxAnimation = true
    -- print("^2[音乐盒-扛音响] 开始播放动画: " .. Boombox2Emote.name)

    -- 添加道具
    Wait(100) -- 等待动画开始

    -- 第一个道具 (太阳镜)
    local PropName = Boombox2Emote.AnimationOptions.Prop
    local PropBone = Boombox2Emote.AnimationOptions.PropBone
    local PropPl1, PropPl2, PropPl3, PropPl4, PropPl5, PropPl6 = table.unpack(Boombox2Emote.AnimationOptions.PropPlacement)
    AddBoomboxPropToPlayer(PropName, PropBone, PropPl1, PropPl2, PropPl3, PropPl4, PropPl5, PropPl6)

    -- 第二个道具 (音响)
    local SecondPropName = Boombox2Emote.AnimationOptions.SecondProp
    local SecondPropBone = Boombox2Emote.AnimationOptions.SecondPropBone
    local SecondPropPl1, SecondPropPl2, SecondPropPl3, SecondPropPl4, SecondPropPl5, SecondPropPl6 = table.unpack(Boombox2Emote.AnimationOptions.SecondPropPlacement)
    AddBoomboxPropToPlayer(SecondPropName, SecondPropBone, SecondPropPl1, SecondPropPl2, SecondPropPl3, SecondPropPl4, SecondPropPl5, SecondPropPl6)

    TriggerEvent('chat:addMessage', {
        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(0, 255, 0, 0.6); border-radius: 3px;"><i class="fas fa-music"></i> {0}: {1}</div>',
        args = {"音乐盒", "开始扛起音响动作！按 X 取消"}
    })

    -- print("^2[音乐盒-扛音响] 音响2动作播放完成")
    return true
end

function BoomboxSimpleNotify(message)
    TriggerEvent('chat:addMessage', {
        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(0, 150, 255, 0.6); border-radius: 3px;"><i class="fas fa-music"></i> {0}: {1}</div>',
        args = {"扛音响", message}
    })
end

-- 注意：HideRadio 和 ShowRadio 函数已被移除，现在使用服务端处理音乐盒的临时删除和恢复

-- 触发扛起音响动作
function TriggerCarryBoomboxAction(radioId)
    -- 直接播放单人动作，不需要同步
    PlayBoombox2EmoteFromRadio(radioId)
end

-- 移除了所有同步相关的网络事件处理

-- 监听取消动作按键
local lastCancelTime = 0
local cancelKeyPressed = false
Citizen.CreateThread(function()
    while true do
        local sleep = 5 -- 更频繁的检查

        if IsInBoomboxAnimation then
            -- 方法1：检查X键是否被按下（按住检测）
            if IsControlPressed(1, 73) then -- X键按住
                if not cancelKeyPressed then
                    local currentTime = GetGameTimer()
                    if currentTime - lastCancelTime > 200 then -- 减少防抖时间到200ms
                        lastCancelTime = currentTime
                        cancelKeyPressed = true
                        CancelBoomboxEmote()
                        BoomboxSimpleNotify("已取消扛音响动作")
                        -- print("^2[音乐盒-扛音响] X键取消动作触发（按住检测）")
                    end
                end
            -- 方法2：检查X键刚被按下（点击检测）
            elseif IsControlJustPressed(1, 73) then -- X键刚按下
                local currentTime = GetGameTimer()
                if currentTime - lastCancelTime > 200 then
                    lastCancelTime = currentTime
                    CancelBoomboxEmote()
                    BoomboxSimpleNotify("已取消扛音响动作")
                    -- print("^2[音乐盒-扛音响] X键取消动作触发（点击检测）")
                end
                cancelKeyPressed = false
            else
                -- 按键释放时重置状态
                cancelKeyPressed = false
            end

            -- 显示提示文本
            DisplayHelpText("按 ~INPUT_VEH_DUCK~ 取消扛音响动作")
        else
            -- 不在动画中时重置状态
            cancelKeyPressed = false
            sleep = 100 -- 不在动画中时可以休眠更久
        end

        Citizen.Wait(sleep)
    end
end)

-- 额外的按键监听 - 使用原生键盘检测作为备用
Citizen.CreateThread(function()
    while true do
        if IsInBoomboxAnimation then
            -- 检测键盘X键 (键码88)
            if IsDisabledControlJustPressed(0, 73) or IsDisabledControlPressed(0, 73) then
                local currentTime = GetGameTimer()
                if currentTime - lastCancelTime > 300 then
                    lastCancelTime = currentTime
                    CancelBoomboxEmote()
                    BoomboxSimpleNotify("已取消扛音响动作")
                    -- print("^2[音乐盒-扛音响] X键取消动作触发（备用检测）")
                end
            end
            Citizen.Wait(0)
        else
            Citizen.Wait(500)
        end
    end
end)

-- 扛音箱时音频位置更新线程
Citizen.CreateThread(function()
    while true do
        if IsInBoomboxAnimation and currentRadioId then
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)

            -- 获取当前扛着的音乐盒的创建者ID
            local radioData = radioObjects[currentRadioId]
            if radioData and radioData.creatorServerId then
                local audioId = "music_" .. radioData.creatorServerId

                -- 如果音频存在，更新其位置为玩家当前位置
                if exports["xsound"]:soundExists(audioId) then
                    exports["xsound"]:Position(audioId, playerCoords)
                    -- 确保音频距离设置合适
                    exports["xsound"]:Distance(audioId, 50.0)
                end
            end

            Citizen.Wait(100) -- 每100ms更新一次位置
        else
            Citizen.Wait(1000) -- 不在扛音箱状态时休眠更久
        end
    end
end)

-- 测试命令 (可选，用于调试)
RegisterCommand('testcarryboombox', function(source, args, raw)
    if args[1] then
        local radioId = tonumber(args[1])
        if radioId and radioObjects[radioId] then
            TriggerCarryBoomboxAction(radioId)
        else
            TriggerEvent("chatMessage", "[测试]", {255, 0, 0}, "音乐盒ID无效或不存在")
        end
    else
        TriggerEvent("chatMessage", "[测试]", {255, 255, 0}, "用法: /testcarryboombox [音乐盒ID]")
    end
end, false)

RegisterCommand('cancelboombox', function(source, args, raw)
    if IsInBoomboxAnimation then
        CancelBoomboxEmote()
        TriggerEvent("chatMessage", "[测试]", {0, 255, 0}, "已取消扛音响动作")
    else
        TriggerEvent("chatMessage", "[测试]", {255, 0, 0}, "当前没有在播放扛音响动作")
    end
end, false)

-- 调试歌词显示状态的命令
RegisterCommand('debuglyrics', function(source, args, raw)
    TriggerEvent("chatMessage", "[歌词调试]", {255, 165, 0}, "=== 歌词显示状态调试 ===")
    TriggerEvent("chatMessage", "[歌词调试]", {255, 255, 255}, "扛起状态: " .. (IsInBoomboxAnimation and "是" or "否"))
    TriggerEvent("chatMessage", "[歌词调试]", {255, 255, 255}, "当前音乐盒ID: " .. (currentRadioId or "无"))

    -- 显示所有音乐盒的状态
    for radioId, radioData in pairs(radioObjects) do
        local isCarriedByMe = (currentRadioId == radioId and IsInBoomboxAnimation)
        local hasLyrics = radioLyricsMap[radioId] and radioLyricsMap[radioId].lyrics
        local hasSong = radioSongMap[radioId] ~= nil

        TriggerEvent("chatMessage", "[歌词调试]", {255, 255, 255},
            "音乐盒 " .. radioId .. ": 扛起=" .. (isCarriedByMe and "是" or "否") ..
            ", 有歌词=" .. (hasLyrics and "是" or "否") ..
            ", 有歌曲=" .. (hasSong and "是" or "否"))

        if hasLyrics then
            TriggerEvent("chatMessage", "[歌词调试]", {255, 255, 255},
                "  当前歌词: " .. tostring(radioLyricsMap[radioId].lyrics))
        end
    end

    local count = 0
    for radioId, radioData in pairs(radioObjects) do
        count = count + 1
        local hasLyrics = radioLyricsMap[radioId] and radioLyricsMap[radioId].lyrics or "无"
        local hasSong = radioSongMap[radioId] and "有" or "无"
        local isHidden = radioData.isTemporaryHidden and "是" or "否"
        local hasObject = radioData.object and "是" or "否"

        TriggerEvent("chatMessage", "[歌词调试]", {255, 255, 255},
            string.format("音乐盒%s: 歌曲=%s, 歌词=%s", radioId, hasSong, hasLyrics))
        TriggerEvent("chatMessage", "[歌词调试]", {255, 255, 255},
            string.format("  临时隐藏=%s, 有对象=%s", isHidden, hasObject))
    end

    if count == 0 then
        TriggerEvent("chatMessage", "[歌词调试]", {255, 0, 0}, "没有找到任何音乐盒")
    end
end, false)

-- 测试歌词显示的命令
RegisterCommand('testlyrics', function(source, args, raw)
    if IsInBoomboxAnimation and currentRadioId then
        TriggerEvent("chatMessage", "[测试]", {0, 255, 0}, "扛起状态下歌词显示测试 - 检查控制台输出")
        print("^2[歌词测试] 当前扛起音乐盒ID: " .. tostring(currentRadioId))
        print("^2[歌词测试] 扛起动画状态: " .. tostring(IsInBoomboxAnimation))

        -- 检查歌词数据
        if radioLyricsMap[currentRadioId] then
            print("^2[歌词测试] 歌词数据存在: " .. tostring(radioLyricsMap[currentRadioId].lyrics))
        else
            print("^1[歌词测试] 歌词数据不存在")
        end

        -- 检查歌曲数据
        if radioSongMap[currentRadioId] then
            print("^2[歌词测试] 歌曲数据存在: " .. tostring(radioSongMap[currentRadioId].title))
        else
            print("^1[歌词测试] 歌曲数据不存在")
        end
    else
        TriggerEvent("chatMessage", "[测试]", {255, 0, 0}, "请先扛起音乐盒再使用此命令")
    end
end, false)

-- 删除所有音乐盒模型的命令
RegisterCommand("clearradios", function()
    -- 确认对话框
    TriggerEvent("chatMessage", "[系统]", {255, 165, 0}, "正在删除所有音乐盒模型...")

    -- 停止所有音乐播放
    for radioId, radioData in pairs(radioObjects) do
        local audioId = "music_" .. radioData.creatorServerId
        if exports["xsound"]:soundExists(audioId) then
            exports["xsound"]:Destroy(audioId)
        end

        -- 删除测试音乐盒的音频
        local testAudioId = "test_radio_" .. radioId
        if exports["xsound"]:soundExists(testAudioId) then
            exports["xsound"]:Destroy(testAudioId)
        end
    end

    -- 删除所有本地音乐盒对象
    for radioId, radioData in pairs(radioObjects) do
        if DoesEntityExist(radioData.object) then
            DeleteEntity(radioData.object)
        end
    end

    -- 清空本地数据
    radioObjects = {}
    radioSongMap = {}
    currentSongInfo = nil



    -- 通知服务器清理数据
    TriggerServerEvent("radio:clearAllRadios")

    TriggerEvent("chatMessage", "[系统]", {0, 255, 0}, "已删除所有音乐盒模型")
end, false)

-- 添加测试命令，用于放置测试音乐盒
RegisterCommand("testradio", function(source, args)
    -- 获取玩家位置和朝向
    local playerPed = PlayerPedId()
    
    -- 检查玩家是否在车内
    if IsPedInAnyVehicle(playerPed, false) then
        TriggerEvent("chatMessage", "[测试系统]", {255, 0, 0}, "您不能在车辆内放置测试音乐盒")
        return
    end
    
    local coords = GetEntityCoords(playerPed)
    local heading = GetEntityHeading(playerPed)
    
    -- 计算放置位置 - 在玩家前方
    local forwardVector = GetEntityForwardVector(playerPed)
    local placementCoords = vector3(
        coords.x + forwardVector.x * 0.5,
        coords.y + forwardVector.y * 0.5,
        coords.z - 0.95
    )
    
    -- 播放放置动画
    StartAnimation("anim@heists@money_grab@briefcase", "put_down_case")
    
    -- 等待动画播放
    Wait(1000)
    
    -- 清理动画
    ClearPedTasks(playerPed)
    
    -- 发送到服务器创建音乐盒
    TriggerServerEvent("radio:createTestRadio", placementCoords, vector3(0.0, 0.0, heading), tonumber(args[1] or 1))
    
    -- 通知
    TriggerEvent("chatMessage", "[测试系统]", {0, 255, 0}, "您放置了一个测试音乐盒 #" .. (args[1] or 1))
end, false)

-- 测试音乐盒事件处理
RegisterNetEvent("radio:setupTestRadio")
AddEventHandler("radio:setupTestRadio", function(radioId, testSongIndex)
    -- 测试歌曲数据
    local testSongs = {
        {
            id = "338067506",
            mid = "0012g3Et1iFQCC",
            title = "夜曲",
            artist = "周杰伦",
            platform = "QQ",
            duration = 240
        },
        {
            id = "28754031",
            title = "海阔天空",
            artist = "Beyond",
            platform = "NETEASE",
            duration = 320
        },
        {
            id = "347230",
            title = "爱你",
            artist = "陈芳语",
            platform = "NETEASE", 
            duration = 200
        }
    }
    
    -- 测试歌词数据
    local testLyrics = {
        -- 夜曲歌词
        [1] = "一杯敬明天 一杯敬过往\n一杯敬自由 一杯敬死亡\n一杯敬你 一杯敬我\n干了这杯 干了以后",
        -- 海阔天空歌词
        [2] = "今天我 寒夜里看雪飘过\n怀着冷却了的心窝飘远方\n风雨里追赶 雾里分不清影踪\n天空海阔你与我",
        -- 爱你歌词
        [3] = "我可以假装 看不见你的泪光\n我可以假装 没听见你的谎\n我可以假装 你没有说过爱我\n反正我也不是真的那么爱你"
    }
    
    -- 索引超出范围则使用第一首歌
    if testSongIndex < 1 or testSongIndex > #testSongs then
        testSongIndex = 1
    end
    
    -- 选择歌曲和歌词
    local songData = testSongs[testSongIndex]
    local lyrics = testLyrics[testSongIndex]
    
    -- 绑定歌曲到音乐盒
    if radioId and radioObjects[radioId] then
        -- 设置歌曲数据
        radioSongMap[radioId] = songData
        
        -- 设置歌词数据
        radioLyricsMap[radioId] = {
            lyrics = lyrics,
            lyricsArray = {{time = 0, content = lyrics}},  -- 简化的歌词数组
            currentIndex = 1,
            updateTime = GetGameTimer()
        }
        
        TriggerEvent("chatMessage", "[测试系统]", {0, 255, 0}, "已为音乐盒 #" .. radioId .. " 设置歌曲: " .. songData.title .. " - " .. songData.artist)
    else
        TriggerEvent("chatMessage", "[测试系统]", {255, 0, 0}, "无法找到音乐盒 #" .. radioId)
    end
end)

-- 添加一个播放测试音乐盒歌曲的命令
RegisterCommand("playradio", function(source, args)
    -- 获取音乐盒ID和音量
    local radioId = tonumber(args[1])
    local volume = tonumber(args[2] or 1.0)
    
    -- 检查ID是否有效
    if not radioId or not radioObjects[radioId] then
        TriggerEvent("chatMessage", "[测试系统]", {255, 0, 0}, "无效的音乐盒ID: " .. tostring(radioId))
        return
    end
    
    -- 获取歌曲数据
    local songData = radioSongMap[radioId]
    if not songData then
        TriggerEvent("chatMessage", "[测试系统]", {255, 0, 0}, "音乐盒 #" .. radioId .. " 没有绑定歌曲")
        return
    end
    
    -- 播放测试歌曲
    local testUrls = {
        -- 夜曲 (周杰伦) 试听链接
        [1] = "https://isure.stream.qqmusic.qq.com/M500004RO4xB3UTvHN.mp3?guid=3946173165&vkey=DF5B0968F4F3D2E96FA11C671E8D0300BE5C83D0C1E905B67E5C0339959C36FF181F5ADB6BACE6A66B3B976733708B72DF012CA2E9F696C7__v21ebdd5a0&uin=3677119696&fromtag=120042",
        -- 海阔天空 (Beyond) 试听链接
        [2] = "https://aod.cos.tx.xmcdn.com/storages/8ac1-audiofreehighqps/20/D3/CMCoOSADmPPsAAC-0QBjlYAn.m4a",
        -- 爱你 (陈芳语) 试听链接
        [3] = "https://aod.cos.tx.xmcdn.com/storages/f673-audiofreehighqps/3B/65/CMCoOSQDlvrIAAEFwwBjlXMQ.m4a"
    }
    
    local testSongIndex = radioObjects[radioId].testSongIndex or 1
    local url = testUrls[testSongIndex]
    
    if not url then
        TriggerEvent("chatMessage", "[测试系统]", {255, 0, 0}, "没有找到音乐盒 #" .. radioId .. " 对应的测试歌曲URL")
        return
    end
    
    -- 获取音乐盒坐标
    local coords = radioObjects[radioId].coords
    
    -- 创建音频ID
    local audioId = "test_radio_" .. radioId
    
    -- 停止之前可能在播放的音乐
    if exports["xsound"]:soundExists(audioId) then
        exports["xsound"]:Destroy(audioId)
    end
    
    -- 播放音乐
    exports["xsound"]:PlayUrlPos(audioId, url, volume, coords, false)
    
    -- 设置声音距离
    exports["xsound"]:Distance(audioId, 15.0)
    
    TriggerEvent("chatMessage", "[测试系统]", {0, 255, 0}, "音乐盒 #" .. radioId .. " 开始播放: " .. songData.title .. " - " .. songData.artist)
end, false)

-- 停止测试音乐盒播放的命令
RegisterCommand("stopradio", function(source, args)
    local radioId = tonumber(args[1])
    
    if not radioId then
        TriggerEvent("chatMessage", "[测试系统]", {255, 0, 0}, "请指定要停止的音乐盒ID")
        return
    end
    
    local audioId = "test_radio_" .. radioId
    
    if exports["xsound"]:soundExists(audioId) then
        exports["xsound"]:Destroy(audioId)
        TriggerEvent("chatMessage", "[测试系统]", {0, 255, 0}, "已停止音乐盒 #" .. radioId .. " 的播放")
    else
        TriggerEvent("chatMessage", "[测试系统]", {255, 0, 0}, "音乐盒 #" .. radioId .. " 没有正在播放的音乐")
    end
end, false)

-- 显示附近音乐盒ID的命令
RegisterCommand("radioinfo", function()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local foundAny = false
    
    TriggerEvent("chatMessage", "[测试系统]", {0, 255, 0}, "--- 附近音乐盒信息 ---")
    
    for radioId, radioData in pairs(radioObjects) do
        if DoesEntityExist(radioData.object) then
            local dist = #(playerCoords - radioData.coords)
            if dist <= 15.0 then
                local songInfo = "无歌曲"
                if radioSongMap[radioId] then
                    songInfo = radioSongMap[radioId].title .. " - " .. radioSongMap[radioId].artist
                end
                
                local testType = ""
                if radioData.isTest then
                    testType = "[测试] "
                end
                
                TriggerEvent("chatMessage", "", {255, 255, 0}, "ID: " .. radioId .. " " .. testType ..
                    "距离: " .. string.format("%.1f", dist) .. "米, " .. 
                    "歌曲: " .. songInfo)
                foundAny = true
            end
        end
    end
    
    if not foundAny then
        TriggerEvent("chatMessage", "", {255, 0, 0}, "附近没有音乐盒")
    end
    
    TriggerEvent("chatMessage", "[测试系统]", {0, 255, 0}, "--- 使用方法 ---")
    TriggerEvent("chatMessage", "", {255, 255, 0}, "/testradio [1-3] - 放置测试音乐盒")
    TriggerEvent("chatMessage", "", {255, 255, 0}, "/playradio [ID] [音量] - 播放音乐盒的歌曲")
    TriggerEvent("chatMessage", "", {255, 255, 0}, "/stopradio [ID] - 停止播放")
end, false)

-- 添加处理暂停音乐的事件
RegisterNetEvent("radio:pauseMusicAtClient")
AddEventHandler("radio:pauseMusicAtClient", function(creatorId, radioId, pauser)
    -- 获取音频ID
    local audioId = "music_" .. creatorId

    -- 获取自己的服务器ID
    local myServerId = GetPlayerServerId(PlayerId())

    -- 检查是否有音频在播放
    if exports["xsound"] and exports["xsound"]:soundExists(audioId) and exports["xsound"]:isPlaying(audioId) then
        -- 获取当前播放位置
        local currentTime = exports["xsound"]:getTimeStamp(audioId) or 0

        -- 暂停音频
        exports["xsound"]:Pause(audioId)

        -- 如果是音乐创建者，同时更新UI界面的暂停状态
        if creatorId == myServerId then
            -- 通知UI界面暂停（无论UI是否打开都发送，因为UI可能在后台运行）
            SendNUIMessage({
                action = "pausePlayback",
                currentTime = currentTime,
                isPlaying = false,
                source = "radio"
            })

            -- 同时更新client.lua中的播放状态
            TriggerEvent("music:updatePlaybackState", false, currentTime)
        end

        -- 如果是自己操作自己的音乐盒，不显示通知（因为已经在操作函数里显示了）
        if pauser == myServerId and creatorId == myServerId then
            return
        end

        -- 显示提示（包括谁暂停了音乐）
        local pauserName = GetPlayerName(pauser) or "未知玩家"
        if creatorId == myServerId then
            -- 如果你是音乐盒所有者
            TriggerEvent('chat:addMessage', {
                template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 165, 0, 0.6); border-radius: 3px;"><i class="fas fa-pause"></i> {0}: {1}</div>',
                args = {"音乐盒", pauserName .. " 暂停了您的音乐盒 #" .. radioId .. " 的音乐"}
            })
        else
            -- 如果你是其他玩家
            TriggerEvent('chat:addMessage', {
                template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 165, 0, 0.6); border-radius: 3px;"><i class="fas fa-pause"></i> {0}: {1}</div>',
                args = {"音乐盒", pauserName .. " 暂停了音乐盒 #" .. radioId .. " 的音乐"}
            })
        end
    end
end)

-- 添加处理恢复音乐的事件
RegisterNetEvent("radio:resumeMusicAtClient")
AddEventHandler("radio:resumeMusicAtClient", function(creatorId, radioId, resumer)
    -- 获取音频ID
    local audioId = "music_" .. creatorId

    -- 获取自己的服务器ID
    local myServerId = GetPlayerServerId(PlayerId())

    -- 检查是否有音频被暂停
    if exports["xsound"] and exports["xsound"]:soundExists(audioId) and not exports["xsound"]:isPlaying(audioId) then
        -- 恢复音频
        exports["xsound"]:Resume(audioId)

        -- 如果是音乐创建者，同时更新UI界面的播放状态
        if creatorId == myServerId then
            -- 获取当前播放位置
            local currentTime = exports["xsound"]:getTimeStamp(audioId) or 0

            -- 通知UI界面恢复播放（无论UI是否打开都发送，因为UI可能在后台运行）
            SendNUIMessage({
                action = "resumePlayback",
                currentTime = currentTime,
                isPlaying = true,
                source = "radio"
            })

            -- 同时更新client.lua中的播放状态
            TriggerEvent("music:updatePlaybackState", true, currentTime)
        end

        -- 如果是自己操作自己的音乐盒，不显示通知（因为已经在操作函数里显示了）
        if resumer == myServerId and creatorId == myServerId then
            return
        end
        
        -- 显示提示（包括谁恢复了音乐）
        local resumerName = GetPlayerName(resumer) or "其他玩家"
        if creatorId == myServerId then
            -- 如果你是音乐盒所有者
            TriggerEvent('chat:addMessage', {
                template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(0, 255, 0, 0.6); border-radius: 3px;"><i class="fas fa-play"></i> {0}: {1}</div>',
                args = {"音乐盒", resumerName .. " 恢复了您的音乐盒 #" .. radioId .. " 的音乐"}
            })
        else
            -- 如果你是其他玩家
            TriggerEvent('chat:addMessage', {
                template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(0, 255, 0, 0.6); border-radius: 3px;"><i class="fas fa-play"></i> {0}: {1}</div>',
                args = {"音乐盒", resumerName .. " 恢复了音乐盒 #" .. radioId .. " 的音乐"}
            })
        end
    end
end)

-- 添加控制是否显示下一句歌词的命令
RegisterCommand("nextlyric", function(source, args)
    if args[1] == "on" or args[1] == "true" then
        showNextLyric = true
        TriggerEvent("chatMessage", "[歌词设置]", {0, 255, 255}, "已开启显示下一句歌词")
    elseif args[1] == "off" or args[1] == "false" then
        showNextLyric = false
        TriggerEvent("chatMessage", "[歌词设置]", {0, 255, 255}, "已关闭显示下一句歌词")
    else
        TriggerEvent("chatMessage", "[歌词设置]", {0, 255, 255}, "当前设置: " .. (showNextLyric and "显示下一句歌词" or "不显示下一句歌词"))
        TriggerEvent("chatMessage", "[歌词设置]", {0, 255, 255}, "用法: /nextlyric [on/off]")
    end
end, false)

-- 监听音乐播放位置改变事件（当用户拖动进度条时触发）
RegisterNetEvent("xsound:setTimeStampAtClient")
AddEventHandler("xsound:setTimeStampAtClient", function(sourcePlayerId, time)
    -- 检查是否有关联的音乐盒在播放
    local audioId = "music_" .. sourcePlayerId
    
    -- 更新与此音乐盒关联的歌词时间点
    for radioId, radioData in pairs(radioObjects) do
        if radioData.creatorServerId == sourcePlayerId and radioLyricsMap[radioId] then
            -- 计算新的更新时间点（用当前游戏时间减去歌曲已播放的时间）
            local newUpdateTime = GetGameTimer() - (time * 1000)
            radioLyricsMap[radioId].updateTime = newUpdateTime
            
            -- 输出调试信息
            print("^3[调试信息]^7 歌曲进度已调整: " .. time .. "秒，更新歌词时间点")
            
            -- 立即更新显示的歌词
            local lyricsArray = radioLyricsMap[radioId].lyricsArray
            if lyricsArray and #lyricsArray > 0 then
                local timeMs = time * 1000  -- 转换为毫秒
                
                -- 查找对应时间点的歌词
                for i = 1, #lyricsArray do
                    if i == #lyricsArray or (timeMs >= lyricsArray[i].time and timeMs < lyricsArray[i+1].time) then
                        radioLyricsMap[radioId].currentIndex = i
                        radioLyricsMap[radioId].lyrics = lyricsArray[i].content:gsub("\\n", "\n")
                        
                        -- 设置下一句歌词
                        if i < #lyricsArray and radioSongMap[radioId] then
                            radioSongMap[radioId].nextLyric = lyricsArray[i+1].content:gsub("\\n", "\n")
                        elseif radioSongMap[radioId] then
                            radioSongMap[radioId].nextLyric = nil
                        end
                        
                        break
                    end
                end
            end
            
            break  -- 找到匹配的音乐盒后退出循环
        end
    end
end)



-- 监听音乐播放位置改变事件（当用户拖动进度条时触发）
RegisterNetEvent("xsound:setTimeStampAtClient")
AddEventHandler("xsound:setTimeStampAtClient", function(sourcePlayerId, time)
    -- 检查是否有关联的音乐盒在播放
    local audioId = "music_" .. sourcePlayerId
    
    -- 更新与此音乐盒关联的歌词时间点
    for radioId, radioData in pairs(radioObjects) do
        if radioData.creatorServerId == sourcePlayerId and radioLyricsMap[radioId] then
            -- 计算新的更新时间点（用当前游戏时间减去歌曲已播放的时间）
            local newUpdateTime = GetGameTimer() - (time * 1000)
            radioLyricsMap[radioId].updateTime = newUpdateTime
            
            -- 输出调试信息
            print("^3[调试信息]^7 歌曲进度已调整: " .. time .. "秒，更新歌词时间点")
            
            -- 立即更新显示的歌词
            local lyricsArray = radioLyricsMap[radioId].lyricsArray
            if lyricsArray and #lyricsArray > 0 then
                local timeMs = time * 1000  -- 转换为毫秒
                
                -- 查找对应时间点的歌词
                for i = 1, #lyricsArray do
                    if i == #lyricsArray or (timeMs >= lyricsArray[i].time and timeMs < lyricsArray[i+1].time) then
                        radioLyricsMap[radioId].currentIndex = i
                        radioLyricsMap[radioId].lyrics = lyricsArray[i].content:gsub("\\n", "\n")
                        
                        -- 设置下一句歌词
                        if i < #lyricsArray and radioSongMap[radioId] then
                            radioSongMap[radioId].nextLyric = lyricsArray[i+1].content:gsub("\\n", "\n")
                        elseif radioSongMap[radioId] then
                            radioSongMap[radioId].nextLyric = nil
                        end
                        
                        break
                    end
                end
            end
            
            break  -- 找到匹配的音乐盒后退出循环
        end
    end
end)

-- 音乐盒同步清理线程 - 处理偶发的同步问题
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5000) -- 每5秒检查一次

        -- 清理可能存在的重复音乐盒对象
        for radioId, radioData in pairs(radioObjects) do
            if radioData and not radioData.isTemporary then
                -- 检查是否有重复的对象
                if radioData.isTemporaryHidden and radioData.object and DoesEntityExist(radioData.object) then
                    -- 如果标记为隐藏但对象仍然存在，删除它
                    DeleteObject(radioData.object)
                    radioData.object = nil
                elseif not radioData.isTemporaryHidden and radioData.object and not DoesEntityExist(radioData.object) then
                    -- 如果没有标记为隐藏但对象不存在，清理引用
                    radioData.object = nil
                end

                -- 检查透明度异常的对象
                if radioData.object and DoesEntityExist(radioData.object) then
                    local alpha = GetEntityAlpha(radioData.object)
                    if alpha == 0 and not radioData.isTemporaryHidden then
                        -- 如果对象透明但没有标记为隐藏，恢复透明度
                        SetEntityAlpha(radioData.object, 255, false)
                    end
                end
            end
        end
    end
end)
