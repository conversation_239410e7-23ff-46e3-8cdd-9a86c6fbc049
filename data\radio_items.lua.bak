-- 音乐盒物品定义

-- 检测服务器框架
local ESX = nil
local QBCore = nil
local frameworkInitialized = false

-- 初始化ESX
local function InitESX()
    ESX = exports["es_extended"]:getSharedObject()
    if ESX then
        -- 注册物品
        ESX.RegisterUsableItem('radio_player', function(source)
            -- 显示通知
            TriggerClientEvent('esx:showNotification', source, '您放置了一个音乐盒')
            -- 修复：删除TriggerEvent调用，直接触发客户端放置事件
            TriggerClientEvent('radio:placeRadio', source)
            -- 从背包中移除物品
            local xPlayer = ESX.GetPlayerFromId(source)
            if xPlayer then
                xPlayer.removeInventoryItem('radio_player', 1)
            end
        end)
        
        print("^2[音乐盒系统]^7 ESX框架检测成功")
        frameworkInitialized = true
        return true
    end
    return false
end

-- 初始化QBCore
local function InitQBCore()
    QBCore = exports['qb-core']:GetCoreObject()
    if QBCore then
        -- 注册物品
        QBCore.Functions.CreateUseableItem("radio_player", function(source, item)
            -- 显示通知
            TriggerClientEvent('QBCore:Notify', source, '您放置了一个音乐盒', 'success')
            -- 修复：删除TriggerEvent调用，直接触发客户端放置事件
            TriggerClientEvent('radio:placeRadio', source)
            -- 从背包中移除物品
            local Player = QBCore.Functions.GetPlayer(source)
            if Player then
                Player.Functions.RemoveItem('radio_player', 1)
                TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items["radio_player"], "remove")
            end
        end)
        
        print("^2[音乐盒系统]^7 QBCore框架检测成功")
        frameworkInitialized = true
        return true
    end
    return false
end

-- 事件监听器，用于初始化物品
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- 尝试初始化ESX
        if not InitESX() then
            -- 如果ESX初始化失败，尝试QBCore
            if not InitQBCore() then
                -- 如果两个框架都没有检测到，提供独立命令
                print("^3[音乐盒系统]^7 未检测到支持的框架，使用独立命令 /placeradio")
                
                -- 注册指令处理程序（作为后备方案）
                RegisterCommand("giveradio", function(source, args, rawCommand)
                    local playerId = tonumber(args[1]) or source
                    TriggerClientEvent('chatMessage', playerId, '[系统]', {255, 0, 0}, '您收到了一个音乐盒，请使用 /placeradio 放置它。')
                    -- 这里您可以添加物品到玩家背包的逻辑，如果有自定义背包系统
                end, true) -- 设置为true表示仅限管理员使用
            end
        end
    end
end)

-- 添加QB-Core物品定义（如果使用QB-Core）
-- 将以下内容添加到qb-core/shared/items.lua:
--[[
["radio_player"] = {
    ["name"] = "radio_player",
    ["label"] = "音乐盒",
    ["weight"] = 2000,
    ["type"] = "item",
    ["image"] = "radio_player.png",
    ["unique"] = false,
    ["useable"] = true,
    ["shouldClose"] = true,
    ["combinable"] = nil,
    ["description"] = "一个便携式音乐盒，可以播放音乐"
},
]]

-- 添加ESX物品定义（如果使用ESX）
-- 使用以下SQL运行:
--[[
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('radio_player', '音乐盒', 2, 0, 1);
]] 