-- 每日推荐歌曲服务端实现
local dailyRecommendSongs = {}
local lastUpdateDate = ""

-- 不需要导入MusicService模块，它已经是全局变量了
-- local MusicService = exports["music"].music_service

-- FiveM已经内置了json库，不需要额外引入

-- 前置声明函数，解决相互调用问题
local InitDatabase
local UpdateDailyRecommend
local SearchDailyRecommend
local ClearDailyRecommendTable
local AddSongsToRecommend

-- 搜索每日推荐歌曲
SearchDailyRecommend = function()
    -- 直接使用API模块搜索
    if API and API.searchMusic then
        -- 使用API直接搜索
        API.searchMusic("NETEASE", "每日推荐", function(result)
            HandleSearchResult(result)
        end, 20)
    else
        -- 无法搜索，使用模拟数据
        -- 禁用日志输出
        -- print("无法使用API搜索，使用模拟数据")
        local mockSongs = GenerateMockRecommendSongs()
        HandleSearchResult({
            status = "success",
            songs = mockSongs
        })
    end
end

-- 处理搜索结果
function HandleSearchResult(result)
    -- 检查结果是否为有效的歌曲数组
    if result and type(result) == "table" and #result > 0 then
        -- 搜索成功，保存到数据库
        dailyRecommendSongs = result
        lastUpdateDate = os.date("%Y-%m-%d")
        
        -- 保存到数据库
        AddSongsToRecommend(result)
    else
        -- 禁用日志输出
        -- print("搜索每日推荐失败或结果为空")
        
        -- 生成一些模拟数据作为备选
        local mockSongs = GenerateMockRecommendSongs()
        dailyRecommendSongs = mockSongs
        lastUpdateDate = os.date("%Y-%m-%d")
        
        -- 保存到数据库
        AddSongsToRecommend(mockSongs)
    end
end

-- 将推荐歌曲添加到数据库
AddSongsToRecommend = function(songs)
    -- 添加每首歌曲
    for i, song in ipairs(songs) do
        -- 确保歌曲有id
        if not song.id or song.id == "" then
            song.id = song.platform .. "_" .. (song.mid or "") .. "_" .. song.name .. "_" .. song.singer
        end
        
        -- 处理duration字段，确保是数字
        local duration = 0
        if song.duration then
            if type(song.duration) == "number" then
                duration = song.duration
            elseif type(song.duration) == "string" then
                duration = tonumber(song.duration) or 0
            elseif type(song.duration) == "table" then
                -- 如果是表，尝试转换为字符串后再转为数字
                duration = tonumber(tostring(song.duration)) or 0
            end
        end
        
        -- 添加到每日推荐歌曲表
        exports.oxmysql:execute(
            "INSERT INTO `daily_recommend_songs` (`id`, `name`, `singer`, `album`, `duration`, `cover_url`, `url`, `platform`, `order_index`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            {
                song.id,
                song.name or "未知歌曲",
                song.singer or "未知歌手",
                song.album or "未知专辑",
                duration, -- 使用处理后的duration值
                song.pic or song.picUrl or "",
                song.url or "",
                song.platform or "NETEASE",
                i
            }, function(songResult)
                -- 禁用日志输出
                -- if songResult then
                --     print("每日推荐歌曲保存成功: " .. song.name)
                -- else
                --     print("每日推荐歌曲保存失败: " .. song.name)
                -- end
            end
        )
    end
    
    -- 禁用日志输出
    -- print("共添加 " .. #songs .. " 首每日推荐歌曲")
end

-- 生成模拟的每日推荐歌曲
function GenerateMockRecommendSongs()
    local mockSongs = {}
    local songNames = {
        "夜曲", "告白气球", "稻香", "晴天", "青花瓷",
        "七里香", "简单爱", "不能说的秘密", "等你下课", "菊花台",
        "双节棍", "东风破", "发如雪", "千里之外", "听妈妈的话",
        "爱在西元前", "安静", "彩虹", "星晴", "龙卷风"
    }
    
    -- 一些测试用的音乐URL
    local sampleUrls = {
        "https://music.163.com/song/media/outer/url?id=5257138.mp3", -- 周杰伦-夜曲
        "https://music.163.com/song/media/outer/url?id=418603077.mp3", -- 周杰伦-告白气球
        "https://music.163.com/song/media/outer/url?id=185792.mp3", -- 周杰伦-稻香
        "https://music.163.com/song/media/outer/url?id=186001.mp3", -- 周杰伦-晴天
        "https://music.163.com/song/media/outer/url?id=185811.mp3", -- 周杰伦-青花瓷
        "https://music.163.com/song/media/outer/url?id=186016.mp3", -- 周杰伦-七里香
        "https://music.163.com/song/media/outer/url?id=186015.mp3", -- 周杰伦-简单爱
        "https://music.163.com/song/media/outer/url?id=185798.mp3", -- 周杰伦-不能说的秘密
        "https://music.163.com/song/media/outer/url?id=529823795.mp3", -- 周杰伦-等你下课
        "https://music.163.com/song/media/outer/url?id=185813.mp3"  -- 周杰伦-菊花台
    }
    
    for i = 1, 20 do
        local url = ""
        if i <= #sampleUrls then
            url = sampleUrls[i]
        else
            -- 超过样本数量的使用默认公共音乐URL
            url = "https://music.163.com/song/media/outer/url?id=186016.mp3" -- 周杰伦-七里香
        end
        
        table.insert(mockSongs, {
            id = "mock_" .. i,
            name = songNames[i] or "推荐歌曲 " .. i,
            singer = "周杰伦",
            album = "周杰伦精选集",
            duration = math.random(180, 300),
            pic = "https://via.placeholder.com/60x60/333/fff?text=推荐",
            picUrl = "https://via.placeholder.com/60x60/333/fff?text=推荐",
            url = url,
            platform = "NETEASE",
            search_keyword = "每日推荐"
        })
    end
    
    return mockSongs
end

-- 清空每日推荐表
ClearDailyRecommendTable = function(callback)
    -- 直接删除所有记录
    exports.oxmysql:execute("DELETE FROM `daily_recommend_songs`", {}, function(result)
        -- 禁用日志输出
        -- print("每日推荐表清空完成")
        if callback then
            callback()
        end
    end)
end

-- 更新每日推荐（先清空表，再搜索新数据）
UpdateDailyRecommend = function()
    -- 先清空表
    ClearDailyRecommendTable(function()
        -- 清空后搜索新数据
        SearchDailyRecommend()
    end)
end

-- 初始化数据库
InitDatabase = function()
    -- 创建歌曲详细信息表
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS `daily_recommend_songs` (
            `id1` INT AUTO_INCREMENT PRIMARY KEY,
            `id` VARCHAR(128) NOT NULL,
            `name` VARCHAR(128) NOT NULL,
            `singer` VARCHAR(128) NOT NULL,
            `album` VARCHAR(128) DEFAULT NULL,
            `duration` INT DEFAULT 0,
            `cover_url` TEXT DEFAULT NULL,
            `url` TEXT DEFAULT NULL,
            `platform` VARCHAR(20) NOT NULL DEFAULT 'NETEASE',
            `order_index` INT NOT NULL DEFAULT 0,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_song_id (id),
            INDEX idx_order (order_index)
        )
    ]], {}, function(result)
        -- 禁用日志输出
        -- 禁用日志输出
-- print("每日推荐歌曲表初始化完成")
        
        -- 检查是否有数据，没有则初始化搜索
        exports.oxmysql:fetch("SELECT COUNT(*) as count FROM `daily_recommend_songs`", {}, function(results)
            if results and results[1].count == 0 then
                -- 表为空，需要搜索并保存
                -- 禁用日志输出
                -- print("每日推荐表为空，初始化搜索")
                SearchDailyRecommend()
            else
                -- 已有数据，加载最新的推荐
                exports.oxmysql:fetch([[
                    SELECT * FROM daily_recommend_songs
                    ORDER BY order_index ASC
                ]], {}, function(songData)
                    if songData and #songData > 0 then
                        -- 处理歌曲数据，确保格式与API返回一致
                        for _, song in ipairs(songData) do
                            song.pic = song.cover_url
                            song.picUrl = song.cover_url
                            song.source = song.platform:lower()
                        end
                        
                        dailyRecommendSongs = songData
                        lastUpdateDate = os.date("%Y-%m-%d") -- 简单使用今天日期
                        -- 禁用日志输出
                        -- print("已加载每日推荐数据，共 " .. #dailyRecommendSongs .. " 首歌曲")
                    end
                end)
            end
        end)
    end)
end

-- 设置每天00:00自动更新
local function SetupDailyUpdate()
    -- 计算到明天00:00的时间（秒）
    local now = os.time()
    local tomorrow = os.date("*t", now)
    tomorrow.hour = 0
    tomorrow.min = 0
    tomorrow.sec = 0
    tomorrow.day = tomorrow.day + 1
    local timeUntilMidnight = os.difftime(os.time(tomorrow), now)
    
    -- 设置定时器
    SetTimeout(timeUntilMidnight * 1000, function()
        -- 更新每日推荐（先清空表，再搜索新数据）
        UpdateDailyRecommend()
        
        -- 重新设置定时器
        SetupDailyUpdate()
    end)
    
    -- 禁用日志输出
    -- print("已设置每日推荐自动更新，下次更新时间: " .. os.date("%Y-%m-%d %H:%M:%S", os.time(tomorrow)))
end

-- 获取每日推荐的API
RegisterNetEvent("getDailyRecommend")
AddEventHandler("getDailyRecommend", function()
    local source = source
    
    -- 查询所有歌曲
    exports.oxmysql:fetch([[
        SELECT * FROM daily_recommend_songs
        ORDER BY order_index ASC
    ]], {}, function(songData)
        if songData and #songData > 0 then
            -- 处理歌曲数据，确保格式与API返回一致
            for _, song in ipairs(songData) do
                song.pic = song.cover_url
                song.picUrl = song.cover_url
                song.source = song.platform:lower()
                song.name = song.name or "未知歌曲"
                song.singer = song.singer or "未知歌手"
                song.search_keyword = "每日推荐"
            end
            
            -- 创建一个线程处理URL更新
            Citizen.CreateThread(function()
                -- 计数器和计时器，确保处理完所有歌曲后再返回
                local urlUpdateCount = 0
                local urlUpdated = {}
                
                for i, song in ipairs(songData) do
                    -- 为每首歌曲添加默认URL（为空的情况）
                    if not song.url or song.url == "" or song.url == "null" then
                        urlUpdateCount = urlUpdateCount + 1
                        urlUpdated[i] = false
                        
                        if song.song_id then
                            -- 查询音乐库中是否有这首歌的URL
                            exports.oxmysql:fetch("SELECT url FROM music_songs WHERE id = ?", {song.song_id}, function(urlResult)
                                if urlResult and #urlResult > 0 and urlResult[1].url and urlResult[1].url ~= "" then
                                    -- 使用找到的URL
                                    song.url = urlResult[1].url
                                    
                                    -- 更新daily_recommend_songs表中的URL
                                    exports.oxmysql:execute("UPDATE daily_recommend_songs SET url = ? WHERE id = ?", 
                                        {urlResult[1].url, song.id})
                                    
                                    urlUpdated[i] = true
                                elseif API and API.getSongUrl then
                                    -- 如果在音乐库中找不到，尝试通过API获取URL
                                    API.getSongUrl(song.platform, song.song_id, function(result)
                                        if result and result.url and result.url ~= "" then
                                            song.url = result.url
                                            -- 更新daily_recommend_songs表中的URL
                                            exports.oxmysql:execute("UPDATE daily_recommend_songs SET url = ? WHERE id = ?", 
                                                {result.url, song.id})
                                        end
                                        urlUpdated[i] = true
                                    end)
                                else
                                    urlUpdated[i] = true
                                end
                            end)
                        else
                            urlUpdated[i] = true
                        end
                    end
                end
                
                -- 如果没有需要更新的URL，直接返回
                if urlUpdateCount == 0 then
                    TriggerClientEvent("getDailyRecommend:response", source, {
                        status = "success",
                        songs = songData
                    })
                    return
                end
                
                -- 等待所有URL更新完毕或者超时
                local timeout = 0
                while true do
                    local allDone = true
                    for i=1, #urlUpdated do
                        if urlUpdated[i] == false then
                            allDone = false
                            break
                        end
                    end
                    
                    if allDone or timeout > 200 then -- 最多等待2秒
                        -- 返回给客户端
                        TriggerClientEvent("getDailyRecommend:response", source, {
                            status = "success",
                            songs = songData
                        })
                        break
                    end
                    
                    timeout = timeout + 1
                    Citizen.Wait(10) -- 等待10ms再检查
                end
            end)
        else
            -- 数据库中没有歌曲，需要搜索
            SearchDailyRecommendAndRespond(source)
        end
    end)
end)

-- 搜索并响应客户端
function SearchDailyRecommendAndRespond(source)
    -- 禁用日志输出
    -- print("每日推荐表为空，初始化搜索")
    -- 使用API直接搜索
    if API and API.searchMusic then
        API.searchMusic("NETEASE", "每日推荐", function(result)
            if result and type(result) == "table" and #result > 0 then
                -- 搜索成功，处理结果确保数据类型正确
                for _, song in ipairs(result) do
                    -- 确保必要字段存在且类型正确
                    if not song.id or song.id == "" then
                        song.id = song.platform .. "_" .. (song.mid or "") .. "_" .. song.name .. "_" .. song.singer
                    end
                    
                    -- 确保duration是数字类型
                    if song.duration then
                        if type(song.duration) == "number" then
                            -- 已经是数字，不需要处理
                        elseif type(song.duration) == "string" then
                            song.duration = tonumber(song.duration) or 0
                        elseif type(song.duration) == "table" then
                            song.duration = tonumber(tostring(song.duration)) or 0
                        else
                            song.duration = 0
                        end
                    else
                        song.duration = 0
                    end
                    
                    -- 确保其他字段也有正确的类型
                    song.name = song.name or "未知歌曲"
                    song.singer = song.singer or "未知歌手"
                    song.album = song.album or "未知专辑"
                    song.platform = song.platform or "NETEASE"
                    song.search_keyword = "每日推荐"
                end
                
                -- 保存处理后的数据
                dailyRecommendSongs = result
                lastUpdateDate = os.date("%Y-%m-%d")
                
                -- 创建一个线程处理URL更新
                Citizen.CreateThread(function()
                    -- 计数器，记录需要更新的URL数量
                    local urlUpdateCount = 0
                    local urlUpdated = {}
                    
                    -- 确保每首歌曲都有URL
                    for i, song in ipairs(result) do
                        if not song.url or song.url == "" then
                            urlUpdateCount = urlUpdateCount + 1
                            urlUpdated[i] = false
                            
                            -- 如果URL为空，尝试获取
                            if API.getSongUrl then
                                API.getSongUrl(song.platform, song.id, function(urlResult)
                                    if urlResult and urlResult.url and urlResult.url ~= "" then
                                        song.url = urlResult.url
                                    end
                                    urlUpdated[i] = true
                                end)
                            else
                                urlUpdated[i] = true
                            end
                        end
                    end
                    
                    -- 如果没有需要更新的URL，直接保存并返回
                    if urlUpdateCount == 0 then
                        -- 保存到数据库
                        AddSongsToRecommend(result)
                        
                        -- 返回给客户端
                        TriggerClientEvent("getDailyRecommend:response", source, {
                            status = "success",
                            songs = dailyRecommendSongs
                        })
                        return
                    end
                    
                    -- 等待所有URL更新完毕或者超时
                    local timeout = 0
                    while true do
                        local allDone = true
                        for i=1, #urlUpdated do
                            if urlUpdated[i] == false then
                                allDone = false
                                break
                            end
                        end
                        
                        if allDone or timeout > 200 then -- 最多等待2秒
                            -- 保存到数据库
                            AddSongsToRecommend(result)
                            
                            -- 返回给客户端
                            TriggerClientEvent("getDailyRecommend:response", source, {
                                status = "success",
                                songs = dailyRecommendSongs
                            })
                            break
                        end
                        
                        timeout = timeout + 1
                        Citizen.Wait(10) -- 等待10ms再检查
                    end
                end)
            else
                -- 搜索失败，使用模拟数据
                local mockSongs = GenerateMockRecommendSongs()
                dailyRecommendSongs = mockSongs
                lastUpdateDate = os.date("%Y-%m-%d")
                
                -- 保存到数据库
                AddSongsToRecommend(mockSongs)
                
                -- 返回给客户端
                TriggerClientEvent("getDailyRecommend:response", source, {
                    status = "success",
                    songs = dailyRecommendSongs
                })
            end
        end, 20)
    else
        -- 无法搜索，使用模拟数据
        local mockSongs = GenerateMockRecommendSongs()
        dailyRecommendSongs = mockSongs
        lastUpdateDate = os.date("%Y-%m-%d")
        
        -- 保存到数据库
        AddSongsToRecommend(mockSongs)
        
        -- 返回给客户端
        TriggerClientEvent("getDailyRecommend:response", source, {
            status = "success",
            songs = dailyRecommendSongs
        })
    end
end

-- 资源启动时初始化
AddEventHandler('onResourceStart', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then
        return
    end
    
    -- 初始化数据库
    InitDatabase()
    
    -- 设置每天00:00自动更新
    SetupDailyUpdate()
end) 