fx_version 'cerulean'
game 'gta5'
lua54 'yes'
author '<PERSON><PERSON>ou'
description '音乐系统插件'
version '1.0.0'

-- 优先加载配置
shared_script 'config.lua' -- 最高优先级加载

-- 声明依赖
dependencies {
    'xsound',
    'oxmysql'
}

-- 客户端脚本
client_scripts {
    -- 'config.lua', -- 移除重复加载
    'client/client.lua',
    'client/radio_client.lua'  -- 新增:音乐盒客户端脚本
}

-- 服务端脚本（按依赖顺序排列）
server_scripts {
    -- 'config.lua', -- 移除重复加载
    'lib/api.lua',           -- 首先加载API模块
    'lib/cache.lua',         -- 然后加载缓存模块
    'lib/database.lua',      -- 然后加载数据库模块
    'lib/lyrics.lua',        
    'lib/music_service.lua', 
    'lib/network.lua',       
    'server/daily_recommend.lua', 
    'server/server.lua',     
    'server/radio_server.lua', 
    'data/radio_items.lua'   
}

-- 导出服务端模块
server_exports {
    'api',
    'cache',
    'database',
    'lyrics',
    'music_service',
    'network'
}

-- UI文件
files {
    'html/index.html',
    'html/style.css',
    'html/script.js'
}

-- NUI页面
ui_page 'html/index.html'


data_file 'DLC_ITYP_REQUEST' 'stream/animations/<EMAIL>'

escrow_ignore {
    'config.lua'
    
}