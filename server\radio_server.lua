-- 音乐盒服务端脚本

-- 确保配置被加载
print("^2[音乐盒系统]^7 读取配置中...")
if not Config then
    Config = {}
    print("^1[音乐盒系统]^7 配置未加载，创建空配置对象")
end

-- 检查并输出RadioItem配置
if Config.RadioItem then
    print("^2[音乐盒系统]^7 使用物品名称: " .. Config.RadioItem)
else
    print("^1[音乐盒系统]^7 警告：RadioItem未设置！")
end

local radios = {} -- 储存服务器所有的音乐盒信息
local playerRadios = {} -- 跟踪每个玩家放置的音乐盒ID

-- 存储所有音乐盒
local radioList = {}

-- 全局音乐盒ID计数器，确保ID唯一
local nextRadioId = 1

-- 调试日志
local DEBUG = false -- 是否启用调试日志

-- 调试输出
local function debugPrint(...)
    -- 完全禁用输出，即使DEBUG为true也不输出
    return
end

-- 添加事件处理函数用于共享音乐盒数据
RegisterNetEvent('music:getRadioData')
AddEventHandler('music:getRadioData', function(cb)
    if cb then
        cb(radios, playerRadios)
    end
end)

-- 检查框架是否可用
local function IsESXAvailable()
    local resourceState = GetResourceState('es_extended')
    if resourceState == "started" or resourceState == "starting" then
        return true
    end
    return false
end

-- 检查QBCore框架是否可用
local function IsQBCoreAvailable()
    local resourceState = GetResourceState('qb-core')
    if resourceState == "started" or resourceState == "starting" then
        return true
    end
    return false
end

-- 安全地获取QBCore对象
local function GetQBCore()
    local QBCore = nil
    
    -- 使用pcall安全地尝试获取QBCore
    local success = pcall(function()
        QBCore = exports['qb-core']:GetCoreObject()
    end)
    
    if not success or not QBCore then
        -- 尝试使用事件获取
        TriggerEvent('QBCore:GetObject', function(obj) QBCore = obj end)
        Citizen.Wait(200) -- 等待回调
    end
    
    return QBCore
end

-- 添加资源启动事件
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- 禁用音乐盒系统日志
        -- print("^2[音乐盒系统]^7 资源已启动，版本1.0")
        
        -- 检查框架状态
        if IsESXAvailable() then
            -- 禁用音乐盒系统日志
            -- print("^2[音乐盒系统]^7 已检测到ESX框架")
        elseif IsQBCoreAvailable() then
            -- 禁用音乐盒系统日志
            -- print("^2[音乐盒系统]^7 已检测到QBCore框架") 
        else
            -- 禁用音乐盒系统日志
            -- print("^3[音乐盒系统]^7 警告：未检测到支持的框架，使用独立命令 /placeradio")
        end
    end
end)

-- 注册生成音乐盒事件
RegisterNetEvent("radio:createRadio")
AddEventHandler("radio:createRadio", function(coords, rotation)
    local source = source
    local playerName = GetPlayerName(source)
    
    -- 检查玩家是否已经放置过音乐盒
    if playerRadios[source] then
        TriggerClientEvent("chatMessage", source, "[系统]", {255, 0, 0}, "您已经放置了一个音乐盒，不能同时放置多个！请先收起之前的音乐盒。")
        -- 归还物品
        GiveRadioItemToPlayer(source)
        return
    end
    
    -- 生成唯一ID
    local radioId = "radio_" .. nextRadioId
    nextRadioId = nextRadioId + 1
    
    -- 储存音乐盒信息
    radios[radioId] = {
        id = radioId,
        creator = source,
        creatorName = playerName,
        coords = coords,
        rotation = rotation,
        timestamp = os.time()
    }
    
    -- 记录玩家放置的音乐盒
    playerRadios[source] = radioId
    
    -- 通知所有客户端生成音乐盒模型
    TriggerClientEvent("radio:syncRadio", -1, radios[radioId])
    
    -- 禁用音乐盒系统日志
    -- print("^2[音乐盒系统] ^7玩家 " .. playerName .. " 放置了一个音乐盒，ID: " .. radioId)
end)

-- 删除音乐盒事件
RegisterNetEvent("radio:removeRadio")
AddEventHandler("radio:removeRadio", function(radioId)
    if radios[radioId] then
        local source = source
        local playerName = GetPlayerName(source)

        -- 检查是否是音乐盒的所有者
        if radios[radioId].creator ~= source then
            TriggerClientEvent("chatMessage", source, "[系统]", {255, 0, 0}, "这不是您放置的音乐盒，您不能删除它")
            return
        end

        -- 通知所有客户端删除音乐盒模型
        TriggerClientEvent("radio:removeRadio", -1, radioId)

        -- 从记录中移除玩家的音乐盒
        if radios[radioId].creator == source then
            playerRadios[source] = nil
        end

        -- 从服务器列表中删除
        radios[radioId] = nil

        -- 禁用音乐盒系统日志
        -- print("^2[音乐盒系统] ^7玩家 " .. playerName .. " 删除了一个音乐盒，ID: " .. radioId)
    end
end)

-- 临时删除音乐盒事件（用于扛音响功能）
RegisterNetEvent("radio:temporaryRemoveRadio")
AddEventHandler("radio:temporaryRemoveRadio", function(radioId)
    if radios[radioId] then
        local source = source

        -- 检查是否是音乐盒的所有者
        if radios[radioId].creator ~= source then
            TriggerClientEvent("chatMessage", source, "[系统]", {255, 0, 0}, "这不是您放置的音乐盒，您不能操作它")
            return
        end

        -- 标记为临时删除状态
        radios[radioId].temporaryRemoved = true
        radios[radioId].removedBy = source

        -- 通知所有客户端临时隐藏音乐盒模型（不删除数据）
        TriggerClientEvent("radio:temporaryHideRadio", -1, radioId)

        -- print("^3[音乐盒-扛音响] 临时删除音乐盒: " .. radioId)
    end
end)

-- 恢复音乐盒事件（用于扛音响功能）
RegisterNetEvent("radio:restoreRadio")
AddEventHandler("radio:restoreRadio", function(radioId, newCoords, newRotation)
    if radios[radioId] and radios[radioId].temporaryRemoved then
        local source = source

        -- 检查是否是删除者
        if radios[radioId].removedBy ~= source then
            TriggerClientEvent("chatMessage", source, "[系统]", {255, 0, 0}, "您没有权限恢复这个音乐盒")
            return
        end

        -- 更新音乐盒位置到新位置
        if newCoords and newRotation then
            radios[radioId].coords = newCoords
            radios[radioId].rotation = newRotation
            -- print("^3[音乐盒-扛音响] 更新音乐盒位置: " .. radioId .. " 到新坐标")
        end

        -- 移除临时删除标记
        radios[radioId].temporaryRemoved = nil
        radios[radioId].removedBy = nil

        -- 通知所有客户端重新创建音乐盒模型
        TriggerClientEvent("radio:syncRadio", -1, radios[radioId])

        -- print("^3[音乐盒-扛音响] 恢复音乐盒: " .. radioId)
    end
end)

-- 当玩家加入游戏时，同步所有现有的音乐盒
RegisterNetEvent("radio:requestSync")
AddEventHandler("radio:requestSync", function()
    local source = source
    
    -- 发送所有现有的音乐盒信息给新加入的玩家
    for _, radio in pairs(radios) do
        TriggerClientEvent("radio:syncRadio", source, radio)
    end
end)

-- 使用物品事件 - 修改为客户端计算放置位置
RegisterNetEvent("radio:useRadioItem")
AddEventHandler("radio:useRadioItem", function()
    local source = source
    
    -- 移除背包中的音乐盒物品
    RemoveRadioItemFromPlayer(source)
    
    -- 通知客户端创建音乐盒
    TriggerClientEvent("radio:placeRadio", source)
end)

-- 回收音乐盒到物品栏
RegisterNetEvent("radio:giveRadioItem")
AddEventHandler("radio:giveRadioItem", function()
    local source = source
    
    -- 给玩家添加音乐盒物品
    GiveRadioItemToPlayer(source)
    
    -- 通知玩家
    TriggerClientEvent("chatMessage", source, "[系统]", {0, 255, 0}, "音乐盒已添加到您的物品栏")
end)

-- 从玩家背包中移除音乐盒物品
function RemoveRadioItemFromPlayer(playerId)
    local itemName = Config.RadioItem

    if not itemName then
        print("^1[音乐盒系统]^7 错误：未设置RadioItem配置，无法移除物品")
        return
    end

    -- 优先使用ox_inventory
    if GetResourceState('ox_inventory') == 'started' then
        local success = exports.ox_inventory:RemoveItem(playerId, itemName, 1)
        if success then
            print("^2[音乐盒系统]^7 从ox_inventory移除物品: " .. itemName)
            return
        else
            print("^1[音乐盒系统]^7 ox_inventory移除物品失败: " .. itemName)
        end
    end

    -- 检测ESX框架
    if IsESXAvailable() then
        local ESX = exports["es_extended"]:getSharedObject()
        local xPlayer = ESX.GetPlayerFromId(playerId)
        if xPlayer then
            xPlayer.removeInventoryItem(itemName, 1)
            print("^2[音乐盒系统]^7 从ESX背包移除物品: " .. itemName)
            return
        end
    end

    -- 如果没有找到框架，跳过
    print("^3[音乐盒系统]^7 未找到物品系统框架，跳过物品移除")
end

-- 给玩家背包添加音乐盒物品
function GiveRadioItemToPlayer(playerId)
    local itemName = Config.RadioItem

    if not itemName then
        print("^1[音乐盒系统]^7 错误：未设置RadioItem配置，无法添加物品")
        return
    end

    -- 优先使用ox_inventory
    if GetResourceState('ox_inventory') == 'started' then
        local success = exports.ox_inventory:AddItem(playerId, itemName, 1)
        if success then
            print("^2[音乐盒系统]^7 添加物品到ox_inventory: " .. itemName)
            return
        else
            print("^1[音乐盒系统]^7 ox_inventory添加物品失败: " .. itemName)
        end
    end

    -- 检测ESX框架
    if IsESXAvailable() then
        local ESX = exports["es_extended"]:getSharedObject()
        local xPlayer = ESX.GetPlayerFromId(playerId)
        if xPlayer then
            xPlayer.addInventoryItem(itemName, 1)
            print("^2[音乐盒系统]^7 添加物品到ESX背包: " .. itemName)
            return
        end
    end

    -- 如果没有找到框架，跳过
    print("^3[音乐盒系统]^7 未找到物品系统框架，跳过物品添加")
end

-- 添加诊断命令（允许所有玩家使用）
RegisterCommand("radiodiagnose", function(source, args)
    local src = source
    -- 获取当前配置的物品名称
    local itemName = Config.RadioItem
    
    -- 输出诊断信息
    print("^2[音乐盒系统诊断]^7 开始诊断...")
    print("^2[音乐盒系统诊断]^7 使用物品名称: " .. (itemName or "未配置"))
    
    -- 检查框架状态
    print("^2[音乐盒系统诊断]^7 ESX框架: " .. (IsESXAvailable() and "可用" or "不可用"))
    print("^2[音乐盒系统诊断]^7 QBCore框架: " .. (IsQBCoreAvailable() and "可用" or "不可用"))

    -- 检查ox_inventory状态
    local oxInventoryState = GetResourceState('ox_inventory')
    print("^2[音乐盒系统诊断]^7 OX_Inventory: " .. (oxInventoryState == "started" and "可用" or "不可用 (" .. oxInventoryState .. ")"))
    
    -- 检查注册的物品
    if IsESXAvailable() then
        local ESX = exports["es_extended"]:getSharedObject()
        print("^2[音乐盒系统诊断]^7 ESX物品注册状态: 正在检测...")
    end
    
    if IsQBCoreAvailable() then
        local QBCore = GetQBCore()
        if not QBCore then
            print("^1[音乐盒系统]^7 无法获取QBCore对象")
        else
        local itemExists = QBCore.Shared.Items[itemName] ~= nil
        print("^2[音乐盒系统诊断]^7 QBCore物品注册状态: " .. (itemExists and "已注册" or "未注册"))
        if src > 0 then
            local oxInventoryState = GetResourceState('ox_inventory')
            if oxInventoryState == "started" then
                TriggerClientEvent('chatMessage', src, "[系统]", {0, 255, 0}, "使用ox_inventory物品系统")
                TriggerClientEvent('chatMessage', src, "[系统]", {255, 165, 0}, "请确保在 ox_inventory/data/items.lua 中添加了物品定义")
                TriggerClientEvent('chatMessage', src, "[系统]", {255, 165, 0}, "参考 OX-INVENTORY-SETUP.md 文件获取详细说明")
            else
                TriggerClientEvent('chatMessage', src, "[系统]", {0, 255, 0}, "物品 '" .. itemName .. "' 在 QBCore 中" .. (itemExists and "已正确注册" or "未注册"))
                if not itemExists then
                    TriggerClientEvent('chatMessage', src, "[系统]", {255, 165, 0}, "请检查 qb-core/shared/items.lua 中是否添加了物品定义")
                end
            end
            end
        end
    end
    
    -- 输出当前音乐盒数量
    local count = 0
    for _ in pairs(radios) do count = count + 1 end
    print("^2[音乐盒系统诊断]^7 当前音乐盒数量: " .. count)
    
    if src > 0 then
        TriggerClientEvent('chatMessage', src, "[系统]", {0, 255, 0}, "音乐盒系统诊断已完成，请查看服务器控制台")
    end
end, false) -- false表示所有玩家都可以使用

-- 添加快速测试命令
RegisterCommand("testradioitem", function(source, args)
    local src = source
    if src == 0 then return end -- 只允许玩家使用

    local itemName = Config.RadioItem or "yinyue"

    -- 优先使用ox_inventory
    if GetResourceState('ox_inventory') == 'started' then
        local success = exports.ox_inventory:AddItem(src, itemName, 1)
        if success then
            TriggerClientEvent('chatMessage', src, "[测试]", {0, 255, 0}, "已给予音乐盒物品 (ox_inventory)，请尝试使用")
            return
        else
            TriggerClientEvent('chatMessage', src, "[测试]", {255, 0, 0}, "ox_inventory添加物品失败")
        end
    end

    -- 检测框架并尝试给予物品
    if IsQBCoreAvailable() then
        TriggerClientEvent('chatMessage', src, "[测试]", {255, 165, 0}, "ox_inventory不可用，请检查配置")
    elseif IsESXAvailable() then
        local ESX = exports["es_extended"]:getSharedObject()
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer then
            xPlayer.addInventoryItem(itemName, 1)
            TriggerClientEvent('chatMessage', src, "[测试]", {0, 255, 0}, "已给予音乐盒物品 (ESX)，请尝试使用")
        else
            TriggerClientEvent('chatMessage', src, "[测试]", {255, 0, 0}, "无法获取ESX玩家数据")
        end
    else
        TriggerClientEvent('chatMessage', src, "[测试]", {255, 0, 0}, "未检测到支持的框架")
    end
end, false)



-- 同步音乐盒歌曲信息
RegisterNetEvent("radio:syncRadioSong")
AddEventHandler("radio:syncRadioSong", function(radioId, songData)
    local source = source

    -- 验证音乐盒是否存在且属于该玩家
    if not radios[radioId] or radios[radioId].creator ~= source then
        return
    end

    -- 向所有玩家广播音乐盒的歌曲信息
    TriggerClientEvent("radio:syncRadioSong", -1, radioId, songData, source)
end)

-- ========================================
-- 扛起音响功能 (单人模式)
-- ========================================
-- 不需要服务端处理，纯客户端功能

-- 创建测试音乐盒
RegisterNetEvent("radio:createTestRadio")
AddEventHandler("radio:createTestRadio", function(coords, rotation, testSongIndex)
    local source = source
    local playerName = GetPlayerName(source)
    
    -- 生成唯一ID
    local radioId = "test_radio_" .. nextRadioId
    nextRadioId = nextRadioId + 1
    
    -- 创建一个虚拟玩家ID (使用一个大数字作为测试玩家的ID，确保与实际玩家不冲突)
    local testPlayerId = 999666 -- 使用一个大数字
    
    -- 储存音乐盒信息，指定虚拟创建者ID
    radios[radioId] = {
        id = radioId,
        creator = testPlayerId,  -- 使用虚拟玩家ID而不是当前玩家ID
        creatorName = "测试账号",  -- 虚拟玩家名称
        coords = coords,
        rotation = rotation,
        timestamp = os.time(),
        isTest = true,  -- 标记为测试音乐盒
        testSongIndex = testSongIndex or 1
    }
    
    -- 这里不记录测试音乐盒到playerRadios中，以免影响原有逻辑
    -- 测试音乐盒只是为了测试显示不同的歌词
    
    -- 通知所有客户端生成音乐盒模型
    TriggerClientEvent("radio:syncRadio", -1, radios[radioId])
    
    -- 额外通知创建者设置测试歌曲
    TriggerClientEvent("radio:setupTestRadio", source, radioId, testSongIndex or 1)
    
    -- 日志
    print("^2[测试系统] ^7玩家 " .. playerName .. " 放置了测试音乐盒 #" .. radioId .. "，创建者ID为虚拟ID: " .. testPlayerId .. "，歌曲索引:" .. (testSongIndex or 1))
    
    -- 通知玩家
    TriggerClientEvent("chatMessage", source, "[测试系统]", {0, 255, 0}, "您放置了一个属于测试账号的音乐盒 #" .. radioId .. "，您无权操作此音乐盒")
end)

-- 清理所有测试音乐盒
RegisterNetEvent("radio:cleanTestRadios")
AddEventHandler("radio:cleanTestRadios", function()
    local source = source
    local playerName = GetPlayerName(source)
    local cleanedCount = 0

    -- 查找并删除所有测试音乐盒
    local toRemove = {}
    for radioId, radioData in pairs(radios) do
        if radioData.isTest then
            table.insert(toRemove, radioId)
        end
    end

    -- 删除测试音乐盒
    for _, radioId in ipairs(toRemove) do
        -- 通知所有客户端删除音乐盒模型
        TriggerClientEvent("radio:removeRadio", -1, radioId)

        -- 从服务器列表中删除
        radios[radioId] = nil
        cleanedCount = cleanedCount + 1
    end

    -- 通知玩家
    TriggerClientEvent("chatMessage", source, "[测试系统]", {0, 255, 0}, "已清理 " .. cleanedCount .. " 个测试音乐盒")
    print("^2[测试系统] ^7玩家 " .. playerName .. " 清理了 " .. cleanedCount .. " 个测试音乐盒")
end)

-- 清理所有音乐盒
RegisterNetEvent("radio:clearAllRadios")
AddEventHandler("radio:clearAllRadios", function()
    local source = source
    local playerName = GetPlayerName(source)
    local cleanedCount = 0

    -- 获取所有音乐盒ID
    local toRemove = {}
    for radioId, radioData in pairs(radios) do
        table.insert(toRemove, radioId)
    end

    -- 删除所有音乐盒
    for _, radioId in ipairs(toRemove) do
        -- 通知所有客户端删除音乐盒模型
        TriggerClientEvent("radio:removeRadio", -1, radioId)

        -- 从服务器列表中删除
        radios[radioId] = nil
        cleanedCount = cleanedCount + 1

        print(string.format("^3[音乐盒系统]^7 %s 删除了音乐盒 ID: %s", playerName, radioId))
    end

    -- 清空玩家音乐盒记录
    playerRadios = {}

    -- 通知所有玩家清理结果
    TriggerClientEvent("chatMessage", -1, "[系统]", {255, 165, 0},
        string.format("%s 清理了所有音乐盒 (共 %d 个)", playerName, cleanedCount))

    print(string.format("^1[音乐盒系统]^7 %s 清理了所有音乐盒，共删除 %d 个", playerName, cleanedCount))
end)

-- 向指定音乐盒播放音乐
RegisterNetEvent("radio:playToSpecificRadio")
AddEventHandler("radio:playToSpecificRadio", function(radioId, songIndex)
    local source = source
    local playerName = GetPlayerName(source)

    -- 检查音乐盒是否存在
    if not radios[radioId] then
        TriggerClientEvent("chatMessage", source, "[测试系统]", {255, 0, 0}, "音乐盒 #" .. radioId .. " 不存在")
        return
    end

    -- 通知客户端播放测试歌曲
    TriggerClientEvent("radio:playTestSong", source, radioId, songIndex or 1)

    -- 通知玩家
    TriggerClientEvent("chatMessage", source, "[测试系统]", {0, 255, 0}, "正在向音乐盒 #" .. radioId .. " 播放测试歌曲 #" .. (songIndex or 1))
end)

-- 添加处理暂停音乐请求的事件
RegisterNetEvent("radio:requestPauseMusic")
AddEventHandler("radio:requestPauseMusic", function(creatorId, radioId)
    local source = source
    local pauserName = GetPlayerName(source)
    local ownerName = GetPlayerName(creatorId) or "离线玩家"
    
    -- 调试输出（只在服务器控制台显示）
    -- print("^3[音乐盒系统]^7 玩家 " .. pauserName .. " 请求暂停 ID: " .. creatorId .. " (" .. ownerName .. ") 的音乐盒 #" .. radioId)
    
    -- 获取附近的玩家
    local nearbyPlayers = GetNearbyPlayers(source, 30.0)  -- 30米范围内的玩家
    
    -- 向附近的所有玩家和音乐盒所有者广播暂停事件
    for _, playerId in ipairs(nearbyPlayers) do
        TriggerClientEvent("radio:pauseMusicAtClient", playerId, creatorId, radioId, source)
    end
    
    -- 确保音乐盒所有者也收到通知（如果不在附近）
    if not table.contains(nearbyPlayers, creatorId) and creatorId > 0 then
        TriggerClientEvent("radio:pauseMusicAtClient", creatorId, creatorId, radioId, source)
    end
    
    -- 确保操作者也收到通知（如果不在附近玩家列表中）
    if not table.contains(nearbyPlayers, source) then
        TriggerClientEvent("radio:pauseMusicAtClient", source, creatorId, radioId, source)
    end
    
    -- 通知所有者（如果在线且不是操作者本人）
    if creatorId ~= source and creatorId > 0 then
        TriggerClientEvent("chatMessage", creatorId, "[音乐盒系统]", {255, 165, 0}, pauserName .. " 暂停了您的音乐盒 #" .. radioId .. " 的音乐")
    end
end)

-- 添加处理恢复音乐请求的事件
RegisterNetEvent("radio:requestResumeMusic")
AddEventHandler("radio:requestResumeMusic", function(creatorId, radioId)
    local source = source
    local resumerName = GetPlayerName(source)
    local ownerName = GetPlayerName(creatorId) or "离线玩家"
    
    -- 调试输出（只在服务器控制台显示）
    -- print("^3[音乐盒系统]^7 玩家 " .. resumerName .. " 请求恢复 ID: " .. creatorId .. " (" .. ownerName .. ") 的音乐盒 #" .. radioId .. " 的音乐")
    
    -- 获取附近的玩家
    local nearbyPlayers = GetNearbyPlayers(source, 30.0)  -- 30米范围内的玩家
    
    -- 向附近的所有玩家和音乐盒所有者广播恢复事件
    for _, playerId in ipairs(nearbyPlayers) do
        TriggerClientEvent("radio:resumeMusicAtClient", playerId, creatorId, radioId, source)
    end
    
    -- 确保音乐盒所有者也收到通知（如果不在附近）
    if not table.contains(nearbyPlayers, creatorId) and creatorId > 0 then
        TriggerClientEvent("radio:resumeMusicAtClient", creatorId, creatorId, radioId, source)
    end
    
    -- 确保操作者也收到通知（如果不在附近玩家列表中）
    if not table.contains(nearbyPlayers, source) then
        TriggerClientEvent("radio:resumeMusicAtClient", source, creatorId, radioId, source)
    end
    
    -- 通知所有者（如果在线且不是操作者本人）
    if creatorId ~= source and creatorId > 0 then
        TriggerClientEvent("chatMessage", creatorId, "[音乐盒系统]", {0, 255, 0}, resumerName .. " 恢复了您的音乐盒 #" .. radioId .. " 的音乐")
    end
end)

-- 获取附近玩家的辅助函数
function GetNearbyPlayers(sourcePlayer, radius)
    local playerList = {}
    local sourceCoords = GetEntityCoords(GetPlayerPed(sourcePlayer))
    
    -- 遍历所有在线玩家
    for _, playerId in ipairs(GetPlayers()) do
        local playerCoords = GetEntityCoords(GetPlayerPed(playerId))
        local distance = #(sourceCoords - playerCoords)
        
        -- 如果在指定范围内，加入列表
        if distance <= radius then
            table.insert(playerList, tonumber(playerId))
        end
    end
    
    return playerList
end

-- 检查表中是否包含某个值的辅助函数
function table.contains(tbl, element)
    for _, value in ipairs(tbl) do
        if value == element then
            return true
        end
    end
    return false
end

-- 添加手动重新注册物品的命令
RegisterCommand("reregisterradio", function(source, args)
    local src = source
    if src == 0 then return end -- 只允许玩家使用

    local itemName = Config.RadioItem or "yinyue"

    -- 检测QBCore框架并重新注册物品
    if IsQBCoreAvailable() then
        local QBCore = GetQBCore()
        if QBCore then
            -- 重新注册物品
            QBCore.Functions.CreateUseableItem(itemName, function(source, item)
                print("^2[音乐盒系统]^7 玩家 " .. GetPlayerName(source) .. " 使用了音乐盒物品")

                -- 从背包中移除物品
                local Player = QBCore.Functions.GetPlayer(source)
                if Player then
                    -- 使用正确的QB-Core API
                    if Player.Functions and Player.Functions.RemoveItem then
                        Player.Functions.RemoveItem(itemName, 1)
                    elseif Player.removeItem then
                        Player.removeItem(itemName, 1)
                    end

                    -- 检查物品是否存在于共享物品中
                    if QBCore.Shared.Items[itemName] then
                        TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items[itemName], "remove")
                    end
                end

                -- 直接触发客户端放置事件
                TriggerClientEvent('radio:placeRadio', source)
            end)

            TriggerClientEvent('chatMessage', src, "[系统]", {0, 255, 0}, "音乐盒物品已重新注册，请尝试使用")
            print("^2[音乐盒系统]^7 手动重新注册物品成功: " .. itemName)
        else
            TriggerClientEvent('chatMessage', src, "[系统]", {255, 0, 0}, "无法获取QBCore对象")
        end
    else
        TriggerClientEvent('chatMessage', src, "[系统]", {255, 0, 0}, "QBCore框架不可用")
    end
end, false)

-- 添加调试Player对象结构的命令
RegisterCommand("debugplayer", function(source, args)
    local src = source
    if src == 0 then return end

    if IsQBCoreAvailable() then
        local QBCore = GetQBCore()
        if QBCore then
            local Player = QBCore.Functions.GetPlayer(src)
            if Player then
                print("^3[调试]^7 Player对象结构:")
                print("^3[调试]^7 Player.Functions存在: " .. tostring(Player.Functions ~= nil))
                if Player.Functions then
                    print("^3[调试]^7 Player.Functions.AddItem存在: " .. tostring(Player.Functions.AddItem ~= nil))
                    print("^3[调试]^7 Player.Functions.RemoveItem存在: " .. tostring(Player.Functions.RemoveItem ~= nil))

                    -- 检查其他可能的方法名
                    for k, v in pairs(Player.Functions) do
                        if type(v) == "function" and (string.lower(k):find("add") or string.lower(k):find("remove") or string.lower(k):find("item")) then
                            print("^3[调试]^7 Player.Functions." .. k .. " 存在 (类型: " .. type(v) .. ")")
                        end
                    end
                end
                print("^3[调试]^7 Player.addItem存在: " .. tostring(Player.addItem ~= nil))
                print("^3[调试]^7 Player.removeItem存在: " .. tostring(Player.removeItem ~= nil))

                -- 检查Player对象的所有方法
                print("^3[调试]^7 Player对象的所有方法:")
                for k, v in pairs(Player) do
                    if type(v) == "function" and (string.lower(k):find("add") or string.lower(k):find("remove") or string.lower(k):find("item")) then
                        print("^3[调试]^7 Player." .. k .. " 存在 (类型: " .. type(v) .. ")")
                    end
                end

                TriggerClientEvent('chatMessage', src, "[调试]", {0, 255, 255}, "Player对象结构已输出到服务器控制台")
            else
                TriggerClientEvent('chatMessage', src, "[调试]", {255, 0, 0}, "无法获取Player对象")
            end
        end
    end
end, false)