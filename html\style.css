/* ==================== 基础样式 ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 深色主题 */
    --primary-bg: #1a1a1a;
    --secondary-bg: #2a2a2a;
    --sidebar-bg: #141414;
    --hover-bg: #333333;
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #666666;
    --border-color: #333333;
    --shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    --gradient: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    --player-bg: #1e1e1e;
}

[data-theme="light"] {
    --primary-bg: #ffffff;
    --secondary-bg: #f8f9fa;
    --sidebar-bg: #f0f0f0;
    --hover-bg: #e9ecef;
    --primary-color: #007bff;
    --secondary-color: #28a745;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #e9ecef;
    --shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --gradient: linear-gradient(135deg, #007bff, #28a745);
    --player-bg: #ffffff;
}

[data-theme="neon"] {
    --primary-bg: #0d1117;
    --secondary-bg: #161b22;
    --sidebar-bg: #0d1117;
    --hover-bg: #21262d;
    --primary-color: #00ff88;
    --secondary-color: #ff0080;
    --text-primary: #f0f6fc;
    --text-secondary: #7d8590;
    --text-muted: #484f58;
    --border-color: #30363d;
    --shadow: 0 4px 15px rgba(0, 255, 136, 0.2);
    --gradient: linear-gradient(135deg, #00ff88, #ff0080);
    --player-bg: #161b22;
}

[data-theme="gradient"] {
    --primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-bg: rgba(255, 255, 255, 0.1);
    --sidebar-bg: rgba(0, 0, 0, 0.3);
    --hover-bg: rgba(255, 255, 255, 0.2);
    --primary-color: #ffffff;
    --secondary-color: #f8f9fa;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --gradient: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    --player-bg: rgba(0, 0, 0, 0.4);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: transparent; /* 修改为透明背景 */
    color: var(--text-primary);
    overflow: hidden;
    user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.app {
    display: flex;
    flex-direction: column;
    height: 80vh; /* 高度为屏幕高度的80% */
    width: 80vw; /* 宽度为屏幕宽度的80% */
    max-width: 1200px; /* 最大宽度 */
    max-height: 800px; /* 最大高度 */
    background: var(--primary-bg);
    border-radius: 10px; /* 圆角边框 */
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5); /* 添加阴影 */
    padding-bottom: 80px; /* 为播放栏留出空间 */
}

/* ==================== 主容器布局 ==================== */
.main-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* ==================== 侧边栏样式 ==================== */
.sidebar {
    width: 220px;
    min-width: 220px; /* 确保最小宽度 */
    background: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    box-shadow: var(--shadow);
    border-top-left-radius: 10px; /* 添加圆角 */
    border-bottom-left-radius: 10px; /* 添加圆角 */
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent; /* Firefox */
    max-height: calc(100% - 80px); /* 减去播放栏的高度 */
}

/* 侧边栏滚动条样式 */
.sidebar::-webkit-scrollbar {
    width: 6px; /* 滚动条宽度 */
}

.sidebar::-webkit-scrollbar-track {
    background: transparent; /* 滚动条轨道背景透明 */
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2); /* 半透明滚动条 */
    border-radius: 3px; /* 圆角 */
    transition: background 0.3s ease; /* 过渡效果 */
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3); /* 悬停时稍微亮一点 */
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.logo i {
    font-size: 24px;
}

.theme-selector {
    display: flex;
    gap: 8px;
}

.theme-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: var(--secondary-bg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-btn:hover {
    background: var(--hover-bg);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.nav-menu {
    padding: 10px 0; /* 减小内边距 */
    flex: 0 0 auto; /* 不伸缩，保持固定高度 */
}

.nav-menu ul, .my-music ul {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 10px; /* 减小间距 */
    padding: 8px 20px; /* 减小内边距 */
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item:hover {
    background: var(--hover-bg);
    color: var(--primary-color);
}

.nav-item.active {
    background: var(--primary-color);
    color: white;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: white;
}

.nav-item i {
    width: 20px;
    text-align: center;
}

.count {
    margin-left: auto;
    font-size: 12px;
    background: var(--text-muted);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
}

.my-music {
    padding: 10px 0; /* 减小内边距 */
    border-top: 1px solid var(--border-color);
    flex: 0 0 auto; /* 不伸缩，保持固定高度 */
}

.my-music h3 {
    padding: 0 20px 5px; /* 减小底部内边距 */
    font-size: 13px; /* 减小字体大小 */
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 5px; /* 添加顶部外边距 */
}

.user-playlists {
    flex: 2; /* 增加权重，让歌单列表区域更大 */
    padding: 10px 0; /* 减小内边距 */
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止溢出 */
    max-height: 60%; /* 增加最大高度 */
}

.playlist-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 5px; /* 减小底部内边距 */
    margin-bottom: 5px; /* 添加外边距 */
}

.playlist-header h4 {
    font-size: 13px; /* 减小字体大小 */
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0; /* 移除外边距 */
}

.btn-create-playlist {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-create-playlist:hover {
    transform: scale(1.1);
}

.playlists-list {
    list-style: none;
    flex: 1; /* 占用剩余空间 */
    overflow-y: auto; /* 允许垂直滚动 */
    padding: 0 5px; /* 添加内边距 */
    margin: 2px 0; /* 减小外边距 */
    max-height: 300px; /* 设置最大高度 */
}

.playlists-list::-webkit-scrollbar {
    width: 4px;
}

.playlists-list::-webkit-scrollbar-track {
    background: transparent; /* 与侧边栏一致，使用透明背景 */
}

.playlists-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2); /* 与侧边栏一致，使用半透明 */
    border-radius: 3px;
}

.playlists-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3); /* 悬停效果 */
}

/* ==================== 主内容区样式 ==================== */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--primary-bg);
}

/* ==================== 顶部搜索栏 ==================== */
.top-bar {
    height: 60px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    background: var(--secondary-bg);
    z-index: 10;
}

.search-container {
    flex: 1;
    max-width: 500px;
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--primary-bg);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    padding: 8px 15px;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
}

.search-box i {
    color: var(--text-muted);
    margin-right: 10px;
}

.search-box input {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 14px;
    outline: none;
}

.search-box input::placeholder {
    color: var(--text-muted);
}

.platform-selector {
    background: var(--secondary-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 5px 10px;
    margin: 0 10px;
    font-size: 14px;
    outline: none;
    cursor: pointer;
    min-width: 120px; /* 确保有足够的宽度 */
}

.platform-selector option {
    background: var(--secondary-bg);
    color: var(--text-primary);
    padding: 5px;
}

.search-btn {
    margin-left: 10px;
    padding: 6px 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.search-btn:hover {
    background: var(--secondary-color);
}

.user-actions {
    display: flex;
    gap: 10px;
}

.btn-minimize, .btn-close {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: var(--secondary-bg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-minimize:hover {
    background: var(--secondary-color);
    color: white;
}

.btn-close:hover {
    background: #ff4757;
    color: white;
}

/* ==================== 内容视图区域 ==================== */
.content-views {
    flex: 1;
    overflow-y: auto;
    padding: 20px 30px;
    padding-bottom: 80px; /* 增加更多底部内边距，确保滚动到底部时内容不被遮挡 */
    scrollbar-width: thin;
    scrollbar-color: var(--text-muted) transparent;
    max-height: calc(100vh - 180px); /* 调整最大高度，确保有足够空间且不会与播放器重叠 */
}

.content-views::-webkit-scrollbar {
    width: 6px;
}

.content-views::-webkit-scrollbar-track {
    background: transparent;
}

.content-views::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 3px;
}

/* ==================== 视图样式 ==================== */
.view {
    display: none;
    animation: fadeIn 0.3s ease;
    padding-bottom: 150px; /* 进一步增加底部内边距，确保内容不被播放栏遮挡 */
}

.view.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* ==================== 视图标题和按钮 ==================== */
.view-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.view-header h2 {
    font-size: 24px;
    font-weight: 300;
    color: var(--text-primary);
}

.view-subtitle {
    color: var(--text-secondary);
    margin-top: 5px;
}

.view-actions {
    display: flex;
    gap: 15px;
}

.btn-play-all, .btn-create-new-playlist, .btn-clear-history, .btn-back-to-playlists {
    padding: 8px 16px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.btn-play-all:hover, .btn-create-new-playlist:hover, .btn-clear-history:hover, .btn-back-to-playlists:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* ==================== 轮播图样式 ==================== */
.banner-carousel {
    margin-bottom: 30px;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    height: 180px; /* 减小高度 */
}

.banner-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.banner-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.banner-slide.active {
    opacity: 1;
}

.banner-content {
    text-align: center;
    color: white;
}

.banner-content h3 {
    font-size: 32px;
    font-weight: 300;
    margin-bottom: 10px;
}

.banner-content p {
    font-size: 16px;
    opacity: 0.9;
}

.banner-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: white;
    transform: scale(1.2);
}

/* ==================== 内容部分样式 ==================== */
.content-sections {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.music-section {
    background: var(--secondary-bg);
    border-radius: 15px;
    padding: 25px;
    box-shadow: var(--shadow);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 20px;
    font-weight: 500;
}

.more-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.more-link:hover {
    color: var(--secondary-color);
}

/* ==================== 歌曲网格样式 ==================== */
.song-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.song-card {
    background: var(--primary-bg);
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.song-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
    border-color: var(--primary-color);
}

.song-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.song-card:hover::before {
    left: 100%;
}

.song-cover {
    width: 100%;
    height: 120px;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    position: relative;
}

.song-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.song-card:hover .song-cover img {
    transform: scale(1.1);
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.song-card:hover .play-overlay {
    opacity: 1;
}

.play-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-btn:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
}

.song-info {
    text-align: center;
}

.song-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-artist {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ==================== 歌单网格样式 ==================== */
.playlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.playlist-card {
    background: var(--secondary-bg);
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    text-align: center;
}

.playlist-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
    border-color: var(--primary-color);
}

.playlist-cover {
    width: 100%;
    height: 120px;
    border-radius: 8px;
    margin-bottom: 12px;
    background: var(--gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    position: relative;
    overflow: hidden;
}

.playlist-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.playlist-count {
    font-size: 12px;
    color: var(--text-secondary);
}

/* ==================== 歌曲列表样式 ==================== */
.song-list {
    padding: 10px;
    padding-bottom: 60px; /* 进一步增加底部内边距 */
    border-radius: 8px;
    background: var(--secondary-bg);
    box-shadow: var(--shadow);
    /* 移除滚动功能，使用父容器的滚动条 */
    overflow-y: visible;
    max-height: none;
}

.song-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.song-item:hover {
    background: var(--hover-bg);
}

.song-item:last-child {
    border-bottom: none;
    margin-bottom: 40px; /* 增加最后一个歌曲项的底部边距 */
}

.song-item.playing {
    background: rgba(255, 107, 107, 0.1);
    border-left: 4px solid var(--primary-color);
}

.song-index {
    width: 30px;
    text-align: center;
    color: var(--text-muted);
    font-size: 14px;
}

.song-item.playing .song-index {
    color: var(--primary-color);
}

.song-item-cover {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
}

.song-item-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.song-item-info {
    flex: 1;
    min-width: 0;
}

.song-item-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-item-artist {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-item-duration {
    color: var(--text-muted);
    font-size: 12px;
    min-width: 45px;
    text-align: right;
}

.song-item-actions {
    display: flex;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.song-item:hover .song-item-actions {
    opacity: 1;
}

.song-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: var(--hover-bg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.song-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* ==================== 排行榜样式 ==================== */
.ranking-tabs {
    display: flex;
    gap: 2px;
    background: var(--secondary-bg);
    border-radius: 25px;
    padding: 4px;
    margin-bottom: 30px;
    width: fit-content;
}

.ranking-tab {
    padding: 12px 25px;
    border: none;
    border-radius: 20px;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.ranking-tab.active {
    background: var(--primary-color);
    color: white;
}

.ranking-tab:hover:not(.active) {
    background: var(--hover-bg);
    color: var(--text-primary);
}

/* ==================== 每日推荐样式 ==================== */
.daily-recommend {
    background: var(--secondary-bg);
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
}

.recommend-date {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    color: var(--text-secondary);
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.recommend-date i {
    color: var(--primary-color);
    font-size: 18px;
}

#recommendView .recommend-date {
    font-size: 18px;
    font-weight: 500;
}

#recommendView .song-list {
    margin-top: 0;
    padding-top: 0;
    background: transparent;
    box-shadow: none;
    border-radius: 0;
}

#recommendView .song-item:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

#recommendView .song-item {
    border-left: none;
    border-right: none;
}

#recommendView .song-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

/* ==================== 搜索样式 ==================== */
.search-content {
    background: var(--secondary-bg);
    border-radius: 15px;
    padding: 25px;
    box-shadow: var(--shadow);
    /* 移除内部滚动 */
    max-height: none;
    overflow-y: visible;
}

.search-hot-words {
    margin-bottom: 30px;
}

.search-hot-words h3 {
    font-size: 18px;
    margin-bottom: 15px;
}

.hot-words {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.hot-word {
    padding: 8px 15px;
    background: var(--primary-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.hot-word:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.search-results {
    margin-top: 30px;
    /* 移除内部滚动 */
    max-height: none;
    overflow-y: visible;
    margin-bottom: 60px; /* 增加更多底部边距 */
}

/* 移除搜索结果的滚动条样式 */

/* ==================== 播放器控制栏样式 ==================== */
.player-bar {
    height: 80px;
    background: var(--player-bg);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 100;
    border-bottom-left-radius: 10px; /* 添加圆角 */
    border-bottom-right-radius: 10px; /* 添加圆角 */
    position: fixed; /* 固定位置 */
    bottom: 0; /* 固定在底部 */
    left: 50%; /* 居中 */
    transform: translateX(-50%); /* 水平居中 */
    width: 80vw; /* 与app容器宽度一致 */
    max-width: 1200px; /* 最大宽度与app容器一致 */
}

.player-info {
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 200px;
    max-width: 300px;
}

.player-info .song-cover {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.player-info .song-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.song-details {
    flex: 1;
    min-width: 0;
}

.song-details .song-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-details .song-artist {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-actions {
    display: flex;
    gap: 10px;
}

.btn-favorite {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-favorite:hover, .btn-favorite.active {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* ==================== 播放控件样式 ==================== */
.player-controls {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 500px;
}

.control-buttons {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.control-buttons button {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.control-buttons button:hover {
    color: var(--primary-color);
    background: var(--hover-bg);
    transform: scale(1.1);
}

.btn-play {
    width: 50px !important;
    height: 50px !important;
    background: var(--primary-color) !important;
    color: white !important;
    font-size: 18px !important;
}

.btn-play:hover {
    background: var(--secondary-color) !important;
    transform: scale(1.1) !important;
}

.btn-shuffle.active, .btn-repeat.active {
    color: var(--primary-color);
}

.progress-container {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
}

.time-current, .time-total {
    font-size: 12px;
    color: var(--text-muted);
    min-width: 35px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    overflow: visible;
}

.progress-track {
    position: relative;
    width: 100%;
    height: 100%;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s ease;
    min-width: 1px; /* 确保即使进度为0也有最小宽度 */
    box-shadow: 0 0 2px var(--primary-color);
}

/* 添加进度条动画效果 */
.progress-fill.playing {
    position: relative;
    overflow: hidden;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    background-size: 200% 100%;
    animation: progress-gradient 3s infinite linear;
    box-shadow: 0 0 5px var(--primary-color);
}

.progress-fill.playing::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    animation: progress-pulse 1.5s infinite;
}

.progress-fill.playing::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progress-shine 2s infinite;
}

.progress-handle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
    left: 0%;
    margin-left: -6px; /* 居中对齐 */
    box-shadow: 0 0 5px var(--primary-color);
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

.progress-bar:hover .progress-fill {
    height: 6px;
    margin-top: -1px;
}

@keyframes progress-gradient {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 200% 50%;
    }
}

@keyframes progress-shine {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes progress-pulse {
    0% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.7;
    }
}

/* ==================== 音量控制样式 ==================== */
.player-volume {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 150px;
}

.btn-volume, .btn-playlist, .btn-fullscreen {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-volume:hover, .btn-playlist:hover, .btn-fullscreen:hover {
    color: var(--primary-color);
    background: var(--hover-bg);
}

.volume-slider {
    width: 80px;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    cursor: pointer;
    position: relative;
}

.volume-track {
    position: relative;
    width: 100%;
    height: 100%;
}

.volume-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
    width: 70%;
}

.volume-handle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    left: 70%;
}

/* ==================== 播放列表面板样式 ==================== */
.playlist-panel {
    position: fixed;
    top: 0;
    right: -350px;
    width: 350px;
    height: 100vh;
    background: var(--sidebar-bg);
    border-left: 1px solid var(--border-color);
    transition: right 0.3s ease;
    z-index: 1000;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.2);
}

.playlist-panel.active {
    right: 0;
}

.playlist-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.playlist-header h3 {
    font-size: 18px;
}

.btn-close-playlist {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close-playlist:hover {
    color: var(--primary-color);
    background: var(--hover-bg);
}

.playlist-content {
    padding: 15px;
    max-height: 500px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--text-muted) transparent;
}

.playlist-content::-webkit-scrollbar {
    width: 6px;
}

.playlist-content::-webkit-scrollbar-track {
    background: transparent;
}

.playlist-content::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 3px;
}

.playlist-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.btn-clear-playlist {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 15px;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.btn-clear-playlist:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.playlist-count {
    font-size: 12px;
    color: var(--text-muted);
}

.current-playlist {
    list-style: none;
}

.current-playlist .song-item {
    padding: 10px 20px;
    border-bottom: 1px solid var(--border-color);
}

/* ==================== 模态框样式 ==================== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--primary-bg);
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 18px;
}

.btn-close-modal {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close-modal:hover {
    color: var(--primary-color);
    background: var(--hover-bg);
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--text-muted) transparent;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: transparent;
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 3px;
}

/* 歌单选择样式 */
.playlist-select-list {
    max-height: none;
    overflow-y: visible;
    margin: 15px 0;
    border-radius: 10px;
    background: var(--secondary-bg);
}

.playlist-select-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.playlist-select-item:last-child {
    border-bottom: none;
}

.playlist-select-item:hover {
    background: var(--hover-bg);
}

.playlist-select-name {
    font-weight: 500;
    color: var(--text-primary);
    max-width: 70%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.playlist-select-count {
    font-size: 12px;
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.1);
    padding: 3px 10px;
    border-radius: 12px;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
}

.modal h3 {
    text-align: center;
    padding: 15px 0;
    font-size: 18px;
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--text-secondary);
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--secondary-bg);
    color: var(--text-primary);
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus {
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.modal-footer {
    display: flex;
    gap: 15px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    justify-content: flex-end;
}

.btn-cancel, .btn-confirm {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-cancel {
    background: var(--secondary-bg);
    color: var(--text-secondary);
}

.btn-cancel:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.btn-confirm {
    background: var(--primary-color);
    color: white;
}

.btn-confirm:hover {
    background: var(--secondary-color);
}

/* ==================== 右键菜单样式 ==================== */
.context-menu {
    position: absolute; /* 使用absolute而不是fixed，相对于app定位 */
    background: var(--primary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    z-index: 3000;
    min-width: 180px;
    visibility: hidden;
    display: none;
    overflow: hidden;
    backdrop-filter: blur(10px);
    padding: 5px 0;
}

.context-menu.active {
    display: block;
    animation: contextMenuSlideIn 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes contextMenuSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.context-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.context-menu li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.context-menu li:hover {
    background: var(--hover-bg);
    color: var(--primary-color);
}

.context-menu li:hover::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: var(--primary-color);
}

.context-menu li i {
    width: 18px;
    text-align: center;
    color: var(--text-secondary);
    font-size: 16px;
}

.context-menu li:hover i {
    color: var(--primary-color);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1200px) {
    .app {
        width: 90vw;
        height: 85vh;
    }
}

@media (max-width: 992px) {
    .song-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
    
    .playlist-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
}

@media (max-width: 768px) {
    .app {
        width: 95vw;
        height: 90vh;
    }
    
    .sidebar {
        width: 180px;
        min-width: 180px;
    }
    
    .song-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .playlist-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
    
    .player-info {
        min-width: 150px;
    }
    
    .player-volume {
        min-width: 120px;
    }
}

@media (max-width: 576px) {
    .app {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }
    
    .sidebar {
        display: none;
    }
    
    .song-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .playlist-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .player-info {
        min-width: 120px;
    }
    
    .player-volume {
        display: none;
    }
}

/* ==================== 滚动条样式 ==================== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* ==================== 动画和过渡效果 ==================== */
.song-card, .playlist-card, .song-item {
    animation: slideInUp 0.3s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-btn, .control-buttons button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ==================== 特殊效果 ==================== */
[data-theme="neon"] .song-card:hover,
[data-theme="neon"] .playlist-card:hover {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
}

[data-theme="gradient"] .sidebar {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

[data-theme="gradient"] .player-bar {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
}

/* ==================== Toast提示样式 ==================== */
#toast {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: #fff;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

#toast.show {
    opacity: 1;
    visibility: visible;
    animation: toastIn 0.3s ease forwards;
}

@keyframes toastIn {
    0% {
        transform: translate(-50%, 20px);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

/* ==================== 加载动画 ==================== */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    text-align: center;
}

.loading-container p {
    margin-top: 15px;
    color: var(--text-secondary);
    font-size: 14px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 空状态样式 ==================== */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    color: var(--text-muted);
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--text-secondary);
}

.empty-state p {
    font-size: 14px;
    line-height: 1.5;
}

/* 主题相关的滚动条样式 */
[data-theme="light"] .sidebar::-webkit-scrollbar-thumb,
[data-theme="light"] .playlists-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2); /* 浅色主题使用暗色滚动条 */
}

[data-theme="light"] .sidebar::-webkit-scrollbar-thumb:hover,
[data-theme="light"] .playlists-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3); /* 浅色主题悬停效果 */
}

[data-theme="neon"] .sidebar::-webkit-scrollbar-thumb,
[data-theme="neon"] .playlists-list::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 136, 0.2); /* 霓虹主题使用霓虹色滚动条 */
}

[data-theme="neon"] .sidebar::-webkit-scrollbar-thumb:hover,
[data-theme="neon"] .playlists-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 136, 0.3); /* 霓虹主题悬停效果 */
}

[data-theme="gradient"] .sidebar::-webkit-scrollbar-thumb,
[data-theme="gradient"] .playlists-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3); /* 渐变主题使用更明显的白色 */
}

[data-theme="gradient"] .sidebar::-webkit-scrollbar-thumb:hover,
[data-theme="gradient"] .playlists-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4); /* 渐变主题悬停效果 */
}

/* 歌单项样式 */
.playlist-item {
    display: flex;
    align-items: center;
    gap: 8px; /* 减小间距 */
    padding: 6px 10px; /* 减小内边距 */
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin-bottom: 1px; /* 减小外边距 */
}

.playlist-item:hover {
    background: var(--hover-bg);
}

.playlist-item.active {
    background: var(--primary-color);
    color: white;
}

.playlist-item i {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.playlist-item-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
}

/* 添加错误和加载状态样式 */
.error-text {
    color: #ff4d4d !important;
}

.loading-text {
    color: #ffaa33 !important;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* 添加错误状态相关样式 */
.error-state {
    color: #ff5252 !important;
    position: relative;
}

.error-state::after {
    content: "⚠️";
    font-size: 0.8em;
    margin-left: 5px;
    opacity: 0.7;
}

/* 即使在错误状态下也保持进度条动画 */
.progress-fill.playing {
    animation: progress-pulse 2s infinite;
}

@keyframes progress-pulse {
    0% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.8;
    }
}

/* 错误状态下的封面样式 */
.error-cover {
    opacity: 0.7;
    filter: grayscale(30%);
    border: 1px solid #ff5252;
}

/* 确保错误状态下仍然可以操作控制按钮 */
.player-controls button {
    position: relative;
    z-index: 10;
}
