-- 音乐盒物品定义

-- 检测服务器框架
local ESX = nil
local QBCore = nil
local frameworkInitialized = true

-- 添加对音乐盒相关变量的引用
local playerRadios = {}
local radios = {}

-- 确保Config存在，如果不存在则创建默认值
if not Config then
    Config = {}
    print("^1[警告]^7 Config对象未初始化，创建空配置")
end

-- 不再在此处检查RadioItem配置，使用server/radio_server.lua中已确定的值
if Config.RadioItem then
    print("^2[音乐盒系统]^7 读取到配置的物品代码: " .. Config.RadioItem)
else
    print("^1[警告]^7 严重错误：RadioItem配置丢失")
end

-- 在资源启动时获取音乐盒数据
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- 等待1秒确保radio_server.lua已加载
        Citizen.Wait(1000)
        -- 导出变量
        TriggerEvent('music:getRadioData', function(radioData, playerRadioData)
            radios = radioData
            playerRadios = playerRadioData
        end)
    end
end)

-- 初始化ESX
local function InitESX()
    -- 为QB框架添加兼容处理
    if GetResourceState('es_extended') ~= 'started' then
        return false
    end
    
    -- 安全地尝试获取ESX
    local success = pcall(function()
        ESX = exports["es_extended"]:getSharedObject()
    end)
    
    if not success or not ESX then
        -- 回退到事件方法
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    end
    
    if ESX then
        -- 获取物品名称，如果配置不存在则使用默认值
        local radioItemName = Config.RadioItem
        
        if not radioItemName then
            print("^1[音乐盒系统]^7 错误：未设置RadioItem配置，无法注册ESX物品")
            return
        end
        
        -- 注册物品
        ESX.RegisterUsableItem(radioItemName, function(source)
            -- 从背包中移除物品
            local xPlayer = ESX.GetPlayerFromId(source)
            if xPlayer then
                xPlayer.removeInventoryItem(radioItemName, 1)
            end
            
            -- 直接触发客户端放置事件
            TriggerClientEvent('radio:placeRadio', source)
        end)
        
        -- print("^2[音乐盒系统]^7 ESX框架检测成功，使用物品代码: " .. radioItemName)
        frameworkInitialized = true
        return true
    end
    return false
end

-- 初始化QBCore（使用ox_inventory方式）
local function InitQBCore()
    -- 检查QB框架是否可用
    if GetResourceState('qb-core') ~= 'started' then
        return false
    end

    -- 检查是否使用ox_inventory
    local useOxInventory = GetResourceState('ox_inventory') == 'started'

    if useOxInventory then
        print("^2[音乐盒系统]^7 检测到ox_inventory，使用ox_inventory物品机制")

        -- 使用ox_inventory的物品注册方式
        local radioItemName = Config.RadioItem
        if not radioItemName then
            print("^1[音乐盒系统]^7 错误：未设置RadioItem配置")
            return false
        end

        -- 注册ox_inventory物品使用回调
        -- 方法1: 使用exports注册
        pcall(function()
            exports.ox_inventory:registerHook('useItem', function(playerId, itemName, slotId, metadata)
                if itemName == radioItemName then
                    print("^2[音乐盒系统]^7 玩家 " .. GetPlayerName(playerId) .. " 使用了音乐盒物品 (ox_inventory hook)")

                    -- 移除物品
                    local success = exports.ox_inventory:RemoveItem(playerId, radioItemName, 1, metadata, slotId)
                    if success then
                        -- 触发客户端放置事件
                        TriggerClientEvent('radio:placeRadio', playerId)
                        print("^2[音乐盒系统]^7 物品使用成功，已触发放置事件")
                    else
                        print("^1[音乐盒系统]^7 移除物品失败")
                    end

                    return false -- 阻止默认行为
                end
            end, {
                itemFilter = {
                    [radioItemName] = true
                }
            })
        end)

        -- 方法2: 使用事件监听（备用方案）
        RegisterServerEvent('ox_inventory:useItem')
        AddEventHandler('ox_inventory:useItem', function(source, itemName, slotId)
            if itemName == radioItemName then
                print("^2[音乐盒系统]^7 玩家 " .. GetPlayerName(source) .. " 使用了音乐盒物品 (ox_inventory event)")

                -- 移除物品
                local success = exports.ox_inventory:RemoveItem(source, radioItemName, 1, nil, slotId)
                if success then
                    -- 触发客户端放置事件
                    TriggerClientEvent('radio:placeRadio', source)
                    print("^2[音乐盒系统]^7 物品使用成功，已触发放置事件")
                else
                    print("^1[音乐盒系统]^7 移除物品失败")
                end
            end
        end)

        -- 方法3: 直接注册物品使用函数（最常用的方法）
        exports('useItem', function(event, item, inventory, slot, data)
            if event == 'usingItem' and item.name == radioItemName then
                local playerId = inventory.id
                print("^2[音乐盒系统]^7 玩家 " .. GetPlayerName(playerId) .. " 使用了音乐盒物品 (ox_inventory export)")

                -- 移除物品
                local success = exports.ox_inventory:RemoveItem(playerId, radioItemName, 1, data, slot)
                if success then
                    -- 触发客户端放置事件
                    TriggerClientEvent('radio:placeRadio', playerId)
                    print("^2[音乐盒系统]^7 物品使用成功，已触发放置事件")
                else
                    print("^1[音乐盒系统]^7 移除物品失败")
                end

                return false -- 阻止默认行为
            end
        end)

        print("^2[音乐盒系统]^7 ox_inventory物品注册成功: " .. radioItemName)
        frameworkInitialized = true
        return true
    else
        -- 回退到传统QB-Core方式
        local success = pcall(function()
            QBCore = exports['qb-core']:GetCoreObject()
        end)

        if not success or not QBCore then
            TriggerEvent('QBCore:GetObject', function(obj) QBCore = obj end)
            Citizen.Wait(200)
        end

        if QBCore then
            local radioItemName = Config.RadioItem
            if not radioItemName then
                print("^1[音乐盒系统]^7 错误：未设置RadioItem配置，无法注册QBCore物品")
                return false
            end

            -- 使用传统QB-Core物品注册
            QBCore.Functions.CreateUseableItem(radioItemName, function(source, item)
                print("^2[音乐盒系统]^7 玩家 " .. GetPlayerName(source) .. " 使用了音乐盒物品 (QB-Core)")
                TriggerClientEvent('radio:placeRadio', source)
            end)

            print("^2[音乐盒系统]^7 QB-Core物品注册成功: " .. radioItemName)
            frameworkInitialized = true
            return true
        end
    end

    return false
end

-- 事件监听器，用于初始化物品
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- 延迟初始化，确保其他资源已加载
        Citizen.SetTimeout(2000, function()
            print("^3[音乐盒系统]^7 开始初始化物品注册...")

            -- 尝试初始化QBCore（先检查QBCore框架）
            if not InitQBCore() then
                -- 如果QBCore初始化失败，尝试回退到ESX
                if not InitESX() then
                    -- 如果两个框架都没有检测到，提供独立命令
                    print("^3[音乐盒系统]^7 未检测到支持的框架，使用独立命令 /placeradio")

                    -- 注册指令处理程序（作为后备方案）
                    RegisterCommand("giveradio", function(source, args, rawCommand)
                        local playerId = tonumber(args[1]) or source
                        TriggerClientEvent('chatMessage', playerId, '[系统]', {255, 0, 0}, '您收到了一个音乐盒，请使用 /placeradio 放置它。')
                        -- 这里您可以添加物品到玩家背包的逻辑，如果有自定义背包系统
                    end, true) -- 设置为true表示仅限管理员使用
                end
            end
        end)
    end
end)

-- 添加物品定义（根据您使用的背包系统）

-- 如果使用 OX_Inventory，将以下内容添加到 ox_inventory/data/items.lua:
--[[
["yinyue"] = {
    label = "音乐盒",
    weight = 2000,
    stack = false,
    close = true,
    description = "一个便携式音乐盒，可以播放音乐",
    client = {
        image = "radio_player.png",
    }
},
]]

-- 如果使用 QB-Inventory 或需要QB-Core物品定义，将以下内容添加到 qb-core/shared/items.lua:
--[[
["yinyue"] = {
    ["name"] = "yinyue",
    ["label"] = "音乐盒",
    ["weight"] = 2000,
    ["type"] = "item",
    ["image"] = "radio_player.png",
    ["unique"] = false,
    ["useable"] = true,
    ["shouldClose"] = true,
    ["combinable"] = nil,
    ["description"] = "一个便携式音乐盒，可以播放音乐"
},
]]

-- 添加ESX物品定义（如果使用ESX）
-- 使用以下SQL运行:
--[[
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
(Config.RadioItem, '音乐盒', 2, 0, 1);
]]

-- 当玩家断开连接时，清理他们的记录
AddEventHandler('playerDropped', function()
    local source = source
    
    -- 检查玩家是否有放置的音乐盒
    if playerRadios[source] then
        local radioId = playerRadios[source]
        
        -- 如果音乐盒存在，删除它
        if radios[radioId] then
            -- 通知所有客户端删除音乐盒模型
            TriggerClientEvent("radio:removeRadio", -1, radioId)
            
            -- 从服务器列表中删除
            radios[radioId] = nil
            
            -- print("^2[音乐盒系统] ^7玩家离线，自动删除音乐盒，ID: " .. radioId)
        end
        
        -- 从记录中移除玩家
        playerRadios[source] = nil
    end
end) 