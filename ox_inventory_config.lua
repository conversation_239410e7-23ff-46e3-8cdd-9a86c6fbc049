-- OX_Inventory 音乐盒物品配置
-- 将此内容添加到 ox_inventory/data/items.lua 文件中

-- 在 items 表中添加以下内容：
["yinyue"] = {
    label = "音乐盒",
    weight = 2000,
    stack = false,
    close = true,
    description = "一个便携式音乐盒，可以播放音乐",
    consume = 1, -- 使用时消耗1个
    server = {
        export = 'music_tudou.useItem', -- 指向我们的资源导出函数
    },
    client = {
        image = "radio_player.png",
        usetime = 2500, -- 使用时间（毫秒）
        cancel = true, -- 可以取消使用
        anim = {
            dict = 'pickup_object',
            clip = 'putdown_low'
        },
    }
},


