-- 调试开关
local DEBUG = true

-- 调试日志函数
local function debugPrint(...)
    -- 完全禁用调试输出
    return
end

local musicPlayerOpen = false
local currentSong = nil
local supportedPlatforms = {}
local currentPlatform = "QQ" -- 默认使用QQ音乐
local isPlaying = false -- 是否正在播放

-- 一次性事件处理函数，避免RemoveEventHandler错误
local function createOneTimeEventHandler(eventName, callback, timeout)
    local handler = nil
    local isDone = false
    local eventHandled = false
    
    -- 注册事件处理程序
    handler = AddEventHandler(eventName, function(...)
        if not isDone then
            isDone = true
            eventHandled = true
            
            -- 执行回调
            callback(...)
            
            -- 安全移除事件处理程序
            if handler then
                RemoveEventHandler(handler)
                handler = nil
            end
        end
    end)
    
    -- 设置超时
    if timeout and timeout > 0 then
        Citizen.SetTimeout(timeout, function()
            if not eventHandled and not isDone then
                isDone = true
                
                -- 执行超时回调
                callback(nil, true) -- 第二个参数表示超时
                
                -- 安全移除事件处理程序
                if handler then
                    RemoveEventHandler(handler)
                    handler = nil
                end
            end
        end)
    end
    
    return handler
end

-- 注册命令打开音乐播放器
RegisterCommand('plsmusic', function()
    -- 切换UI显示状态
    musicPlayerOpen = not musicPlayerOpen
    
    if musicPlayerOpen then
        -- 显示UI
        SetNuiFocus(true, true)
        SendNUIMessage({
            action = "show"
        })
        debugPrint("音乐播放器已打开")
    else
        -- 隐藏UI
        SetNuiFocus(false, false)
        SendNUIMessage({
            action = "hide"
        })
        debugPrint("音乐播放器已关闭")
    end
end, false)

-- 从音乐盒打开音乐播放器事件处理
RegisterNetEvent("music:openFromRadio")
AddEventHandler("music:openFromRadio", function()
    -- 显示UI
    musicPlayerOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "show"
    })
    debugPrint("从音乐盒打开音乐播放器")
end)

-- 处理音乐盒播放状态更新事件
RegisterNetEvent("music:updatePlaybackState")
AddEventHandler("music:updatePlaybackState", function(playing, currentTime)
    -- 更新本地播放状态
    isPlaying = playing

    -- 如果有当前歌曲，更新其状态
    if currentSong then
        if playing then
            -- 恢复播放：重新计算开始时间
            currentSong.startTime = GetGameTimer() - (currentTime * 1000)
            currentSong.pausedAt = nil
        else
            -- 暂停播放：记录暂停时间和位置
            currentSong.pausedAt = GetGameTimer()
            currentSong.pausedPosition = currentTime
        end
    end

    -- 无论UI是否打开都发送状态更新
    SendNUIMessage({
        action = playing and "resumePlayback" or "pausePlayback",
        currentTime = currentTime,
        isPlaying = playing,
        source = "radio_sync"
    })
end)

-- 检查玩家是否在车内且是驾驶员
local function isPlayerDriving()
    local ped = PlayerPedId()
    
    -- 检查玩家是否在车内
    if not IsPedInAnyVehicle(ped, false) then
        return false
    end
    
    -- 获取玩家所在的车辆
    local vehicle = GetVehiclePedIsIn(ped, false)
    if not DoesEntityExist(vehicle) then
        return false
    end
    
    -- 检查玩家是否是驾驶员（座位索引0是驾驶员）
    return GetPedInVehicleSeat(vehicle, -1) == ped
end

-- 车内音乐控制系统
Citizen.CreateThread(function()
    local lastKeyPressed = 0
    
    while true do
        Citizen.Wait(0)
        
        -- 检查玩家是否在驾驶位置
        if isPlayerDriving() then
            -- 显示提示
            DisplayHelpTextThisFrame("按 ~INPUT_CELLPHONE_PLUS~ 打开音乐播放器", false)
            
            -- 确保Config存在，如果不存在则使用默认按键
            local openKey = 107 -- 默认使用小键盘加号键
            if Config and Config.OpenMusicUIKey then
                openKey = Config.OpenMusicUIKey
            end
            
            -- 检测配置的按键是否被按下
            if IsControlJustPressed(0, openKey) then -- 使用配置文件中的按键设置
                -- 防止重复触发（添加200ms冷却时间）
                local currentTime = GetGameTimer()
                if currentTime - lastKeyPressed > 200 then
                    lastKeyPressed = currentTime
                    
                    -- 切换UI显示状态
                    musicPlayerOpen = not musicPlayerOpen
                    
                    if musicPlayerOpen then
                        -- 显示UI
                        SetNuiFocus(true, true)
                        SendNUIMessage({
                            action = "show"
                        })
                        debugPrint("车内音乐播放器已打开")
                    else
                        -- 隐藏UI
                        SetNuiFocus(false, false)
                        SendNUIMessage({
                            action = "hide"
                        })
                        debugPrint("车内音乐播放器已关闭")
                    end
                end
            end
        end
    end
end)

-- 初始化
Citizen.CreateThread(function()
    -- 初始化时确保UI是关闭的
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = "hide"
    })
    
    -- 获取支持的平台列表
    TriggerServerEvent('music:getSupportedPlatforms')
    
    -- 预加载热门歌曲数据
    Citizen.Wait(1000) -- 等待1秒确保UI初始化完成
    SendNUIMessage({
        action = "preloadData",
        load = true
    })
    
    -- 注册玩家信息到数据库
    Citizen.Wait(1000) -- 再等待1秒
    TriggerServerEvent('music:registerPlayer')
end)

-- 播放音乐
RegisterNUICallback('playSong', function(data, cb)
    local songId = data.songId
    local url = data.url
    local title = data.title or "未知歌曲"
    local artist = data.artist or "未知歌手"
    local duration = tonumber(data.duration) or 180 -- 确保duration是数字，默认3分钟
    
    if not url or url == "" then
        cb({status = "error", message = "歌曲URL为空"})
        return
    end
    
    -- 确保URL使用HTTPS
    url = string.gsub(url, "^http:", "https:")
    
    -- 停止之前的歌曲
    if currentSong then
        -- 通知服务器停止音乐
        TriggerServerEvent("xsound:stopMusic")
        
        -- 确保所有音频停止
        local myServerId = GetPlayerServerId(PlayerId())
        if myServerId then
            local audioId = "music_" .. myServerId
            if exports["xsound"]:soundExists(audioId) then
                exports["xsound"]:Destroy(audioId)
            end
        end
        
        -- 等待一小段时间确保音频完全停止
        Citizen.Wait(100)
        
        -- 重置状态
        currentSong = nil
        isPlaying = false
    end
    
    -- 通知服务器播放音乐，让所有玩家（包括自己）都能听到
    TriggerServerEvent("xsound:playMusic", url, 1.0)
    
    -- 记录当前歌曲信息
    currentSong = {
        id = songId,
        title = title,
        artist = artist,
        url = url,
        duration = duration, -- 已确保是数字
        startTime = GetGameTimer() -- 记录开始播放的时间
    }
    
    isPlaying = true
    cb({status = "success"})
end)

-- 服务端播放回调（当浏览器播放失败时使用）
RegisterNUICallback('playServerSide', function(data, cb)
    local songId = data.songId
    local url = data.url
    local title = data.title or "未知歌曲"
    local artist = data.artist or "未知歌手"
    local originalPlatform = data.platform
    
    -- 智能平台检测（基于歌曲ID）
    local detectedPlatform = "QQ" -- 默认
    if songId then
        local songIdStr = tostring(songId)
        if string.find(songIdStr, "XMLY") then
            detectedPlatform = "XMLY"
        elseif string.match(songIdStr, "^%d+$") and string.len(songIdStr) >= 6 and string.len(songIdStr) <= 8 then
            detectedPlatform = "NETEASE"
        else
            detectedPlatform = "QQ"
        end
    end
    
    -- 使用检测到的平台，而不是前端提供的平台
    local platform = detectedPlatform
    
    -- 禁用客户端调试日志
    -- print("^3[客户端调试] ^7接收到前端数据:")
    -- print("^3[客户端调试] ^7  songId: " .. tostring(songId))
    -- print("^3[客户端调试] ^7  title: " .. tostring(title))
    -- print("^3[客户端调试] ^7  artist: " .. tostring(artist))
    -- print("^3[客户端调试] ^7  原始平台: " .. tostring(originalPlatform))
    -- print("^3[客户端调试] ^7  检测平台: " .. tostring(detectedPlatform))
    -- print("^3[客户端调试] ^7  最终平台: " .. tostring(platform))
    -- print("^3[客户端调试] ^7  mid: " .. tostring(data.mid))
    -- print("^3[客户端调试] ^7  url: " .. tostring(url))
    
    -- 如果是QQ音乐且前端没有传递mid字段，尝试从URL中提取
    local mid = data.mid
    if platform == "QQ" and (not mid or mid == "") then
        if url and string.find(url, "mid=") then
            mid = string.match(url, "mid=([^&]+)")
            if mid then
                -- 禁用客户端调试日志
                -- print("^2[客户端修复] ^7从URL中提取到mid字段: " .. mid)
            end
        end
    elseif platform ~= "QQ" then
        -- 非QQ音乐不需要mid字段
        mid = nil
    end
    
    -- 处理歌曲ID格式
    if songId then
        -- 检查ID是否需要预处理
        if tonumber(songId) and platform:upper() == "QQ" then
            -- QQ音乐数字ID可能需要转换为完整ID格式
            debugPrint("^3[音乐播放器]^7 检测到QQ音乐数字ID: " .. songId)
        end
    end
    
    -- 优先使用maxDuration或durationInSeconds，这些是已经转换为秒数的字段
    local duration = tonumber(data.maxDuration) or tonumber(data.durationInSeconds) or tonumber(data.duration) or 180
    
    -- 确保duration是合理的值（至少30秒，避免歌曲太短）
    if duration < 30 then duration = 180 end
    
    local startTime = tonumber(data.startTime) or 0 -- 新增：指定开始的时间点
    
    if not url or url == "" then
        cb({status = "error", message = "歌曲URL为空"})
        return
    end
    
    -- 确保URL使用HTTPS
    url = string.gsub(url, "^http:", "https:")
    
    -- 检查是否是恢复播放
    local isResume = false
    if currentSong and currentSong.url == url and startTime > 0 then
        isResume = true
    end
    
    -- 只有在不是恢复播放时才停止当前歌曲
    if not isResume then
    -- 停止之前的歌曲
    if currentSong then
        -- 通知服务器停止音乐
        TriggerServerEvent("xsound:stopMusic")
        
        -- 确保所有音频停止
        local myServerId = GetPlayerServerId(PlayerId())
        if myServerId then
            local audioId = "music_" .. myServerId
            if exports["xsound"]:soundExists(audioId) then
                exports["xsound"]:Destroy(audioId)
            end
        end
        
        -- 等待一小段时间确保音频完全停止
        Citizen.Wait(100)
        
        -- 重置状态
        currentSong = nil
        isPlaying = false
        end
    end
    
    -- 记录当前歌曲信息以供调试
    debugPrint("^3[音乐播放器]^7 客户端播放歌曲: " .. title .. " - " .. artist)
    debugPrint("^3[音乐播放器]^7 歌曲ID: " .. (songId or "未知"))
    debugPrint("^3[音乐播放器]^7 歌曲平台: " .. platform)
    debugPrint("^3[音乐播放器]^7 歌曲URL: " .. url)
    if data.mid then
        debugPrint("^3[音乐播放器]^7 歌曲MID: " .. data.mid)
    end
    
    -- 通知服务器播放音乐，让所有玩家（包括自己）都能听到
    -- 使用服务端直接播放模式，传递完整的歌曲数据对象（包含mid字段）
    local songData = {
        id = songId,
        mid = mid, -- 使用提取到的mid字段
        title = title,
        name = title, -- 别名字段
        artist = artist,
        singer = artist, -- 别名字段
        platform = platform,
        url = url,
        duration = duration,
        pic = data.pic or data.picUrl or data.cover,
        album = data.album,
        search_keyword = data.search_keyword
    }
    
    -- 禁用客户端调试日志
    -- print("^3[客户端调试] ^7构建的songData:")
    -- print("^3[客户端调试] ^7  mid: " .. tostring(songData.mid))
    
    TriggerServerEvent("xsound:playMusicFromServer", url, 1.0, startTime, songData)
    
    -- 记录当前歌曲信息
    currentSong = {
        id = songId,
        title = title,
        artist = artist,
        url = url,
        platform = platform,
        duration = duration, -- 已确保是数字
        startTime = GetGameTimer() - (startTime * 1000) -- 根据指定的开始时间调整开始播放的时间
    }
    
    isPlaying = true
    cb({status = "success"})
end)

-- 暂停播放音乐
RegisterNUICallback('pauseSong', function(data, cb)
    if currentSong and isPlaying then
        -- 获取当前播放位置
        local currentTime = 0
        local myServerId = GetPlayerServerId(PlayerId())
        if myServerId then
            local audioId = "music_" .. myServerId
            if exports["xsound"]:soundExists(audioId) then
                -- 获取当前播放位置
                currentTime = exports["xsound"]:getTimeStamp(audioId) or 0
                -- 暂停音频
                exports["xsound"]:Pause(audioId)
            else
                -- 如果音频对象不存在，尝试从当前歌曲信息中计算播放位置
                if currentSong and currentSong.startTime then
                    local elapsedMs = GetGameTimer() - currentSong.startTime
                    currentTime = elapsedMs / 1000
                end
            end
        end
        
        -- 确保位置值有效
        if currentTime < 0 then currentTime = 0 end
        
        -- 记录暂停时间和位置
        if currentSong then
            currentSong.pausedAt = GetGameTimer()
            currentSong.pausedPosition = currentTime
            isPlaying = false
        end
        
        -- 通知服务器暂停音乐
        TriggerServerEvent("xsound:pauseMusic")
    end
    
    cb({status = "success"})
end)

-- 恢复播放音乐
RegisterNUICallback('resumeSong', function(data, cb)
    -- 检查是否有当前歌曲
    if not currentSong then
        cb({status = "error", message = "没有当前歌曲"})
        return
    end
    
    -- 优先使用保存的位置，如果没有则使用传入的位置
    local seekTime = currentSong.pausedPosition
    if not seekTime or seekTime <= 0 then
        seekTime = tonumber(data.seekTime) or 0
    end
    
    -- 确保seekTime是有效值
    if seekTime < 0 then seekTime = 0 end
    
    -- 恢复自己的音频播放
    local myServerId = GetPlayerServerId(PlayerId())
    if myServerId then
        local audioId = "music_" .. myServerId
        if exports["xsound"]:soundExists(audioId) then
            -- 如果有指定的时间点，先设置位置
            if seekTime and seekTime > 0 then
                exports["xsound"]:setTimeStamp(audioId, seekTime)
            end
            
            -- 恢复播放
            exports["xsound"]:Resume(audioId)
        else
            -- 如果音频对象不存在，需要重新创建
            
            -- 检查URL是否有效
            if not currentSong.url or currentSong.url == "" then
                cb({status = "error", message = "无效的URL"})
                return
            end
            
            -- 直接创建音频对象
            local sourcePed = PlayerPedId()
            exports["xsound"]:PlayUrlPos(audioId, currentSong.url, 1.0, GetEntityCoords(sourcePed), false)
            
            -- 设置播放位置
            if seekTime and seekTime > 0 then
                Citizen.Wait(100) -- 等待一小段时间确保音频对象创建成功
                exports["xsound"]:setTimeStamp(audioId, seekTime)
            end
            
            -- 设置距离
            exports["xsound"]:Distance(audioId, 30.0)
        end
    end
    
    -- 更新当前歌曲的开始时间，以便正确计算播放进度
    -- 根据当前时间和播放位置重新计算开始时间
    currentSong.startTime = GetGameTimer() - (seekTime * 1000)
    
    -- 通知服务器恢复音乐播放
    TriggerServerEvent("xsound:resumeMusic", seekTime)
    
    -- 更新暂停状态
    isPlaying = true
    currentSong.pausedAt = nil
    
    cb({status = "success"})
end)

-- 停止播放音乐
RegisterNUICallback('stopSong', function(data, cb)
    if currentSong then
        -- 通知服务器停止音乐
        TriggerServerEvent("xsound:stopMusic")
        currentSong = nil
        isPlaying = false
    end
    
    cb({status = "success"})
end)

-- 关闭UI的回调
RegisterNUICallback('close', function(data, cb)
    musicPlayerOpen = false
    SetNuiFocus(false, false)
    debugPrint("音乐播放器已关闭")
    cb('ok')
end)

-- 客户端处理来自其他玩家的暂停音乐请求
RegisterNetEvent("xsound:pauseMusicAtClient")
AddEventHandler("xsound:pauseMusicAtClient", function(sourcePlayerId)
    local myServerId = GetPlayerServerId(PlayerId())
    if myServerId == sourcePlayerId then
        return -- 跳过，因为自己已经处理了暂停
    end
    
    -- 暂停播放，但不销毁音频对象
    local audioId = "music_" .. sourcePlayerId
    if exports["xsound"]:soundExists(audioId) then
        -- 暂停音频并保存当前位置
        exports["xsound"]:Pause(audioId)
    end
end)

-- 客户端处理来自其他玩家的恢复音乐请求
RegisterNetEvent("xsound:resumeMusicAtClient")
AddEventHandler("xsound:resumeMusicAtClient", function(sourcePlayerId, seekTime)
    local myServerId = GetPlayerServerId(PlayerId())
    if myServerId == sourcePlayerId then
        return -- 跳过，因为自己已经处理了恢复
    end
    
    -- 恢复播放
    local audioId = "music_" .. sourcePlayerId
    if exports["xsound"]:soundExists(audioId) then
        -- 如果有指定的时间点，先设置位置
        if seekTime and seekTime > 0 then
            exports["xsound"]:setTimeStamp(audioId, seekTime)
        end
        
        -- 恢复播放
        exports["xsound"]:Resume(audioId)
    else
        -- 如果音频对象不存在，可能需要重新创建
    end
end)

-- 搜索音乐的回调
RegisterNUICallback('searchMusic', function(data, cb)
    local keyword = data.keyword
    local platform = data.platform or currentPlatform
    local limit = data.limit or 20
    
    if keyword and keyword ~= "" then
        debugPrint("搜索音乐: " .. keyword .. " (平台: " .. platform .. ", 最大结果数: " .. limit .. ")")
        -- 确保平台信息正确传递给服务器
        TriggerServerEvent('music:searchMusic', platform, keyword, limit)
        cb({status = "searching"})
    else
        cb({status = "error", message = "搜索关键词不能为空"})
    end
end)

-- 切换平台的回调
RegisterNUICallback('switchPlatform', function(data, cb)
    local platform = data.platform
    
    if platform then
        -- 保存当前平台，确保大写
        currentPlatform = platform:upper()
        debugPrint("切换到平台: " .. currentPlatform)
        
        -- 通知服务器平台已切换
        TriggerServerEvent('music:switchPlatform', currentPlatform)
        
        cb({status = "success"})
    else
        cb({status = "error", message = "无效的平台"})
    end
end)

-- 获取支持的平台列表的回调
RegisterNUICallback('getSupportedPlatforms', function(data, cb)
    -- 如果还没有从服务器获取到平台列表，提供默认列表
    local platforms = supportedPlatforms
    if not platforms or #platforms == 0 then
        platforms = {
            { code = "QQ", name = "QQ音乐" },
            { code = "NETEASE", name = "网易云音乐" },
            { code = "KUGOU", name = "酷狗音乐" },
            { code = "KUWO", name = "酷我音乐" },
            { code = "XMLY", name = "喜马拉雅" }
        }
        debugPrint("^3[音乐播放器] ^7使用默认平台列表，因为服务器列表尚未加载")
    end

    cb({
        status = "success",
        platforms = platforms
    })
end)

-- 接收搜索结果
RegisterNetEvent('music:searchResult')
AddEventHandler('music:searchResult', function(result)
    debugPrint("收到搜索结果，发送到UI")
    
    -- 将结果包装成前端期望的格式
    local formattedResult = {
        code = 200,
        msg = "成功",
        data = result
    }
    
    if result then
        if type(result) == "table" then
            debugPrint("结果数据条数: " .. #result)
            
            -- 处理可能的编码问题
            for i, song in ipairs(result) do
                -- 确保所有字符串字段都存在，避免nil值
                if not song.name then song.name = "未知歌曲" end
                if not song.singer then song.singer = "未知歌手" end
                if not song.album then song.album = "未知专辑" end
            end
        else
            debugPrint("结果不是数组类型")
            formattedResult.code = 500
            formattedResult.msg = "数据格式错误"
            formattedResult.data = nil
        end
    else
        debugPrint("结果为空")
        formattedResult.code = 404
        formattedResult.msg = "未找到结果"
        formattedResult.data = nil
    end
    
    SendNUIMessage({
        action = "searchResult",
        result = formattedResult
    })
end)

-- 接收支持的平台列表
RegisterNetEvent('music:supportedPlatforms')
AddEventHandler('music:supportedPlatforms', function(platforms)
    -- 确保platforms是一个表格
    if type(platforms) ~= "table" then
        debugPrint("^1[音乐播放器] ^7接收到的平台列表不是有效的表格")
        platforms = {}
    end
    
    -- 如果平台列表为空，使用默认平台列表
    if #platforms == 0 then
        debugPrint("^3[音乐播放器] ^7接收到的平台列表为空，使用默认平台列表")
        platforms = {
            {code = "QQ", name = "QQ音乐"},
            {code = "NETEASE", name = "网易云音乐"},
            {code = "KUGOU", name = "酷狗音乐"},
            {code = "KUWO", name = "酷我音乐"},
            {code = "XMLY", name = "喜马拉雅"},
            {code = "QQ_RADIO", name = "蜻蜓FM"}
        }
    end
    
    debugPrint("^2[音乐播放器] ^7已接收平台列表: " .. #platforms .. "个平台")
    supportedPlatforms = platforms
    
    -- 将平台列表发送到UI
        SendNUIMessage({
            action = "supportedPlatforms",
            platforms = platforms
        })
end)

-- 接收缓存统计信息
RegisterNetEvent('music:cacheStats')
AddEventHandler('music:cacheStats', function(stats)
    SendNUIMessage({
        action = "cacheStats",
        stats = stats
    })
end)

-- 接收缓存清除结果
RegisterNetEvent('music:cacheCleared')
AddEventHandler('music:cacheCleared', function(success)
    SendNUIMessage({
        action = "cacheCleared",
        success = success
    })
end)

-- 接收来自其他玩家的音乐播放请求
-- 标准播放函数（简化版，不需要重定向处理）
local function standardPlayUrl(audioId, url, volume, coords, startTime)
    debugPrint("^3[标准播放] 播放URL: " .. url)
    
    -- 直接播放
    if coords then
        exports["xsound"]:PlayUrlPos(audioId, url, volume, coords, false)
    else
        exports["xsound"]:PlayUrl(audioId, url, volume, false)
    end
    
    -- 设置开始时间
    if startTime and startTime > 0 then
        Citizen.Wait(500)
        if exports["xsound"]:soundExists(audioId) then
            exports["xsound"]:setTimeStamp(audioId, startTime)
        end
    end
end

RegisterNetEvent('xsound:playMusicAtClient')
AddEventHandler('xsound:playMusicAtClient', function(url, volume, sourcePlayerId, startTime)
    -- 创建一个音频元素来播放音乐
    if not url or url == "" then 
        debugPrint("^1[音频播放] URL为空，无法播放")
        return 
    end
    
    debugPrint("^3[音频播放] 收到播放请求 - URL: " .. url)
    debugPrint("^3[音频播放] 音量: " .. tostring(volume) .. ", 源玩家ID: " .. tostring(sourcePlayerId))
    
    -- 使用xSound播放3D音效
    if sourcePlayerId then
        -- 获取播放音乐的玩家
        local playerPed = GetPlayerPed(-1)
        local sourcePed = GetPlayerPed(GetPlayerFromServerId(sourcePlayerId))
        local myId = GetPlayerServerId(PlayerId())
        
        debugPrint("^3[音频播放] 我的ServerID: " .. myId .. ", 源玩家ServerID: " .. sourcePlayerId)
        
        if DoesEntityExist(sourcePed) then
            local sourceCoords = GetEntityCoords(sourcePed)
            local playerCoords = GetEntityCoords(playerPed)
            local distance = #(sourceCoords - playerCoords)
            
            debugPrint("^3[音频播放] 源玩家位置: " .. tostring(sourceCoords))
            debugPrint("^3[音频播放] 我的位置: " .. tostring(playerCoords))
            debugPrint("^3[音频播放] 距离: " .. tostring(distance) .. " 米")
            
            -- 创建一个3D音效的音频源
            local audioId = "music_" .. sourcePlayerId
            debugPrint("^3[音频播放] 音频ID: " .. audioId)
            
            -- 如果已存在相同ID的音频，先停止它
            if exports["xsound"]:soundExists(audioId) then
                debugPrint("^3[音频播放] 停止现有音频")
                exports["xsound"]:Destroy(audioId)
            end
            
            -- 使用标准播放功能
            debugPrint("^2[音频播放] 开始播放3D音效")
            standardPlayUrl(audioId, url, volume, sourceCoords, startTime)
            
            -- 验证音频是否创建成功
            Citizen.Wait(1000) -- 增加等待时间以适应重定向解析
            if exports["xsound"]:soundExists(audioId) then
                debugPrint("^2[音频播放] 音频对象创建成功")
                
                -- 设置音频距离和音量
                exports["xsound"]:Distance(audioId, 50.0) -- 增加到50米
                exports["xsound"]:setVolume(audioId, volume)
                
                debugPrint("^2[音频播放] 播放设置完成，距离: 50米")
            else
                debugPrint("^1[音频播放] 音频对象创建失败")
            end
        else
            debugPrint("^1[音频播放] 源玩家实体不存在")
        end
    else
        debugPrint("^1[音频播放] 源玩家ID为空")
    end
end)

-- 接收停止音乐的请求
RegisterNetEvent('xsound:stopMusicAtClient')
AddEventHandler('xsound:stopMusicAtClient', function(sourcePlayerId)
    if sourcePlayerId then
        local audioId = "music_" .. sourcePlayerId
        
        -- 如果存在该音频，停止它
        if exports["xsound"]:soundExists(audioId) then
            exports["xsound"]:Destroy(audioId)
        end
    end
end)

-- 接收播放错误的通知
RegisterNetEvent('xsound:playbackError')
AddEventHandler('xsound:playbackError', function(errorMessage)
    debugPrint("^1[音乐播放器] ^7播放错误: " .. errorMessage)
    
    -- 检查是否是超时或网络错误
    if string.find(errorMessage, "连接超时") or string.find(errorMessage, "网络错误") then
        -- 如果当前有歌曲信息，尝试使用浏览器直接播放
        if currentSong and currentSong.url then
            debugPrint("^3[音乐播放器] ^7尝试使用浏览器直接播放...")
            
            -- 通知UI尝试直接播放
            SendNUIMessage({
                action = "directPlay",
                url = currentSong.url,
                title = currentSong.title,
                artist = currentSong.artist,
                duration = currentSong.duration
            })
            
            -- 仅设置一次延迟检查，不输出调试信息
            Citizen.SetTimeout(3000, function()
                -- 检查当前是否有音乐在播放
                if currentSong and isPlaying then
                    -- 计算播放进度
                    local startTime = currentSong.startTime or GetGameTimer()
                    local elapsedTime = (GetGameTimer() - startTime) / 1000
                    local duration = tonumber(currentSong.duration) or 180
                    
                    -- 发送播放状态更新通知，强制更新UI状态
                    SendNUIMessage({
                        action = "playbackStatus",
                        currentTime = elapsedTime,
                        duration = duration,
                        isPlaying = true,
                        forceUpdate = true
                    })
                    
                    -- 同时发送恢复正常状态的指令
                    SendNUIMessage({
                        action = "resetErrorState",
                        title = currentSong.title,
                        artist = currentSong.artist
                    })
                end
            end)
            
            return
        end
    end
    
    -- 发送错误消息到UI
    if musicPlayerOpen then
        SendNUIMessage({
            action = "playbackError",
            error = errorMessage
        })
    end
end)

-- 播放状态更新线程
Citizen.CreateThread(function()
    local lastUpdate = 0
    local updateInterval = 500 -- 每0.5秒更新一次
    
    while true do
        Citizen.Wait(100)
        
        if isPlaying and currentSong then
            local currentTime = GetGameTimer()
            
            -- 每0.5秒更新一次播放状态
            if currentTime - lastUpdate > updateInterval then
                lastUpdate = currentTime
                
                -- 计算播放进度
                local startTime = currentSong.startTime or currentTime
                local elapsedTime = (currentTime - startTime) / 1000
                local duration = tonumber(currentSong.duration) or 180 -- 确保duration是数字，默认3分钟
                
                -- 如果超过了歌曲时长，可能是循环播放
                if elapsedTime > duration then
                    -- 如果是循环播放，重置开始时间
                    if currentSong.isLooped then
                        currentSong.startTime = GetGameTimer()
                        elapsedTime = 0
                    else
                        -- 否则停止播放
                        TriggerServerEvent("xsound:stopMusic")
                        currentSong = nil
                        isPlaying = false
                        
                        -- 发送停止播放状态到UI
                        if musicPlayerOpen then
                            SendNUIMessage({
                                action = "playbackStatus",
                                isPlaying = false
                            })
                        end
                    end
                end
                
                -- 发送播放状态到UI，但不打印调试信息
                if musicPlayerOpen then
                    SendNUIMessage({
                        action = "playbackStatus",
                        currentTime = elapsedTime,
                        duration = duration,
                        isPlaying = isPlaying
                    })
                end
            end
        else
            Citizen.Wait(1000) -- 如果没有播放，降低检查频率
        end
    end
end)

-- 添加资源停止事件处理
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        debugPrint("^2[音乐播放器] ^7资源停止，强制停止所有音频...")
        
        -- 停止当前播放的音乐
        if currentSong then
            -- 通知服务器停止音乐
            TriggerServerEvent("xsound:stopMusic")
            
            -- 重置状态
            currentSong = nil
            isPlaying = false
        end
        
        -- 停止所有可能的音频源
        local myServerId = GetPlayerServerId(PlayerId())
        if myServerId then
            local audioId = "music_" .. myServerId
            if exports["xsound"]:soundExists(audioId) then
                exports["xsound"]:Destroy(audioId)
            end
        end
        
        -- 遍历所有可能的玩家ID并尝试停止他们的音频
        for i = 0, 128 do
            local audioId = "music_" .. i
            if exports["xsound"]:soundExists(audioId) then
                exports["xsound"]:Destroy(audioId)
            end
        end
        
        -- 尝试停止所有已知的音频ID
        local commonAudioIds = {"music", "alarm", "siren", "horn", "radio", "phone"}
        for _, id in ipairs(commonAudioIds) do
            if exports["xsound"]:soundExists(id) then
                exports["xsound"]:Destroy(id)
            end
        end
        
        -- 确保音频真的停止
        SetAudioFlag("DisableFlightMusic", true)
        SetAudioFlag("PoliceScannerDisabled", true)
        
        -- 告诉UI资源正在停止
        if musicPlayerOpen then
            SendNUIMessage({
                action = "resourceStopping"
            })
        end
    end
end)

-- 设置播放位置
RegisterNUICallback('setTimeStamp', function(data, cb)
    local time = tonumber(data.time) or 0
    
    -- 确保时间是有效值
    if time < 0 then time = 0 end
    
    if currentSong and isPlaying then
        local myServerId = GetPlayerServerId(PlayerId())
        if myServerId then
            local audioId = "music_" .. myServerId
            if exports["xsound"]:soundExists(audioId) then
                -- 设置播放位置
                exports["xsound"]:setTimeStamp(audioId, time)
                
                -- 更新当前歌曲的开始时间，以便正确计算播放进度
                currentSong.startTime = GetGameTimer() - (time * 1000)
                
                -- 通知服务器设置播放位置
                TriggerServerEvent("xsound:setTimeStamp", time)
                
                cb({status = "success"})
                return
            end
        end
    end
    
    cb({status = "error", message = "无法设置播放位置"})
end)

-- 设置音量
RegisterNUICallback('setVolume', function(data, cb)
    local volume = tonumber(data.volume) or 1.0
    
    -- 确保音量在有效范围内
    if volume < 0.0 then volume = 0.0 end
    if volume > 1.0 then volume = 1.0 end
    
    if currentSong then
        local myServerId = GetPlayerServerId(PlayerId())
        if myServerId then
            local audioId = "music_" .. myServerId
            if exports["xsound"]:soundExists(audioId) then
                -- 设置音量
                exports["xsound"]:setVolume(audioId, volume)
                
                -- 通知服务器设置音量
                TriggerServerEvent("xsound:setVolume", volume)
                
                cb({status = "success"})
                return
            end
        end
    end
    
    cb({status = "error", message = "无法设置音量"})
end)

-- 客户端处理来自其他玩家的设置播放位置请求
RegisterNetEvent("xsound:setTimeStampAtClient")
AddEventHandler("xsound:setTimeStampAtClient", function(sourcePlayerId, time)
    local myServerId = GetPlayerServerId(PlayerId())
    if myServerId == sourcePlayerId then
        return -- 跳过，因为自己已经处理了设置
    end
    
    -- 设置播放位置
    local audioId = "music_" .. sourcePlayerId
    if exports["xsound"]:soundExists(audioId) then
        -- 设置播放位置
        exports["xsound"]:setTimeStamp(audioId, time)
    end
end)

-- 客户端处理来自其他玩家的设置音量请求
RegisterNetEvent("xsound:setVolumeAtClient")
AddEventHandler("xsound:setVolumeAtClient", function(sourcePlayerId, volume)
    local myServerId = GetPlayerServerId(PlayerId())
    if myServerId == sourcePlayerId then
        return -- 跳过，因为自己已经处理了设置
    end
    
    -- 设置音量
    local audioId = "music_" .. sourcePlayerId
    if exports["xsound"]:soundExists(audioId) then
        -- 设置音量
        exports["xsound"]:setVolume(audioId, volume)
    end
end)

-- 创建歌单NUI回调
RegisterNUICallback('createPlaylist', function(data, cb)
    local name = data.name
    local description = data.description or ""
    
    if not name or name == "" then
        cb({status = "error", message = "歌单名称不能为空"})
        return
    end
    
    debugPrint("^3[音乐播放器] ^7请求创建歌单: " .. name)
    
    -- 触发服务器事件创建歌单
    TriggerServerEvent("music:createPlaylist", name, description)

    -- 注册一次性事件等待服务器响应
    RegisterNetEvent("music:playlistCreated")
    createOneTimeEventHandler("music:playlistCreated", function(success, id, message, isTimeout)
        if isTimeout then
            cb({status = "error", message = "请求超时"})
        else
            if success then
                cb({status = "success", playlistId = id})
            else
                cb({status = "error", message = message or "创建失败"})
            end
        end
    end, 5000)
end)

-- 获取玩家歌单列表NUI回调
RegisterNUICallback('getPlayerPlaylists', function(data, cb)
    debugPrint("^3[音乐播放器] ^7请求获取玩家歌单")
    
    -- 触发服务器事件获取歌单列表
    TriggerServerEvent("music:getPlayerPlaylists")
    
    -- 注册一次性事件等待服务器响应
    RegisterNetEvent("music:playerPlaylists")
    createOneTimeEventHandler("music:playerPlaylists", function(playlists, isTimeout)
        if isTimeout then
            cb({status = "error", message = "请求超时"})
        else
            cb({status = "success", playlists = playlists or {}})
        end
    end, 5000)
end)

-- 获取歌单中的歌曲NUI回调
RegisterNUICallback('getPlaylistSongs', function(data, cb)
    local playlistId = data.playlistId
    
    if not playlistId then
        cb({status = "error", message = "歌单ID不能为空"})
        return
    end
    
    debugPrint("^3[音乐播放器] ^7请求获取歌单歌曲，歌单ID: " .. playlistId)
    
    -- 触发服务器事件获取歌单歌曲
    TriggerServerEvent("music:getPlaylistSongs", playlistId)
    
    -- 注册一次性事件等待服务器响应
    RegisterNetEvent("music:playlistSongs")
    createOneTimeEventHandler("music:playlistSongs", function(songs, isTimeout)
        if isTimeout then
            cb({status = "error", message = "请求超时"})
        else
            cb({status = "success", songs = songs or {}})
        end
    end, 5000)
end)

-- 添加歌曲到歌单NUI回调
RegisterNUICallback('addSongToPlaylist', function(data, cb)
    local playlistId = data.playlistId
    local songId = data.songId
    local searchKeyword = data.searchKeyword
    local platform = data.platform
    
    if not playlistId or not songId then
        cb({status = "error", message = "参数不完整"})
        return
    end
    
    debugPrint("^3[音乐播放器] ^7请求添加歌曲到歌单，歌单ID: " .. playlistId .. ", 歌曲ID: " .. songId .. ", 搜索词: " .. (searchKeyword or "无") .. ", 平台: " .. (platform or "无"))
    
    -- 触发服务器事件
    TriggerServerEvent("music:addSongToPlaylist", playlistId, songId, searchKeyword, platform)
    
    -- 注册一次性事件等待服务器响应
    RegisterNetEvent("music:songAddedToPlaylist")
    createOneTimeEventHandler("music:songAddedToPlaylist", function(success, isTimeout)
        if isTimeout then
            cb({status = "error", message = "请求超时"})
        else
            if success then
                cb({status = "success"})
            else
                cb({status = "error", message = "添加失败"})
            end
        end
    end, 5000)
end)

-- 添加/删除收藏歌曲NUI回调
RegisterNUICallback('toggleFavoriteSong', function(data, cb)
    local songId = data.songId
    local isFavorite = data.isFavorite
    local searchKeyword = data.searchKeyword
    local platform = data.platform
    local songData = data.songData  -- 新增：获取完整的歌曲数据
    
    if not songId then
        cb({status = "error", message = "歌曲ID不能为空"})
        return
    end
    
    if isFavorite then
        -- 添加收藏
        debugPrint("^3[音乐播放器] ^7请求添加歌曲到收藏，歌曲ID: " .. songId .. ", 搜索词: " .. (searchKeyword or "无") .. ", 平台: " .. (platform or "无"))
        
        -- 如果有完整歌曲数据，先确保保存到music_songs表
        if songData then
            debugPrint("^2[调试] 收到歌曲数据: 标题=" .. (songData.name or songData.title or "无") .. 
                  ", 歌手=" .. (songData.singer or songData.artist or "无") .. 
                  ", 平台=" .. (songData.platform or "无") .. 
                  ", 搜索关键词=" .. (songData.search_keyword or "无"))
            TriggerServerEvent("music:saveSongToDatabase", songData)
        else
            debugPrint("^1[调试] 未收到歌曲数据，创建基本数据")
            -- 创建基本的歌曲数据
            local basicSongData = {
                id = songId,
                name = "每日推荐歌曲",
                singer = "未知歌手",
                platform = platform or "NETEASE",
                search_keyword = searchKeyword or "每日推荐"
            }
            TriggerServerEvent("music:saveSongToDatabase", basicSongData)
        end
        
        -- 确保搜索关键词和平台信息有默认值
        if not searchKeyword or searchKeyword == "" then
            searchKeyword = "每日推荐"
        end
        if not platform or platform == "" then
            platform = "NETEASE"
        end
        
        TriggerServerEvent("music:addFavoriteSong", songId, searchKeyword, platform)
        
        -- 注册一次性事件等待服务器响应
        RegisterNetEvent("music:favoriteSongAdded")
        createOneTimeEventHandler("music:favoriteSongAdded", function(success, isTimeout)
            if isTimeout then
                cb({status = "error", message = "请求超时"})
            else
                if success then
                    cb({status = "success", isFavorite = true})
                else
                    cb({status = "error", message = "添加收藏失败"})
                end
            end
        end, 5000)
    else
        -- 移除收藏
        debugPrint("^3[音乐播放器] ^7请求从收藏中移除歌曲，歌曲ID: " .. songId)
        TriggerServerEvent("music:removeFavoriteSong", songId)
        
        -- 注册一次性事件等待服务器响应
        RegisterNetEvent("music:favoriteSongRemoved")
        createOneTimeEventHandler("music:favoriteSongRemoved", function(success, isTimeout)
            if isTimeout then
                cb({status = "error", message = "请求超时"})
            else
                if success then
                    cb({status = "success", isFavorite = false})
                else
                    cb({status = "error", message = "移除收藏失败"})
                end
            end
        end, 5000)
    end
end)

-- 获取收藏歌曲列表NUI回调
RegisterNUICallback('getFavoriteSongs', function(data, cb)
    debugPrint("^3[音乐播放器] ^7请求获取收藏歌曲")
    
    -- 触发服务器事件获取收藏歌曲
    TriggerServerEvent("music:getFavoriteSongs")
    
    -- 注册一次性事件等待服务器响应
    RegisterNetEvent("music:favoriteSongs")
    createOneTimeEventHandler("music:favoriteSongs", function(songs, isTimeout)
        if isTimeout then
            cb({status = "error", message = "请求超时"})
        else
            cb({status = "success", songs = songs or {}})
        end
    end, 5000)
end)

-- 检查歌曲是否已收藏NUI回调
RegisterNUICallback('checkSongFavorite', function(data, cb)
    local songId = data.songId
    
    if not songId then
        cb({status = "error", message = "歌曲ID不能为空"})
        return
    end
    
    -- 触发服务器事件检查歌曲是否已收藏
    TriggerServerEvent("music:checkSongFavorite", songId)
    
    -- 注册一次性事件等待服务器响应
    RegisterNetEvent("music:songFavoriteStatus")
    createOneTimeEventHandler("music:songFavoriteStatus", function(isFavorite, isTimeout)
        if isTimeout then
            cb({status = "error", message = "请求超时"})
        else
            cb({status = "success", isFavorite = isFavorite})
        end
    end, 5000)
end)

-- 添加NUICallback处理程序，用于触发服务器事件
RegisterNUICallback('triggerServerEvent', function(data, cb)
    local eventName = data.eventName
    local args = data.args
    
    if eventName then
        debugPrint("^3[音乐播放器] ^7触发服务器事件: " .. eventName .. ", 参数类型: " .. type(args))
        
        -- 详细的参数调试
        if type(args) == "table" then
            debugPrint("^2[详细调试] args.type: " .. tostring(args.type))
            debugPrint("^2[详细调试] args.keyword: " .. tostring(args.keyword))
            debugPrint("^2[详细调试] args.platform: " .. tostring(args.platform))
            debugPrint("^2[详细调试] args.source: " .. tostring(args.source))
        end
        
        -- 确保传递正确的参数到服务器
        if args then
            TriggerServerEvent(eventName, args)
        else
            TriggerServerEvent(eventName)
        end
    end
    
    cb({status = 'success'})
end)

-- 注册客户端事件处理程序，接收服务器的每日推荐响应
RegisterNetEvent('getDailyRecommend:response')
AddEventHandler('getDailyRecommend:response', function(data)
    -- 将响应传递给UI
    SendNUIMessage({
        action = "getDailyRecommend:response",
        status = data.status,
        songs = data.songs,
        message = data.message
    })
end)

-- 注册客户端事件处理程序，接收服务器的排行榜响应
RegisterNetEvent('getRankingSongs:response')
AddEventHandler('getRankingSongs:response', function(data)
    debugPrint("^3[音乐播放器] ^7收到排行榜响应，发送到UI")
    
    if data and data.songs and #data.songs > 0 then
        debugPrint("^2[音乐播放器] ^7排行榜歌曲数量: " .. #data.songs)
        
        -- 确保每首歌曲都有正确的字段
        for i, song in ipairs(data.songs) do
            -- 确保必要字段存在
            if not song.title and song.name then song.title = song.name end
            if not song.artist and song.singer then song.artist = song.singer end
            if not song.cover and song.pic then song.cover = song.pic end
            if not song.cover and song.picUrl then song.cover = song.picUrl end
            
            -- 调试信息
            if i <= 3 then -- 只输出前三首歌曲信息，避免日志过多
                debugPrint(string.format("^2[音乐播放器] ^7歌曲 #%d - ID: %s, 标题: %s, 歌手: %s, URL存在: %s", 
                    i, 
                    song.id or "未知", 
                    song.title or song.name or "未知歌曲", 
                    song.artist or song.singer or "未知歌手",
                    song.url and "是" or "否"
                ))
            end
        end
    else
        debugPrint("^1[音乐播放器] ^7排行榜响应中没有歌曲数据")
    end
    
    -- 将响应传递给UI
    SendNUIMessage({
        action = "getRankingSongs:response",
        status = data.status,
        songs = data.songs,
        type = data.type,
        message = data.message
    })
end)

-- 注册客户端事件，用于接收和转发NUI消息
RegisterNetEvent('send_player_nui_message')
AddEventHandler('send_player_nui_message', function(data)
    debugPrint("^3[音乐播放器] ^7收到NUI消息，转发到UI，类型:", data.type)
    -- 直接将消息转发给UI
    SendNUIMessage(data)
end)

-- 添加获取歌曲URL的回调函数
RegisterNUICallback('getSongUrl', function(data, cb)
    local songId = data.songId
    local platform = data.platform
    
    if not songId or not platform then
        cb({status = "error", message = "缺少必要参数"})
        return
    end
    
    debugPrint("^3[音乐播放器] ^7请求获取歌曲URL - ID:", songId, "平台:", platform)
    
    -- 触发服务器事件获取歌曲URL
    TriggerServerEvent('music:getSongUrl', songId, platform)
    
    -- 注册一次性事件等待服务器响应
    RegisterNetEvent("music:songUrl")
    createOneTimeEventHandler("music:songUrl", function(url, isTimeout)
        if isTimeout then
            cb({status = "error", message = "请求超时"})
        else
            if url and url ~= "" then
                cb({status = "success", url = url})
            else
                cb({status = "error", message = "无法获取歌曲URL"})
            end
        end
    end, 5000)
end)

-- 获取歌曲信息NUI回调
RegisterNUICallback('getSongInfo', function(data, cb)
    local songId = data.songId
    local platform = data.platform or "NETEASE"
    
    if not songId then
        cb({status = "error", message = "歌曲ID不能为空"})
        return
    end
    
    -- 触发服务器事件获取歌曲信息
    TriggerServerEvent("music:getSongInfo", songId, platform)
    
    -- 注册一次性事件等待服务器响应
    RegisterNetEvent("music:songInfo")
    createOneTimeEventHandler("music:songInfo", function(song, isTimeout)
        if isTimeout then
            cb({status = "error", message = "请求超时"})
        else
            if song then
                cb({status = "success", song = song})
            else
                cb({status = "error", message = "未找到歌曲信息"})
            end
        end
    end, 5000)
end)

-- 添加搜索调试切换命令
RegisterCommand("toggleSearchDebug", function()
    DEBUG = not DEBUG
    TriggerEvent("chatMessage", "[音乐播放器]", {0, 255, 255}, "搜索调试模式: " .. (DEBUG and "已启用" or "已禁用"))
    if DEBUG then
        TriggerEvent("chatMessage", "[音乐播放器]", {0, 255, 255}, "搜索调试日志现在会显示在控制台中")
    end
end, false)

-- 测试音频播放命令
RegisterCommand("testplay", function(source, args)
    local testUrl = args[1] or "https://shybot.top/v2/music/api/?type=kg_url&hash=0EB88F45412BFB597C19964950E558D2&album_id=36340852&album_audio_id=249901234&down=1"
    local myId = GetPlayerServerId(PlayerId())
    
    print("^3[测试播放] 测试URL: " .. testUrl)
    print("^3[测试播放] 我的ServerID: " .. myId)
    
    -- 直接调用音频播放事件
    TriggerEvent('xsound:playMusicAtClient', testUrl, 1.0, myId, 0)
end, false)

-- 测试智能播放功能
RegisterCommand("testsmart", function(source, args)
    local testUrl = args[1] or "https://shybot.top/v2/music/api/?type=kg_url&hash=0EB88F45412BFB597C19964950E558D2&album_id=36340852&album_audio_id=249901234&down=1"
    local audioId = "test_smart_" .. math.random(1000, 9999)
    
    print("^3[智能播放测试] 测试URL: " .. testUrl)
    print("^3[智能播放测试] 音频ID: " .. audioId)
    
    -- 直接调用智能播放函数
    smartPlayUrl(audioId, testUrl, 1.0, nil, 0)
    
    -- 延迟检查结果
    Citizen.SetTimeout(2000, function()
        if exports["xsound"]:soundExists(audioId) then
            local isPlaying = exports["xsound"]:isPlaying(audioId)
            local duration = exports["xsound"]:getMaxDuration(audioId)
            print("^2[智能播放测试] 音频创建成功")
            print("^3[智能播放测试] 播放状态: " .. tostring(isPlaying))
            print("^3[智能播放测试] 总时长: " .. tostring(duration))
        else
            print("^1[智能播放测试] 音频创建失败")
        end
    end)
end, false)

-- 音频状态检查命令
RegisterCommand("audiostatus", function()
    local myId = GetPlayerServerId(PlayerId())
    local audioId = "music_" .. myId
    
    print("^3[音频状态] 音频ID: " .. audioId)
    
    if exports["xsound"]:soundExists(audioId) then
        local isPlaying = exports["xsound"]:isPlaying(audioId)
        local volume = exports["xsound"]:getVolume(audioId)
        local currentTime = exports["xsound"]:getTimeStamp(audioId)
        local maxTime = exports["xsound"]:getMaxDuration(audioId)
        
        print("^2[音频状态] 音频存在")
        print("^3[音频状态] 正在播放: " .. tostring(isPlaying))
        print("^3[音频状态] 音量: " .. tostring(volume))
        print("^3[音频状态] 当前时间: " .. tostring(currentTime))
        print("^3[音频状态] 总时长: " .. tostring(maxTime))
    else
        print("^1[音频状态] 音频不存在")
    end
end, false)

-- 手动创建音频测试
RegisterCommand("testcreate", function(source, args)
    local testUrl = args[1] or "https://shybot.top/v2/music/api/?type=kg_url&hash=0EB88F45412BFB597C19964950E558D2&album_id=36340852&album_audio_id=249901234&down=1"
    local audioId = "test_audio_" .. math.random(1000, 9999)
    
    print("^3[创建测试] 创建音频ID: " .. audioId)
    print("^3[创建测试] URL: " .. testUrl)
    
    -- 直接使用xSound播放
    exports["xsound"]:PlayUrl(audioId, testUrl, 1.0, false)
    
    Citizen.Wait(1000)
    
    if exports["xsound"]:soundExists(audioId) then
        print("^2[创建测试] 音频创建成功")
        local isPlaying = exports["xsound"]:isPlaying(audioId)
        local duration = exports["xsound"]:getMaxDuration(audioId)
        local currentTime = exports["xsound"]:getTimeStamp(audioId)
        print("^3[创建测试] 播放状态: " .. tostring(isPlaying))
        print("^3[创建测试] 总时长: " .. tostring(duration))
        print("^3[创建测试] 当前时间: " .. tostring(currentTime))
        
        -- 检查音频流信息
        Citizen.Wait(2000)
        local newCurrentTime = exports["xsound"]:getTimeStamp(audioId)
        print("^3[创建测试] 2秒后时间: " .. tostring(newCurrentTime))
        
        if newCurrentTime and currentTime and newCurrentTime > currentTime then
            print("^2[创建测试] 音频时间在正常推进")
        else
            print("^1[创建测试] 音频时间没有推进，可能音频文件有问题")
        end
    else
        print("^1[创建测试] 音频创建失败")
    end
end, false)

-- 对比测试QQ音乐URL
RegisterCommand("testqq", function()
    local qqUrl = "https://shybot.top/v2/music/api/?type=qq_url&id=0012g3Et1iFQCC&down=1"  -- QQ音乐URL格式
    local audioId = "test_qq_" .. math.random(1000, 9999)
    
    print("^3[QQ测试] 创建音频ID: " .. audioId)
    print("^3[QQ测试] URL: " .. qqUrl)
    
    exports["xsound"]:PlayUrl(audioId, qqUrl, 1.0, false)
    
    Citizen.Wait(1000)
    
    if exports["xsound"]:soundExists(audioId) then
        print("^2[QQ测试] 音频创建成功")
        local isPlaying = exports["xsound"]:isPlaying(audioId)
        local duration = exports["xsound"]:getMaxDuration(audioId)
        print("^3[QQ测试] 播放状态: " .. tostring(isPlaying))
        print("^3[QQ测试] 总时长: " .. tostring(duration))
    else
        print("^1[QQ测试] 音频创建失败")
    end
end, false)

-- 测试简化的酷狗URL
RegisterCommand("testkgsimple", function()
    local kgSimpleUrl = "https://shybot.top/v2/music/api/?type=kg_url&hash=0EB88F45412BFB597C19964950E558D2&down=1"  -- 移除额外参数
    local audioId = "test_kg_simple_" .. math.random(1000, 9999)
    
    print("^3[酷狗简化测试] 创建音频ID: " .. audioId)
    print("^3[酷狗简化测试] URL: " .. kgSimpleUrl)
    
    exports["xsound"]:PlayUrl(audioId, kgSimpleUrl, 1.0, false)
    
    Citizen.Wait(1000)
    
    if exports["xsound"]:soundExists(audioId) then
        print("^2[酷狗简化测试] 音频创建成功")
        local isPlaying = exports["xsound"]:isPlaying(audioId)
        local duration = exports["xsound"]:getMaxDuration(audioId)
        print("^3[酷狗简化测试] 播放状态: " .. tostring(isPlaying))
        print("^3[酷狗简化测试] 总时长: " .. tostring(duration))
    else
        print("^1[酷狗简化测试] 音频创建失败")
    end
end, false)

-- 测试直接MP3 URL
RegisterCommand("testdirectmp3", function()
    local directMp3 = "http://fs.youthandroid2.kugou.com/202506201210/445e98958948e3811a17a9ba64b13dcd/v3/0eb88f45412bfb597c19964950e558d2/yp/full/ap3116_us1861499369_df0io82d3zj09a0mzmx93zhuoj_pi411_mx249901234_qu128_s4155797882.mp3"
    local audioId = "test_direct_mp3_" .. math.random(1000, 9999)
    
    print("^3[直接MP3测试] 创建音频ID: " .. audioId)
    print("^3[直接MP3测试] URL: " .. directMp3)
    
    exports["xsound"]:PlayUrl(audioId, directMp3, 1.0, false)
    
    Citizen.Wait(1000)
    
    if exports["xsound"]:soundExists(audioId) then
        print("^2[直接MP3测试] 音频创建成功")
        local isPlaying = exports["xsound"]:isPlaying(audioId)
        local duration = exports["xsound"]:getMaxDuration(audioId)
        print("^3[直接MP3测试] 播放状态: " .. tostring(isPlaying))
        print("^3[直接MP3测试] 总时长: " .. tostring(duration))
    else
        print("^1[直接MP3测试] 音频创建失败")
    end
end, false)

-- URL重定向解析函数
local function resolveRedirect(url, callback)
    print("^3[重定向解析] 开始解析: " .. url)
    
    PerformHttpRequest(url, function(statusCode, responseText, responseHeaders)
        print("^3[重定向解析] 状态码: " .. tostring(statusCode))
        
        if statusCode == 302 or statusCode == 301 then
            -- 重定向，查找Location头
            for name, value in pairs(responseHeaders or {}) do
                if string.lower(name) == "location" then
                    print("^2[重定向解析] 找到重定向地址: " .. value)
                    callback(value)
                    return
                end
            end
        elseif statusCode == 200 then
            -- 直接可用的URL
            print("^2[重定向解析] URL直接可用")
            callback(url)
            return
        end
        
        print("^1[重定向解析] 解析失败")
        callback(nil)
    end, "HEAD")  -- 使用HEAD请求只获取响应头
end

-- 测试重定向解析
RegisterCommand("testredirect", function()
    local kgUrl = "https://shybot.top/v2/music/api/?type=kg_url&hash=0EB88F45412BFB597C19964950E558D2&album_id=36340852&album_audio_id=249901234&down=1"
    
    resolveRedirect(kgUrl, function(resolvedUrl)
        if resolvedUrl then
            print("^2[重定向测试] 解析成功: " .. resolvedUrl)
            
            -- 使用解析后的URL播放
            local audioId = "test_resolved_" .. math.random(1000, 9999)
            exports["xsound"]:PlayUrl(audioId, resolvedUrl, 1.0, false)
            
            Citizen.Wait(1000)
            
            if exports["xsound"]:soundExists(audioId) then
                print("^2[重定向测试] 音频播放成功")
                local isPlaying = exports["xsound"]:isPlaying(audioId)
                print("^3[重定向测试] 播放状态: " .. tostring(isPlaying))
            else
                print("^1[重定向测试] 音频播放失败")
            end
        else
            print("^1[重定向测试] URL解析失败")
        end
    end)
end, false)