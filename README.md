# FiveM 音乐系统

## 📝 简介

这是一个为FiveM服务器开发的高级音乐播放系统，支持在游戏中播放和共享来自多个音乐平台的音乐。系统包含两个主要组件：
1. **音乐播放器** - 允许玩家搜索、播放音乐以及创建歌单
2. **音乐盒** - 可放置在地图上的物理对象，能够播放音乐并向周围玩家显示歌词

## ✨ 主要功能

- **多平台支持**：支持QQ音乐、网易云音乐、酷狗、酷我等多个音乐平台
- **歌词同步**：实时显示带有特效的3D歌词
- **歌单管理**：创建、查看和管理个人歌单
- **收藏系统**：收藏喜欢的歌曲
- **位置音频**：基于3D空间音频，距离越远音量越小
- **音乐盒功能**：在游戏世界中放置音乐盒，让附近玩家都能听到并看到歌词
- **权限控制**：只有音乐盒的放置者才能拾取或移除
- **数据库存储**：自动缓存歌曲信息和歌词，减少API请求

## 📋 安装要求

- FiveM服务器
- oxmysql资源
- xsound资源（3D音频系统）
- ox_target资源（可选，用于交互菜单）

## 🔧 安装步骤

1. 下载最新版本的资源
2. 将`music`文件夹放入服务器的resources目录
3. 确保以下行存在于server.cfg中：
   ```
   ensure oxmysql
   ensure xsound
   ensure ox_target  # 可选
   ensure music
   ```
4. 首次启动时，系统将自动创建所需的数据库表

## 🎮 玩家使用指南

### 音乐播放器



### 音乐盒使用

- 右键点击音乐盒打开交互菜单
- 选择"打开播放器"来播放音乐
- 如果是音乐盒的主人，还可以选择"收起音乐盒"来拾取它



## 🔍 开发者信息

### 目录结构

```
music/
├── client/               # 客户端脚本
│   ├── client.lua        # 音乐播放器主客户端代码
│   └── radio_client.lua  # 音乐盒客户端代码
├── server/               # 服务端脚本
│   ├── server.lua        # 主服务端代码
│   ├── radio_server.lua  # 音乐盒服务端代码
│   └── daily_recommend.lua # 每日推荐功能
├── lib/                  # 核心功能库
│   ├── api.lua           # API请求处理
│   ├── cache.lua         # 缓存管理
│   ├── database.lua      # 数据库操作
│   ├── music_service.lua # 音乐服务逻辑
│   └── network.lua       # 网络通信
├── data/                 # 数据文件
│   └── radio_items.lua   # 音乐盒物品定义
├── html/                 # 前端界面
│   ├── index.html        # 播放器界面HTML
│   ├── style.css         # 样式表
│   └── script.js         # 前端脚本
└── fxmanifest.lua        # 资源清单
```



## 📊 性能说明

- 系统使用数据库缓存歌曲信息和歌词，减少API请求次数
- 3D音频和歌词仅在距离音乐盒30米范围内的玩家处理，降低资源消耗
- 音量随距离自动调整，超出范围的玩家不会加载音频
- 每个玩家限制只能放置一个音乐盒，避免资源滥用

## 📜 许可协议

此资源使用[MIT许可证](https://opensource.org/licenses/MIT)，允许自由使用、修改和分发，但需保留原始版权声明。

## 🛠️ 故障排除

### 常见问题

1. **无法听到音乐**
   - 检查xsound资源是否正确加载
   - 确认游戏音量设置
   - 尝试使用`/radiodiagnose`命令进行诊断

2. **无法看到歌词**
   - 确保距离音乐盒足够近（30米内）
   - 检查是否有歌词缓存问题，可使用`/lyrics_db_info`查看

3. **音乐盒无法交互**
   - 确认ox_target资源是否正确加载
   - 检查是否接近音乐盒（2米内）

如有更多问题，请提交issues或联系开发者。 