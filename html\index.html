<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FiveM Music Player</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 可拖动的标题栏 */
        .title-bar {
            height: 30px;
            background: var(--sidebar-bg);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            cursor: move;
        }
        
        .title-bar-title {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        /* 关闭按钮样式 */
        .close-btn {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--text-secondary);
            transition: color 0.2s;
        }
        
        .close-btn:hover {
            color: var(--accent-color);
        }
        
        /* 确保应用居中 */
        #app {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none; /* 默认隐藏 */
        }
    </style>
</head>
<body>
    <div id="app" class="app">
        <!-- 添加标题栏 -->
        <div class="title-bar">
            <div class="title-bar-title"></div>
        </div>
        
        <!-- 主容器 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <!-- Logo和主题切换 -->
                <div class="sidebar-header">
                    <div class="logo">
                        <i class="fas fa-music"></i>
                        <span>FiveM Music</span>
                    </div>
                    <div class="theme-selector">
                        <button class="theme-btn" data-theme="dark" title="深色主题">
                            <i class="fas fa-moon"></i>
                        </button>
                        <button class="theme-btn" data-theme="light" title="浅色主题">
                            <i class="fas fa-sun"></i>
                        </button>
                        <button class="theme-btn" data-theme="neon" title="霓虹主题">
                            <i class="fas fa-bolt"></i>
                        </button>
                        <button class="theme-btn" data-theme="gradient" title="渐变主题">
                            <i class="fas fa-palette"></i>
                        </button>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <nav class="nav-menu">
                    <ul>
                        <li class="nav-item active" data-view="discover">
                            <i class="fas fa-compass"></i>
                            <span>发现音乐</span>
                        </li>
                        <li class="nav-item" data-view="recommend">
                            <i class="fas fa-heart"></i>
                            <span>每日推荐</span>
                        </li>
                        <li class="nav-item" data-view="ranking">
                            <i class="fas fa-trophy"></i>
                            <span>排行榜</span>
                        </li>
                        <li class="nav-item" data-view="search">
                            <i class="fas fa-search"></i>
                            <span>搜索</span>
                        </li>
                    </ul>
                </nav>

                <!-- 我的音乐 -->
                <div class="my-music">
                    <h3>我的音乐</h3>
                    <ul>
                        <li class="nav-item" data-view="favorites">
                            <i class="fas fa-star"></i>
                            <span>我的收藏</span>
                            <span class="count">0</span>
                        </li>
                        <li class="nav-item" data-view="playlists">
                            <i class="fas fa-list"></i>
                            <span>创建的歌单</span>
                            <span class="count">0</span>
                        </li>
                        <li class="nav-item" data-view="history">
                            <i class="fas fa-history"></i>
                            <span>最近播放</span>
                        </li>
                    </ul>
                </div>

                <!-- 用户创建的歌单列表 -->
                <div class="user-playlists">
                    <div class="playlist-header">
                        <h4>我的歌单</h4>
                        <button class="btn-create-playlist">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <ul id="playlistsList" class="playlists-list">
                        <!-- 歌单列表将在这里动态生成 -->
                    </ul>
                </div>
            </aside>

            <!-- 主内容区 -->
            <main class="main-content">
                <!-- 顶部搜索栏 -->
                <header class="top-bar">
                    <div class="search-container">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="searchInput" placeholder="搜索歌曲、歌手、专辑">
                            <select id="platformSelector" class="platform-selector">
                                <option value="QQ">QQ音乐</option>
                                <option value="NETEASE">网易云音乐</option>
                                <option value="KUGOU">酷狗音乐</option>
                                <option value="KUWO">酷我音乐</option>
                                <option value="XMLY">喜马拉雅</option>
                                <option value="QQ_RADIO">蜻蜓FM</option>
                            </select>
                            <button id="searchBtn" class="search-btn">搜索</button>
                        </div>
                    </div>
                </header>

                <!-- 内容视图区域 -->
                <div class="content-views">
                    <!-- 发现音乐视图 -->
                    <div id="discoverView" class="view active">
                        <div class="view-header">
                            <h2>发现音乐</h2>
                        </div>
                        
                        <!-- 轮播图 -->
                        <div class="banner-carousel">
                            <div class="banner-container">
                                <div class="banner-slide active">
                                    <div class="banner-content">
                                        <h3>热门推荐</h3>
                                        <p>发现最新最热的音乐</p>
                                    </div>
                                </div>
                                <div class="banner-slide">
                                    <div class="banner-content">
                                        <h3>精选歌单</h3>
                                        <p>为你精心挑选的音乐合集</p>
                                    </div>
                                </div>
                                <div class="banner-slide">
                                    <div class="banner-content">
                                        <h3>新歌首发</h3>
                                        <p>最新发布的优质单曲</p>
                                    </div>
                                </div>
                            </div>
                            <div class="banner-dots">
                                <span class="dot active" data-slide="0"></span>
                                <span class="dot" data-slide="1"></span>
                                <span class="dot" data-slide="2"></span>
                            </div>
                        </div>

                        <!-- 推荐内容 -->
                        <div class="content-sections">
                            <section class="music-section">
                                <div class="section-header">
                                    <h3>热门歌曲</h3>
                                    
                                </div>
                                <div class="song-grid" id="hotSongs">
                                    <!-- 热门歌曲列表将在这里动态生成 -->
                                </div>
                            </section>

                            
                        </div>
                    </div>

                    <!-- 每日推荐视图 -->
                    <div id="recommendView" class="view">
                        <div class="view-header">
                            <h2>每日推荐</h2>
                            <p class="view-subtitle">根据你的喜好为你推荐</p>
                        </div>
                        <div class="daily-recommend">
                            <div class="recommend-date">
                                <i class="fas fa-calendar-alt"></i>
                                <span id="currentDate"></span>
                            </div>
                            <ul class="song-list" id="dailyRecommendList">
                                <!-- 每日推荐歌曲列表 -->
                            </ul>
                        </div>
                    </div>

                    <!-- 排行榜视图 -->
                    <div id="rankingView" class="view">
                        <div class="view-header">
                            <h2>排行榜</h2>
                        </div>
                        <div class="ranking-tabs">
                            <button class="ranking-tab active" data-ranking="hot">热歌榜</button>
                            <button class="ranking-tab" data-ranking="new">新歌榜</button>
                            <button class="ranking-tab" data-ranking="original">说唱榜</button>
                        </div>
                        <div class="ranking-content">
                            <div id="rankingList" class="song-list">
                                <!-- 排行榜歌曲列表 -->
                            </div>
                        </div>
                    </div>

                    <!-- 搜索视图 -->
                    <div id="searchView" class="view">
                        <div class="view-header">
                            <h2>搜索音乐</h2>
                        </div>
                        <div class="search-content">
                            <div class="search-hot-words">
                                <h3>热门搜索</h3>
                                <div class="hot-words">
                                    <span class="hot-word">流行音乐</span>
                                    <span class="hot-word">摇滚</span>
                                    <span class="hot-word">电子音乐</span>
                                    <span class="hot-word">民谣</span>
                                    <span class="hot-word">古典音乐</span>
                                </div>
                            </div>
                            <div id="searchResults" class="search-results">
                                <!-- 搜索结果将在这里显示 -->
                            </div>
                        </div>
                    </div>

                    <!-- 我的收藏视图 -->
                    <div id="favoritesView" class="view">
                        <div class="view-header">
                            <h2>我的收藏</h2>
                            <!-- 移除播放全部按钮 -->
                        </div>
                        <div id="favoritesList" class="song-list">
                            <!-- 收藏的歌曲列表 -->
                        </div>
                    </div>

                    <!-- 歌单视图 -->
                    <div id="playlistsView" class="view">
                        <div class="view-header">
                            <h2>我的歌单</h2>
                            <button class="btn-create-new-playlist">
                                <i class="fas fa-plus"></i>
                                创建新歌单
                            </button>
                        </div>
                        <div id="userPlaylistsGrid" class="playlist-grid">
                            <!-- 用户歌单网格 -->
                        </div>
                    </div>

                    <!-- 播放历史视图 -->
                    <div id="historyView" class="view">
                        <div class="view-header">
                            <h2>最近播放</h2>
                            <button class="btn-clear-history">
                                <i class="fas fa-trash"></i>
                                清空历史
                            </button>
                        </div>
                        <div id="historyList" class="song-list">
                            <!-- 播放历史列表 -->
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 底部播放控制栏 -->
        <div class="player-bar">
            <div class="player-info">
                <div class="song-cover">
                    <img id="currentSongCover" src="https://via.placeholder.com/60x60/333/fff?text=♪" alt="专辑封面">
                </div>
                <div class="song-details">
                    <div class="song-title" id="currentSongTitle">未播放</div>
                    <div class="song-artist" id="currentSongArtist">-</div>
                </div>
                <div class="song-actions">
                    <button class="btn-favorite" id="favoriteBtn" title="收藏">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
            </div>

            <div class="player-controls">
                <div class="control-buttons">
                    <button class="btn-shuffle" id="shuffleBtn" title="随机播放">
                        <i class="fas fa-random"></i>
                    </button>
                    <button class="btn-prev" id="prevBtn" title="上一首">
                        <i class="fas fa-step-backward"></i>
                    </button>
                    <button class="btn-play" id="playBtn" title="播放">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn-next" id="nextBtn" title="下一首">
                        <i class="fas fa-step-forward"></i>
                    </button>
                    <button class="btn-repeat" id="repeatBtn" title="循环播放">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
                <div class="progress-container">
                    <span class="time-current" id="currentTime">0:00</span>
                    <div class="progress-bar">
                        <div class="progress-track">
                            <div class="progress-fill" id="progressFill"></div>
                            <div class="progress-handle" id="progressHandle"></div>
                        </div>
                    </div>
                    <span class="time-total" id="totalTime">0:00</span>
                </div>
            </div>

            <div class="player-volume">
                <button class="btn-volume" id="volumeBtn" title="音量">
                    <i class="fas fa-volume-up"></i>
                </button>
                <div class="volume-slider">
                    <div class="volume-track">
                        <div class="volume-fill" id="volumeFill"></div>
                        <div class="volume-handle" id="volumeHandle"></div>
                    </div>
                </div>
                <button class="btn-playlist" id="playlistBtn" title="播放列表">
                    <i class="fas fa-list"></i>
                </button>
                <button class="btn-fullscreen" id="fullscreenBtn" title="全屏">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </div>

        <!-- 右侧播放列表 -->
        <div class="playlist-panel" id="playlistPanel">
            <div class="playlist-header">
                <h3>播放列表</h3>
                <button class="btn-close-playlist" onclick="player.closePlaylistPanel()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="playlist-content">
                <div class="playlist-actions">
                    <button class="btn-clear-playlist">清空列表</button>
                    <span class="playlist-count">共 <span id="playlistCount">0</span> 首</span>
                </div>
                <ul id="currentPlaylist" class="current-playlist">
                    <!-- 当前播放列表 -->
                </ul>
            </div>
        </div>

        <!-- 模态框 -->
        <div class="modal-overlay" id="modalOverlay">
            <!-- 创建歌单模态框 -->
            <div class="modal" id="createPlaylistModal">
                <div class="modal-header">
                    <h3>创建歌单</h3>
                    <button class="btn-close-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>歌单名称</label>
                        <input type="text" id="playlistNameInput" placeholder="请输入歌单名称">
                    </div>
                    <div class="form-group">
                        <label>歌单描述</label>
                        <textarea id="playlistDescInput" placeholder="请输入歌单描述（可选）"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-cancel">取消</button>
                    <button class="btn-confirm" id="confirmCreatePlaylist">创建</button>
                </div>
            </div>
        </div>

        <!-- 右键菜单 -->
        <div class="context-menu" id="contextMenu">
            <ul>
                <li data-action="play">
                    <i class="fas fa-play"></i>
                    播放
                </li>
                <li data-action="next">
                    <i class="fas fa-plus"></i>
                    下一首播放
                </li>
                <li data-action="favorite">
                    <i class="far fa-heart"></i>
                    收藏到我的音乐
                </li>
                <li data-action="playlist">
                    <i class="fas fa-list"></i>
                    添加到歌单
                </li>
                <li data-action="download">
                    <i class="fas fa-download"></i>
                    下载
                </li>
            </ul>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 