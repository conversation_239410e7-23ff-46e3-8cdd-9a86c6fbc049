-- API模块 - 处理与ShyBot音乐API的交互
API = {}

-- 调试开关
local DEBUG = false

-- 调试日志函数
local function debugPrint(...)
    -- 完全禁用调试输出
    return
end

-- 切换调试模式
function API.toggleDebug()
    DEBUG = not DEBUG
    -- 禁用状态输出
    -- print("^3[API] ^7调试模式: " .. (DEBUG and "已启用" or "已禁用"))
    return DEBUG
end

-- API配置
local API_CONFIG = {
    base_url = "https://shybot.top/v2/music/api/",
    api_key = "96b3597b323680ef20195a49706546d07090ad3a3d05cde5820f2578ad510332", -- 您的API密钥
    timeout = 30000, -- 请求超时时间(毫秒) - 增加到30秒
    max_retries = 3, -- 最大重试次数
    retry_delay = 2000, -- 初始重试延迟(毫秒) - 增加到2秒
    retry_backoff = 1.5 -- 重试延迟的指数增长因子
}

-- 支持的音乐平台（只保留稳定工作的平台）
API.PLATFORMS = {
    QQ = "qq",
    NETEASE = "wyy",
    XMLY = "xmla"
}

-- 完整的URL编码函数，支持中文字符
local function urlEncode(str)
    if type(str) ~= "string" then
        return tostring(str)
    end
    
    -- 将字符串转换为字节数组并编码
    local encoded = ""
    for i = 1, #str do
        local char = string.sub(str, i, i)
        local byte = string.byte(char)
        
        -- 检查是否是安全字符（字母、数字、-、_、.、~）
        if (byte >= 48 and byte <= 57) or    -- 0-9
           (byte >= 65 and byte <= 90) or    -- A-Z
           (byte >= 97 and byte <= 122) or   -- a-z
           byte == 45 or byte == 95 or       -- - _
           byte == 46 or byte == 126 then    -- . ~
            encoded = encoded .. char
        else
            -- 其他字符进行百分号编码
            encoded = encoded .. string.format("%%%02X", byte)
        end
    end
    
    return encoded
end

-- 构建API请求URL
function API.buildUrl(params)
    local url = API_CONFIG.base_url
    
    -- 添加API密钥
    url = url .. "?shykey=" .. API_CONFIG.api_key
    
    -- 添加其他参数
    for key, value in pairs(params) do
        -- 使用完整的URL编码
        local encodedValue = urlEncode(tostring(value))
        url = url .. "&" .. key .. "=" .. encodedValue
    end
    
    debugPrint("构建的URL: " .. url:gsub("shykey=[^&]+", "shykey=***"))
    return url
end

-- 发送API请求，带重试机制
function API.request(params, callback, retryCount)
    retryCount = retryCount or 0
    local url = API.buildUrl(params)
    
    -- 输出请求信息
    if retryCount == 0 then
        -- 禁用请求输出
    else
        -- 禁用重试输出
        -- print("^3[音乐API] 重试请求 (" .. retryCount .. "/" .. API_CONFIG.max_retries .. "): " .. url:gsub("shykey=[^&]+", "shykey=***"))
    end
    
    -- 设置请求开始时间
    local startTime = GetGameTimer()
    
    -- 设置超时检测
    local timeoutId = nil
    timeoutId = SetTimeout(API_CONFIG.timeout, function()
        -- 检查请求是否仍在进行中（尚未收到回调）
        if timeoutId then
            timeoutId = nil
            -- 禁用超时输出
            -- print("^1[音乐API] 请求超时 (" .. API_CONFIG.timeout .. "ms)")
            
            -- 如果未达到最大重试次数，则重试
            if retryCount < API_CONFIG.max_retries then
                local delay = API_CONFIG.retry_delay * (API_CONFIG.retry_backoff ^ retryCount)
                -- 禁用重试输出
                -- print("^3[音乐API] ^7将在 " .. delay .. "ms 后重试请求")
                
                SetTimeout(delay, function()
                    API.request(params, callback, retryCount + 1)
                end)
            else
                -- 禁用失败输出
                -- print("^1[音乐API] 达到最大重试次数，请求失败")
                if callback then
                    callback(nil, 408) -- 408是HTTP超时状态码
                end
            end
        end
    end)
    
    PerformHttpRequest(url, function(statusCode, responseData, headers)
        -- 清除超时检测
        if timeoutId then
            timeoutId = nil
        else
            -- 如果timeoutId已为nil，说明已经触发了超时处理，直接返回
            return
        end
        
        -- 计算请求耗时
        local endTime = GetGameTimer()
        local requestTime = endTime - startTime
        
        local response = nil
        
        if statusCode == 200 then
            -- 输出请求成功信息和耗时
            -- print("^2[音乐API] 请求成功: " .. statusCode .. ", 耗时: " .. requestTime .. "ms")
            
            -- 尝试解析JSON响应
            local success, result = pcall(function()
                return json.decode(responseData)
            end)
            
            if success then
                response = result
                
                -- 检查编码问题
                if type(response) == "table" and #response > 0 then
                    -- print("^2[音乐API] 成功获取 " .. #response .. " 条结果")
                    
                    for i, item in ipairs(response) do
                        -- 确保所有必要的字段都存在
                        if not item.name then item.name = "未知歌曲" end
                        if not item.singer then item.singer = "未知歌手" end
                        if not item.album then item.album = "未知专辑" end
                    end
                elseif type(response) == "table" and #response == 0 then
                    -- print("^3[音乐API] 请求成功但没有结果")
                end
            else
                -- 禁用JSON解析错误输出
                -- print("^1[音乐API] JSON解析错误: " .. tostring(responseData))
                
                -- 解析错误也尝试重试
                if retryCount < API_CONFIG.max_retries then
                    local delay = API_CONFIG.retry_delay * (API_CONFIG.retry_backoff ^ retryCount) -- 指数退避策略
                    -- 禁用重试输出
                    -- print("^3[音乐API] ^7将在 " .. delay .. "ms 后重试请求")
                    
                    SetTimeout(delay, function()
                        API.request(params, callback, retryCount + 1)
                    end)
                    return
                end
            end
        else
            -- 禁用请求失败输出
            -- print("^1[音乐API] 请求失败: " .. (statusCode or 0) .. ", 耗时: " .. requestTime .. "ms")
            
            if responseData and responseData ~= "" then
                -- 禁用错误信息输出
                -- print("^1[音乐API] 错误信息: " .. responseData)
            end
            
            if requestTime >= API_CONFIG.timeout then
                -- 禁用超时输出
                -- print("^1[音乐API] 请求超时")
            end
            
            -- 如果请求失败且未达到最大重试次数，则重试
            if retryCount < API_CONFIG.max_retries then
                local delay = API_CONFIG.retry_delay * (API_CONFIG.retry_backoff ^ retryCount) -- 指数退避策略
                -- 禁用重试输出
                -- print("^3[音乐API] ^7将在 " .. delay .. "ms 后重试请求")
                
                SetTimeout(delay, function()
                    API.request(params, callback, retryCount + 1)
                end)
                return
            else
                -- 禁用失败输出
                -- print("^1[音乐API] 达到最大重试次数，请求失败")
            end
        end
        
        if callback then
            callback(response, statusCode)
        end
    end, "GET", "", nil) -- 移除选项参数，使用默认值
end

-- 获取可用平台列表
function API.getAvailablePlatforms()
    local platforms = {}
    for name, _ in pairs(API.PLATFORMS) do
        table.insert(platforms, name)
    end
    return platforms
end

-- 搜索音乐
function API.searchMusic(platform, keyword, callback, limit)
    -- 确保平台参数是字符串并转换为大写
    if type(platform) == "string" then
        platform = platform:upper()
    end
    
    -- 输出更详细的调试信息，特别是对排行榜请求
    -- print("^2[音乐API] 搜索请求 - 平台: " .. tostring(platform) .. ", 关键词: " .. tostring(keyword) .. ", 最大结果数: " .. tostring(limit or 20))
    -- if keyword == "热歌榜" or keyword == "新歌榜" or keyword == "说唱榜" then
    --     print("^2[音乐API] 检测到排行榜请求: " .. keyword)
    -- end
    
    if not platform or not API.PLATFORMS[platform] then
        print("^1[音乐API] 无效的平台: " .. tostring(platform) .. "，可用平台: " .. table.concat(API.getAvailablePlatforms(), ", "))
        if callback then
            callback(nil, 400)
        end
        return
    end
    
    if not keyword or keyword == "" then
        print("^1[音乐API] 搜索关键词不能为空")
        if callback then
            callback(nil, 400)
        end
        return
    end
    
    -- 设置默认最大结果数量为20
    limit = limit or 20
    
    -- 构建标准搜索参数
    local params = {
        type = API.PLATFORMS[platform:upper()],
        name = keyword,
        limit = tostring(limit)
    }
    
    -- 对排行榜可能需要特殊处理，但仍使用标准参数格式
    API.request(params, function(response, statusCode)
        -- 添加更详细的输出，特别是排行榜响应
        if statusCode == 200 then
            if response and type(response) == "table" then
                -- print("^2[音乐API] 搜索成功: " .. keyword .. ", 结果数量: " .. #response)
            else
                print("^1[音乐API] 搜索成功但结果为空或无效: " .. keyword)
            end
        else
            print("^1[音乐API] 搜索失败: " .. keyword .. ", 状态码: " .. tostring(statusCode))
        end
        
        if callback then
            callback(response, statusCode)
        end
    end)
end

-- 获取歌曲详情
function API.getSongDetail(platform, songId, callback)
    -- 确保平台参数是字符串并转换为大写
    if type(platform) == "string" then
        platform = platform:upper()
    end
    
    if not platform or not API.PLATFORMS[platform] then
        print("^1[音乐API] 无效的平台: " .. tostring(platform) .. "，可用平台: " .. table.concat(API.getAvailablePlatforms(), ", "))
        if callback then
            callback(nil, 400)
        end
        return
    end
    
    if not songId or songId == "" then
        print("^1[音乐API] 歌曲ID不能为空")
        if callback then
            callback(nil, 400)
        end
        return
    end
    
    -- 构建请求参数
    local params = {
        type = API.PLATFORMS[platform] .. "_song",
        id = songId
    }
    
    -- 发送请求
    API.request(params, function(response, statusCode)
        if statusCode == 200 and response then
            -- 确保返回的是单个歌曲对象
            if type(response) == "table" then
                -- 添加平台信息
                response.platform = platform
                
                -- 确保所有必要的字段都存在
                if not response.name then response.name = "未知歌曲" end
                if not response.singer then response.singer = "未知歌手" end
                if not response.album then response.album = "未知专辑" end
                
                if callback then
                    callback(response, statusCode)
                end
            else
                print("^1[音乐API] 获取歌曲详情返回的不是表格类型: " .. type(response))
                if callback then
                    callback(nil, 500)
                end
            end
        else
            print("^1[音乐API] 获取歌曲详情失败: " .. (statusCode or 0))
            if callback then
                callback(nil, statusCode or 500)
            end
        end
    end)
end

-- 获取歌曲URL
function API.getSongUrl(platform, songId, callback)
    -- 确保平台参数是字符串并转换为大写
    if type(platform) == "string" then
        platform = platform:upper()
    end
    
    if not platform or not API.PLATFORMS[platform] then
        print("^1[音乐API] 无效的平台: " .. tostring(platform) .. "，可用平台: " .. table.concat(API.getAvailablePlatforms(), ", "))
        if callback then
            callback(nil)
        end
        return nil
    end
    
    if not songId or songId == "" then
        print("^1[音乐API] 歌曲ID不能为空")
        if callback then
            callback(nil)
        end
        return nil
    end
    
    print("^2[音乐API] 获取歌曲URL - 平台: " .. platform .. ", ID: " .. songId)
    
    -- 构建请求参数 - 使用_url后缀表示获取URL
    local params = {
        type = API.PLATFORMS[platform] .. "_url",
        id = songId
    }
    
    -- 构建URL
    local url = API.buildUrl(params)
    print("^2[音乐API] 生成的URL: " .. url:gsub("shykey=[^&]+", "shykey=***"))
    
    -- 处理无回调的情况（同步调用）
    if not callback then
        -- 对于同步调用，直接返回完整的URL
        return url
    else
        -- 异步调用，通过回调返回结果
        callback({url = url})
        return url
    end
end 